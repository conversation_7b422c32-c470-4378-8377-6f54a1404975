== Base ==
Base

== ChangedResultFile ==
Do you want to save the result file?

== ChangedDestinationFile ==
Do you want to save the destination file?

== CloseButton ==
Close

== ConfiguredStatus ==
configured!

== NotConfigureStatus ==
not configured

== Destination ==
Destination

== ErrorAutomaticOptionNeeded ==
In silent mode the automatic merge option is needed

== ErrorComparisonMethod ==
Invalid comparison method

== ErrorConflictCanNotBeApplied ==
The conflict resolution cannot be applied because it depends on other conflict's resolution. Please try to resolve conflicts in a different order. The conflict resolution returned the following error: {0}

== ErrorContributorsMustBeSpecified ==
The contributors must be specified

== ErrorEncoding ==
Specified encoding cannot be identified ({0}).

== ErrorExecutingExternalDiffTool ==
An error occurred when executing external diff tool:

== ErrorExecutingExternalMergeTool ==
An error occurred when executing external merge tool:

== ErrorExecutingExternal2WayMergeTool ==
An error occurred when executing external 2-way merge tool:

== ErrorFileTitleType ==
Oops! We do not support your language... yet!

== ErrorFileType ==
SemanticMerge received the following files: {0}.
The file extensions are not recognized as supported languages.
You can run your default text-based {1} tool.

Note: in case you know these files contain a supported language check how to use the -l param in SemanticMerge.

== ErrorJVMNotAvailableTitle ==
Oops! JVM not found - required to parse your files

== ErrorJVMNotAvailable ==
SemanticMerge requires a valid JVM installation available
in order to parse your {0} files.

You can run your default text-based {1} tool instead.

== ErrorNumberOfContributorsDoesNotMatch ==
The number of contributors does not match

== ErrorTabSpaces ==
Invalid tab spaces

== ErrorVersionControl ==
Invalid version control to configure

== OpenDialogFilterForExecutableFile ==
Executable Files (*.exe)|*.exe

== OpenDialogTitleForConfigFile ==
Browse for config file

== OpenDialogTitleForExecutableFile ==
Browse for executable file

== PlasticSCMConfigurationNotification ==
Changes won't take effect until you reopen the Unity VCS GUI

== RestartMergeWithConfigChangesMessage ==
In order to apply the new saved configuration, the merge should be restarted.

== RestartDiffWithConfigChangesMessage ==
In order to apply the new saved configuration, the diff should be restarted.

== RestartMergeQuestion ==
You have already resolved {0} conflict(s) that will be lost. Are you sure you want to restart the merge?

== ResultNeeded ==
A result file path must be specified

== Source ==
Source

== UnsolvedPendingConflicts ==
The file still has {0} unsolved conflicts.

== UnexpectedError ==
An unexpected error has occurred.

== UsageDescription ==
Please read below the usage of the tool

== UsageAvalonia ==
Usage: {0} semanticmerge [<mergeOptions>]

      mergeOptions: <files> [-a|--automatic] [--silent] [--merge-decl] [--process-all-merges]
                    [--include-format-change] [--log-external-parser]
                    [<contributorSymbolicNames>] [<generalOptions>] [<contributorExtendedInfo>]

      files: {{<sortedFiles> | <unsortedFiles>}}
            sortedFiles: <filename1> <filename2> <filename3> <filename4> <fileparamorder>
                  fileparamorder:    {{-fpo | --fileparamorder}}=<list of keys separated by a semicolon>
                                     The default value is --fileparamorder=s;d;b;r

            unsortedFiles: <sourceFile> <destinationFile> <baseFile> <resultFile>
                  sourceFile:        {{-s | --source}}=<filename>
                  destinationFile:   {{-d | --destination}}=<filename>
                  baseFile:          {{-b | --base}}=<filename>
                  resultFile:        {{-r | --result}}=<filename>

      -a | --automatic:        Automatically merge without user interaction unless conflicts are found.

      --silent:                This option must be used combined with the --automatic option.
                               When a merge can't be solved automatically, this option causes the tool to return immediately
                               with a non-zero exit code (no semanticmerge tool is shown).
                               If the tool was able to solve the merge automatically, the program returns exit code 0.

      --merge-decl:            Merge automatically as many declarations* as possible.

      --process-all-merges:    Merge automatically as many declarations* as possible and
                               run the external text based tool for each non-automatic merge declaration.

        *A declaration is the statement that defines any of the supported syntax elements: classes, methods, attributes, etc.
         Depending on the element type (e.g. classes, methods), they include a body where the element is implemented.

      --include-format-change: Include changes where only indentation and EOLs have been modified.
                               This includes differences in white spaces and tabs at the beginning of lines and
                               differences in EOLs. It also includes white lines at the beginning of declarations.
                               By default all these differences are ignored to simplify the merge/diff.

      --log-external-parser:   Enable logging the file contents to debug external parsers.

      contributorSymbolicNames: <srcSymbolicName> <dstSymbolicName> <baseSymbolicName>
            srcSymbolicName:     {{-sn | --srcsymbolicname}}=<symbolicname>
            dstSymbolicName:     {{-dn | --dstsymbolicname}}=<symbolicname>
            baseSymbolicName:    {{-bn | --basesymbolicname}}=<symbolicname>


      generalOptions: [<defaultEncoding>] [<resultEncoding>] [<fileLanguage>] [<virtualMachine>]
                      [<externalMergeTool>] [<external2WayMerge>] [<externalDiffTool>]
                      [<tabSpaces>] [<extrainfofile>] [<progress>]
            defaultEncoding:     {{-e | --defaultencoding}}=<encoding>
            resultEncoding:      {{-re | --resultencoding}}=<encoding>
            encoding:            {{ascii | ansi | unicode | bigendian | utf7 | utf8 | utf8bom | utf32be | utf32le}}
            fileLanguage:        {{-l | --fileslanguage}}={{csharp | vb | java | cplusplus | php}}
            virtualMachine:      {{-vm | --virtualmachine}}=<path to the Java Virtual Machine executable>
            tabSpaces:           {{-ts   | --tabspaces}}={{4 | <user_defined>}}
            extrainfofile:       {{-ei   | --extrainfofile}}={{path to a file containing extra merge information}}
            progress:            {{-pg   | --progress}}={{string representing the progress in the whole merge process}}


      contributorExtendedInfo: <contributorOwnerName> <contributorBranchName>
                               <contributorChangeset> <contributorComment>

      contributorOwnerName: <srcOwnerName> <dstOwnerName> <baseOwnerName>
        srcOwnerName:        {{-so | --srcowner}}=<ownername>
        dstOwnerName:        {{-do | --dstowner}}=<ownername>
        baseOwnerName:       {{-bo | --baseowner}}=<ownername>

      contributorBranchName: <srcBranchName>; <dstBranchName> <baseBranchName>
        srcBranchName:       {{-sb | --srcbranch}}=<name>
        dstBranchName:       {{-db | --dstbranch}}=<branchname>
        baseBranchName:      {{-bb | --basebranch}}=<branchname>

      contributorChangeset: <srcChangeset> <dstChangeset> <baseChangeset>
        srcChangeset:        {{-sc | --srccset}}=<changeset>
        dstChangeset:        {{-dc | --dstcset}}=<changeset>
        baseChangeset:       {{-bc | --basecset}}=<changeset>

      contributorComment: <srcComment> <dstComment> <baseComment>
        srcComment:          {{-sm | --srccomment}}=<changeset>
        dstComment:          {{-dm | --dstcomment}}=<changeset>
        baseComment:         {{-bm | --basecomment}}=<changeset>


      Examples:

        {0} semanticmerge -b=base.cs -d=dst.cs -s=src.cs -r=result.cs

        {0} semanticmerge base.cs dst.cs src.cs result.cs --fileparamorder=b;d;s;r

== Usage ==
      Usage: semanticmerge [<configureversioncontrol> | <mergeOptions>]

      configureversioncontrol:       {--configure-version-control}=<versioncontrolkey>
            versioncontrolkey:       {plasticscm | git}

      mergeOptions: <files> [-a|--automatic] [--silent] [--merge-decl] [--process-all-merges]
                    [--nolangwarn] [--nostructurewarn] [--include-format-change] [--log-external-parser]
                    [<contributorSymbolicNames>] [<generalOptions>]
                    [<contributorExtendedInfo>]

      files: {<sortedFiles> | <unsortedFiles>}
            sortedFiles: <filename1> <filename2> <filename3> <filename4> <fileparamorder>
                  fileparamorder:    {-fpo | --fileparamorder}=<list of keys separated by a semicolon>
                                     The default value is --fileparamorder=s;d;b;r

            unsortedFiles: <sourceFile> <destinationFile> <baseFile> <resultFile>
                  sourceFile:        {-s | --source}=<filename>
                  destinationFile:   {-d | --destination}=<filename>
                  baseFile:          {-b | --base}=<filename>
                  resultFile:        {-r | --result}=<filename>

      -a | --automatic:        Automatically merge without user interaction unless conflicts are found.

      --silent:                This option must be used combined with the --automatic option.
                               When a merge can't be solved automatically, this option causes the tool to return immediately
                               with a non-zero exit code (no semanticmerge tool is shown).
                               If the tool was able to solve the merge automatically, the program returns exit code 0.

      --merge-decl:            Merge automatically as many declarations* as possible.

      --process-all-merges:    Merge automatically as many declarations* as possible and
                               run the external text based tool for each non-automatic merge declaration.

        *A declaration is the statement that defines any of the supported syntax elements: classes, methods, attributes, etc.
         Depending on the element type (e.g. classes, methods), they include a body where the element is implemented.

      --nolangwarn:            Run the external text based tool directly when the language is not supported.

      --nostructurewarn:       Run the external text-based tool directly if structure errors are found.

      --include-format-change: Include changes where only indentation and EOLs have been modified.
                               This includes differences in white spaces and tabs at the beginning of lines and
                               differences in EOLs. It also includes white lines at the beginning of declarations.
                               By default all these differences are ignored to simplify the merge/diff.

      --log-external-parser:   Enable logging the file contents to debug external parsers.

      contributorSymbolicNames: <srcSymbolicName> <dstSymbolicName> <baseSymbolicName>
            srcSymbolicName:     {-sn | --srcsymbolicname}=<symbolicname>
            dstSymbolicName:     {-dn | --dstsymbolicname}=<symbolicname>
            baseSymbolicName:    {-bn | --basesymbolicname}=<symbolicname>

      generalOptions: [<defaultEncoding>] [<resultEncoding>] [<fileLanguage>] [<virtualMachine>]
                      [<externalMergeTool>] [<external2WayMerge>] [<externalDiffTool>]
                      [<tabSpaces>] [<extrainfofile>] [<progress>]
            defaultEncoding:     {-e | --defaultencoding}=<encoding>
            resultEncoding:      {-re | --resultencoding}=<encoding>
                  encoding:      {ascii | ansi | unicode | bigendian | utf7 | utf8 | utf8bom | utf32be | utf32le}
            fileLanguage:        {-l | --fileslanguage}={csharp | vb | java | cplusplus | php}
            virtualMachine:      {-vm | --virtualmachine}=<path to the Java Virtual Machine executable>
            externalParser:      {-ep | --externalparser}=<command to execute the external parser>
            externalMergeTool:   {-emt  | --externalmergetool}={default | <user_defined_tool>}
            external2WayMerge:   {-e2mt | --external2waymergetool}={ <user_defined_tool>}
            externalDiffTool:    {-edt  | --externalDiffTool}={default | <user_defined_tool>}
            tabSpaces:           {-ts   | --tabspaces}={4 | <user_defined>}
            extrainfofile:       {-ei   | --extrainfofile}={path to a file containing extra merge information}
            progress:            {-pg   | --progress}={string representing the progress in the whole merge process}

      contributorExtendedInfo: <contributorOwnerName> <contributorBranchName>
                               <contributorChangeset> <contributorComment>

      contributorOwnerName: <srcOwnerName> <dstOwnerName> <baseOwnerName>
        srcOwnerName:        {-so | --srcowner}=<ownername>
        dstOwnerName:        {-do | --dstowner}=<ownername>
        baseOwnerName:       {-bo | --baseowner}=<ownername>

      contributorBranchName: <srcBranchName>; <dstBranchName> <baseBranchName>
        srcBranchName:       {-sb | --srcbranch}=<name>
        dstBranchName:       {-db | --dstbranch}=<branchname>
        baseBranchName:      {-bb | --basebranch}=<branchname>

      contributorChangeset: <srcChangeset> <dstChangeset> <baseChangeset>
        srcChangeset:        {-sc | --srccset}=<changeset>
        dstChangeset:        {-dc | --dstcset}=<changeset>
        baseChangeset:       {-bc | --basecset}=<changeset>

      contributorComment: <srcComment> <dstComment> <baseComment>
        srcComment:          {-sm | --srccomment}=<changeset>
        dstComment:          {-dm | --dstcomment}=<changeset>
        baseComment:         {-bm | --basecomment}=<changeset>


      Examples:

        semanticmergetool -b=base.cs -d=dst.cs -s=src.cs -r=result.cs

        semanticmergetool base.cs dst.cs src.cs result.cs --fileparamorder=b;d;s;r

        semanticmergetool -b=base.cs -d=dst.cs -s=src.cs -r=result.cs
          -e2mt=""kdiff3.exe #sourcefile #destinationfile -o #output""

        semanticmergetool -b=base.cs -d=dst.cs -s=src.cs -r=result.cs
          -emt=""kdiff3.exe #basefile #sourcefile #destinationfile -o #output""

== UsageCaption ==
SemanticMerge Tool usage

== WarningNoExternalDiffTool ==
No external diff tool has been specified in order to proceed with the diff.

== WarningNoExternal2MergeTool ==
No external 2-way merge tool has been specified in order to merge the contents of the selected conflict.

== WarningNoExternalMergeTool ==
No external merge tool has been specified in order to proceed with the merge.

== WarningExternalToolNotFound ==
Tool not found, please introduce a valid one

== WarningSemanticToolNotFound ==
SemanticMerge tool not found, please introduce a valid one

== WarningConfigFileNotFound ==
Config file not found, please introduce a valid one

== WarningInvalidJavaVirtualMachinePath ==
Java Virtual Machine path not found

== WarningInvalidTabSize ==
Invalid tab size

== RenameWindowTitle ==
Renaming source

== RenameWindowLabel ==
New name for {0}:

== RenameButton ==
Rename

== CancelButton ==
_Cancel

== PendingConflictsToSolve ==
{0}/{1}  -  Conflicts to solve: {2}

== NextConflictButtonTooltip ==
Next conflict (Ctrl+PagDown)

== PreviousConflictButtonTooltip ==
Previous conflict (Ctrl+PageUp)

== NextConflictButtonTooltipPrefix ==
Next conflict ({0})

== PreviousConflictButtonTooltipPrefix ==
Previous conflict ({0})

== SaveAndExitExplanation ==
save the result file and exit the tool

== SaveAndExitButton ==
_Save & Exit

== ExitWithoutSavingButton ==
_Exit without saving

== MergeWaitingAnimation ==
Calculating conflicts. Please wait...

== SyncDeclarationMenuItem ==
Sync Declaration

== OptionsButton ==
Options

== FindMenuItem ==
Find...

== OptionsMenuItem ==
Options

== ConfigureFontsAndColorsMenuItem ==
Configure fonts and colors...

== DisplayInfoAboutThisMerge ==
Display info about this merge

== LeftEncodingMenuItem ==
Left encoding

== RightEncodingMenuItem ==
Right encoding

== SkipFormatChangesMenuItem ==
Skip format changes

== ReformatSourceCode ==
Reformat source code

== ConfigurationMenuItem ==
Configuration...

== GetLicense ==
Get license

== ShowUserGuideMenuItem ==
User guide

== UserGuideURL ==
https://semanticmerge.com/documentation

== ShowIntroGuideMenuItem ==
Show intro guide

== OpenSamplesDirectoryMenuItem ==
Open samples

== FollowSemanticmergeMenuItem ==
Follow @semanticmerge

== AboutSemanticmergeMenuItem ==
About

== VisualDiffWindowTitle ==
Visual Diff

== VisualDiffExplanation ==
Shows a graphic that explains the diff

== VisualDiffOptionExplanation ==
Select two declarations and click diff in order to see the differences

== VisualDiffControlExplanation ==
Use the control key with the scroll wheel to zoom in and zoom out

== ZoomInExplanation ==
Zoom in on the graphic; you can also use the control key with the scroll wheel to zoom in

== ZoomOutExplanation ==
Zoom out on the graphic; you can also use the control key with the scroll wheel to zoom out

== VisualMergeWindowTitle ==
Visual Merge

== VisualMerge ==
_Visual Merge

== VisualMergeExplanation ==
Shows a graphic that explains the merge

== RunTextMerge ==
Run _text merge

== RunTextMergeExplanation ==
Runs the traditional, text based, merge tool

== RestartMerge ==
R_estart merge

== RestartMergeExplanation ==
Discards all the changes and restarts the merge operation

== ProcessAllMerges ==
Process all conflicts

== ProcessAllMergesExplanation ==
Merges automatically as many declarations as possible and runs the external text based tool for each non-automatic merge declaration

== VisualDiffButton ==
_Visual diff

== RestartDiffButton ==
_Restart diff

== RestartDiffExplanation ==
Restarts the diff operation

== RunTextDiffButton ==
Run _text diff

== RunTextDiffExplanation ==
Runs the traditional, text based, diff tool

== OutlinePanelVisualDiffButton ==
_Visual Diff

== OutlinePanelRunTextDiffButton ==
Run _Text Diff

== DivergentMoveDetails ==
Divergent Move Details

== ShowMergeInfoExplanation ==
Displays the Merge information

== MergeInfoResultFile ==
Result file:

== MergeInfoLanguage ==
Language:

== MergeInfoBaseFile ==
Base file

== MergeInfoSourceFile ==
Source file

== MergeInfoDestinationFile ==
Destination file

== MergeInfoFileName ==
Name:

== MergeInfoFilePath ==
Path:

== MergeInfoExtraInfo ==
Extra info

== SemanticOutline ==
Semantic Outline

== ExpandSemanticOutlineTooltip ==
Expand Semantic Outline

== CollapseSemanticOutlineTooltip ==
Collapse Semantic Outline

== PendingConflicts ==
Conflicts to solve ({0})

== PendingConflictsExplanation ==
Shows the conflicts that need to be solved manually: happens when the same element has been modified both in 'their changes' and 'your changes'

== SelectTwoDeclarationsToSeeDifferences ==
Please, select two declarations in order to see the differences.

== DiffExplanation ==
Shows the diffs of this element

== SourceDifferences ==
Src - their changes ({0})

== SourceDifferencesExplanation ==
the changes made in the code you're merging from (theirs). Shows the differences between the base and the source contributor

== DestinationDifferences ==
Dst - your changes ({0})

== DestinationDifferencesExplanation ==
the changes on your working copy (yours). Shows the differences between the base and the destination contributor

== NoConflictMessageText ==
There are no conflicts to check.

== NoConflictMessageDetail ==
It means that the tool can solve the merge without user intervention.

Probably you're seeing this because you want to review the merge anyway.

But in case you prefer to automate the conflict resolution as much as possible: add the \"-a\" argument to the semanticmerge commandline.

== MaximizeButtonLeftTooltip ==
Maximize left panel

== RestoreButtonLeftTooltip ==
Restore left panel

== MaximizeButtonSrcTooltip ==
Maximize source panel

== RestoreButtonSrcTooltip ==
Restore source panel

== MaximizeButtonDstTooltip ==
Maximize destination panel

== RestoreButtonDstTooltip ==
Restore destination panel

== MaximizeButtonBaseTooltip ==
Maximize base panel

== RestoreButtonBaseTooltip ==
Restore base panel

== MaximizeButtonResultTooltip ==
Maximize result panel

== RestoreButtonResultTooltip ==
Restore result panel

== CopyToClipboard ==
Copy to clipboard

== BackButtonTooltip ==
Back

== FindMatchesButton ==
Find _matches

== FindMatchesButtonNoUnderscore ==
Find matches

== DiffMatchButton ==
_Diff

== DiffMatchButtonNoUnderscore ==
Diff

== MatchButton ==
_Match

== MatchButtonNoUnderscore ==
Match

== UnMatchButton ==
_Unmatch

== UnMatchButtonNoUnderscore ==
Unmatch

== SelectElementToMatch ==
Select an element to match

== EditResultExplanation ==
You cannot edit the result file until all conflicts are resolved. There are {0} conflict(s) remaining.

== KeepSourceExplanation ==
Keeps the source changes

== KeepDestinationExplanation ==
Keeps the destination changes

== KeepBothExplanation ==
Keeps both changes

== RenameExplanation ==
Renames the destination

== MergeExplanation ==
Runs the external text merge tool to solve this conflict

== TwoWayMergeExplanation ==
Launches the external 2 way mergetool to solve this conflict

== DisplayConfigurationWindowExplanation ==
Shows the configuration window

== DisplayAboutWindowExplanation ==
Shows the about window

== HideUnchangedSelectionExplanation ==
Show only changed declarations

== ShowUnchangedSelectionExplanation ==
Show the complete tree files, so you can check every declaration

== GroupUnchangedSelectionExplanation ==
Show changed declarations grouping the rest of the declarations into an unchanged one

== ExplainMoveMoveExplanation ==
Explains how the element was moved to different locations on source and destination contributors

== SourceFileExplanation ==
Source contributor file

== BaseFileExplanation ==
Base file, the ancestor of the source and destination files used during merge

== DestinationFileExplanation ==
Destination contributor file

== FindMatchesExplanation ==
Opens the matches window for matching an adedd with a deleted

== UnmatchExplanation ==
Unmatch - in case methods were wrongly matched between base and this contributor, you can unmatch, convert them to added/deleted and correct the match manually

== MatchExplanation ==
Applies the match to the selected item

== InvalidEmail ==
Invalid email

== InvalidReport ==
Invalid report

== EnterValidEmailAndReport ==
Please enter your email address, the subject and the comments.

== EnterValidEmailAddress ==
Please enter a valid email address.

== ErrorReadingSampleTitle ==
Error reading sample

== ErrorReadingSampleDescription ==
Cannot parse sample from file {0}: {1}

== SamplesWindowExplanation ==
For every sample case, we recommend you run the traditional merge tool and compare it with SemanticMerge

== ShowIntroGuideButton ==
Show intro _guide

== RunTextMergetoolButton ==
Text

== RunSemanticMergetoolButton ==
Semantic

== WelcomeSemantic20 ==
Welcome to SemanticMerge 2.0

== WelcomeExplanation1 ==
Semantic is a different kind of merge tool. It parses the code before calculating the merge, which makes it possible to detect and automatically solve most conflicts.

== WelcomeExplanation2 ==
It is different than most merge tools you have used so far, so it is worth investing a few minutes browsing the sample cases to become a real merge master and get the best out of the tool.

== ExploreTheSamplesButton ==
_Explore the samples

== ReadTheIntroGuideLinkText1 ==
To learn more about the SemanticMerge fundamentals

== ReadTheIntroGuideLinkText2 ==
Read the Intro Guide

== ReadTheIntroGuideLinkText3 ==
 (3 minutes read).

== DontShowThisDialogOnStartup ==
Don't _show this dialog on start up

== RunTheToolButton ==
Run the _tool

== ParsingErrorsLabel ==
Some issues were found while processing these files.

== ShowParsingErrorsButton ==
Show parsing errors

== ReleaseNotesBaseUrl ==
https://www.plasticscm.com/download/releasenotes

== MultifileCurrentFile ==
File {0} of {1}:

== MultifileCurrentFileExplanation ==
This is the current file. To change file use the conflict navigation buttons and also the multi-file moves.

== Ready ==
Ready

== UnparsedFilesError ==
Some of the files couldn't be parsed.
Please continue with the external tool.

== UnrebuiltFilesError ==
Some of the files couldn't have their structure properly recognized.
Please continue with the external tool.

== ParsingErrorsDescription ==
The following parsing errors have been found, the trees could be inconsistent and proceeding with the merge could result in file corruption:

== ConfirmRestartMergeTitle ==
Confirm restarting merge

== AndText ==
and

== Automatic ==
Automatic

== AutomaticallySolvedConflictSemanticMerge ==
Automatically solved using {0}

== ManuallySolvedConflictSemanticMerge ==
Manually solved using {0}

== SolvedConflictSemanticMerge ==
Solved using {0}

== KeepSourceActionText ==
Keep src

== KeepDestinationActionText ==
Keep dst

== KeepBothActionText ==
Keep both

== RenameActionText ==
Rename

== MergeActionText ==
Merge

== TwoWayMergeActionText ==
2 merge

== ChangePositionActionText ==
Change position

== DiffConflictButton ==
Diff

== KeepDstChangesButton ==
Keep destination

== KeepSrcChangesButton ==
Keep source

== MergeButton ==
Merge

== TwoWayMergeButton ==
2-way

== ExplainMoveButton ==
Explain move

== ViewConflictOnResultButton ==
View on result

== ViewMoveSrcOnResultButton ==
Source on result

== ViewMoveDstOnResultButton ==
Destination on result

== ChangePositionButton ==
Change position

== AddedDifferenceName ==
Added

== MovedDifferenceName ==
Moved

== DeletedDifferenceName ==
Deleted

== ChangedDifferenceName ==
Changed

== RenamedTo ==
Renamed to {0}

== MovedTo ==
Moved to {0}

== MovedFrom ==
Moved from {0}

== MovedFromOtherElement ==
other element

== MovedFromFirstLevel ==
first level

== MovedPosition ==
Moved {0} position

== MovedPositions ==
Moved {0} positions

== MoveDown ==
down

== MoveUp ==
up

== XAndY ==
{0} and {1}

== HideUnchanged ==
Hide unchanged

== HideUnchangedTooltip ==
Show changed declarations only

== ShowUnchanged ==
Show unchanged

== ShowUnchangedTooltip ==
Show the complete file trees, so you can check every declaration

== GroupUnchanged ==
Group unchanged

== GroupUnchangedTooltip ==
Show changed declarations grouping the rest of the declarations into an single, unchanged one

== DiffButton ==
Diff

== ZoomIn ==
Zoom in

== ZoomInTooltip ==
Zoom in on the graphic - you can also use the scroll wheel while holding the control key pressed

== ZoomOut ==
Zoom out

== ZoomOutTooltip ==
Zoom out on the graphic - you can also use the scroll wheel while holding the control key pressed

== BaseLabel ==
Base

== SrcLabel ==
Source (their changes)

== DstLabel ==
Destination (your changes)

== BaseTooltip ==
The common ancestor, it is the parent version of the two files you are merging. It shows how the file was originally

== SrcTooltip ==
The changes made in the code you're merging from (theirs). Shows the differences between the base and the source contributor

== DstTooltip ==
The changes on your working copy (yours). Shows the differences between the base and the destination contributor

== NotResolved ==
Not resolved

== ChangeEditorFont ==
Change editor font...

== Options ==
Options

== EditorOptions ==
Editor options

== ConfigurationDialogHeader ==
Configuration

== ConfigurationDialogTitle ==
Semantic Merge - Configuration

== AutomaticCheckBox ==
Automatically merge without user interaction unless conflicts are found (-a)

== MergeDeclCheckBox ==
Merge declaration conflicts that can be solved automatically (--merge-decl)

== IncludeFormatChangeCheckBox ==
Don't ignore indentation and EOL changes (--include-format-change)

== ProcessAllMergesCheckBox ==
Launch the external tool to process unsolved conflicts (--process-all-merges)

== NoLangWarnCheckBox ==
Run the external tool when the language is not supported (--nolangwarn)

== NoStructureWarnCheckBox ==
Run the external tool when structure errors are found (--nostructurewarn)

== NoJvmWarnCheckBox ==
Run the external tool when no JVM is available (Java and C++) (--nojvmwarn)

== JvmPathLabel ==
Path to the Java Virtual Machine

== ExternalParserLabel ==
Command to run the external parser

== ConfigurationDialogExplanation ==
Sets up various options for interacting with and resolving conflicts with semantic merge

== BrowseJvmPath ==
Select path to JVM

== ExternalToolsAreaTitle ==
External Tools

== PathNotFound ==
Path not found
