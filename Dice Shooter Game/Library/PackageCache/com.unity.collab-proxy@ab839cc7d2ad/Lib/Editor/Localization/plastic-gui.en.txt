== Attribute ==
Attribute

== Attributes ==
Attributes

== Object ==
Object

== Objects ==
Objects

== Item ==
Item

== Items ==
Items

== File ==
File

== Directories ==
Directories

== Branch ==
Branch

== Branches ==
Branches

== Changeset ==
Changeset

== Label ==
Label

== Labels ==
Labels

== CodeReview ==
Code review

== CodeReviews ==
Code reviews

== Server ==
Server

== Organization ==
Organization

== Repository ==
Repository

== Checkin ==
Check in

== CheckinWithAccKey ==
Check _in

== UndoChanges ==
Undo

== UndoChangesWithAccKey ==
_Undo

== UndoCheckoutsKeepingChanges ==
Undo checkouts keeping changes

== UpdateWorkspace ==
Update workspace

== ItemsMenuUndoCheckout ==
Undo checkout

== ItemsMenuUndoChanges ==
Undo changes

== ItemsMenuChangeRevType ==
Change revision type

== MenuItemExternalTools ==
External tools

== ItemNameEmpty ==
The name cannot be empty

== ItemExists ==
The item {0} already exists

== MovingItems ==
Moving items...

== Options ==
Options

== BranchExplorerOptionsToggleTooltip ==
Hide or show the options panel

== GoButton ==
Go

== GoToBranchBase ==
Go to branch base

== BranchExplorerMenu ==
Branch Explorer

== FilterSelectedBranches ==
Filter by selected branches

== FilterSelectedAndRelatedBranches ==
Filter by selected and related branches

== FilterSelectedBranchesPendingMerges ==
Filter by selected and related branches excluding already merged

== RelayoutMenu ==
Reorder

== MoveBranchUp ==
Move selected branches up

== MoveBranchDown ==
Move selected branches down

== MoveBranchTop ==
Move selected branches to the top

== MoveBranchBottom ==
Move selected branches to the bottom

== ClearBranchRelayout ==
Clear selected branches reorder data

== ResetRelayoutData ==
Clear all reorder data

== ClearAllBranchRelayoutsDialogExplanation ==
This action will remove all your branch reorder preferences for any workspace. Do you want to continue?

== PlasticMoveBranchUpShortcutForWindows ==
Ctrl+Up

== PlasticMoveBranchUpShortcutForMacOS ==
Cmd+Up

== PlasticMoveBranchUpShortcutForLinux ==
Ctrl+Up

== PlasticMoveBranchDownShortcutForWindows ==
Ctrl+Down

== PlasticMoveBranchDownShortcutForMacOS ==
Cmd+Down

== PlasticMoveBranchDownShortcutForLinux ==
Ctrl+Down

== PlasticMoveBranchTopShortcutForWindows ==
Ctrl+Shift+Up

== PlasticMoveBranchTopShortcutForMacOS ==
Cmd+Shift+Up

== PlasticMoveBranchTopShortcutForLinux ==
Ctrl+Shift+Up

== PlasticMoveBranchBottomShortcutForWindows ==
Ctrl+Shift+Down

== PlasticMoveBranchBottomShortcutForMacOS ==
Cmd+Shift+Down

== PlasticMoveBranchBottomShortcutForLinux ==
Ctrl+Shift+Down

== PlasticClearBranchLayoutShortcutForWindows ==
Ctrl+Shift+C

== PlasticClearBranchLayoutShortcutForMacOS ==
Cmd+Shift+C

== PlasticClearBranchLayoutShortcutForLinux ==
Ctrl+Shift+C

== GoToParentChangeset ==
Go to parent changeset

== AddInclusion ==
Add inclusion

== AddExclusion ==
Add exclusion

== CustomBranchQuery ==
Custom branch query rule

== CustomChangesetQuery ==
Custom changeset query rule

== CustomLabelQuery ==
Custom label query rule

== ConditionalBranchFormatText ==
New conditional branch format (branches to be highlighted)

== ConditionalChangesetFormatText ==
New conditional changeset format (changesets to be highlighted)

== ConditionalLabelFormatText ==
New conditional label format (labels to be highlighted)

== InclusionRuleText ==
New inclusion branch filter (branches to be included)

== ExclusionRuleText ==
New exclusion branch filter (branches to be excluded)

== ActiveBranch ==
Active branch in the workspace

== ActiveBranches ==
Active branches in the workspace

== BranchesWithPendingIntegrations ==
Branches with pending merges

== AddFormat ==
Add format

== Calculating ==
Calculating...

== CopyrightTextFormat ==
Copyright © 2006-{0} Unity Technologies. All Rights Reserved.

== FiltersAndConditionalFormat ==
Advanced filters & format

== ReleaseNotesLinkText ==
Release Notes

== WebsiteLink ==
https://unity.com

== ReleaseNotesUrlPreffix ==
https://www.plasticscm.com/download/releasenotes/

== RevertStart ==
Reverting the revision in the workspace...

== RevertingItemsSingular ==
Reverting 1 item...

== RevertingItemsPlural ==
Reverting {0} items...

== RevertingProgress ==
Reverting file {0} of {1}: {2}

== Validating ==
Validating data...

== UpdateReportNoLinesSelected ==
Please select the paths you want to update

== UpdateProgressCalculating ==
Calculating...

== UpdateProgressUpdating ==
Updating...

== UpdateProgressSingular ==
Updated {0} of {1} ({2} of 1 file){4}

== UpdateProgressPlural ==
Updated {0} of {1} ({2} of {3} files){4}

== CheckinComments ==
Check-in comments

== RecentCheckinComments ==
Recent check-in comments

== UpdatingSelectedItems ==
Updating the selected items...

== LoadingItems ==
Loading items...

== LoadingFilters ==
Loading filters...

== LoadingBranches ==
Loading branches...

== LoadingBranchExplorer ==
Loading Branch Explorer...

== LoadingLabels ==
Loading labels...

== LoadingChangesets ==
Loading changesets...

== LoadingShelves ==
Loading shelves...

== LoadingLocks ==
Loading locks...

== LoadingSyncViewDetails ==
Loading sync view details...

== LoadingDeletedItems ==
Loading deleted items...

== UncheckoutStart ==
Unchecking out selected items...

== ActionMenuButtonTooltip ==
Perform actions to the selected item

== MainSidebarItemsItem ==
Workspace Explorer

== MainSidebarPendingChangesItem ==
Pending Changes

== MainSidebarBranchesItem ==
Branches

== MainSidebarBranchExplorerItem ==
Branch Explorer

== MainSidebarChangesetsItem ==
Changesets

== MainSidebarLabelsItem ==
Labels

== MainSidebarAttributesItem ==
Attributes

== MainSidebarSyncItem ==
Sync Repositories

== MainSidebarSyncItemCloudEdition ==
Sync to Cloud

== MainSidebarMergeItem ==
Merge

== MainSidebarIncomingChangesItem ==
Incoming Changes

== Preferences ==
Preferences

== Settings ==
Settings

== MainSidebarAboutItem ==
About Unity Version Control

== MainSidebarAboutGluonItem ==
About Gluon

== MainSidebarDocumentationItem ==
Documentation

== MainSidebarOpenUnityDashboardItem ==
Open Unity Cloud

== MainSidebarUpdateVersionItem ==
Update Unity Version Control

== MainSidebarInstallVersionItem ==
Restart & Install new update

== MainSidebarCodeReviewsItem ==
Code Reviews

== ItemColumn ==
Item

== StatusColumn ==
Status

== ExtensionColumn ==
Extension

== TypeColumn ==
Type

== DateModifiedColumn ==
Date modified

== RepositoryColumn ==
Repository

== ServerColumn ==
Server

== ObjectNameColumn ==
Object name

== NameColumn ==
Name

== PathColumn ==
Path

== ProblemColumn ==
Problem

== SeverityColumn ==
Severity

== TimestampColumn ==
Timestamp

== LastEditedByColumn ==
Last edited by

== CreationDateColumn ==
Creation date

== LastActivityColumn ==
Last activity

== GuidColumn ==
Guid

== CommentColumn ==
Comment

== ItemsCount ==
({0})

== ItemsCountSingular ==
(1 item)

== ItemsCountPlural ==
({0} items)

== ItemsSelected ==
({0}/{1} selected)

== ItemsSelectedSingular ==
{0} of 1 item selected

== ItemsSelectedPlural ==
{0} of {1} items selected

== ItemsSelectedForGluonSingular ==
{0} of 1 item

== ItemsSelectedForGluonPlural ==
{0} of {1} items

== ChangedItemsCategory ==
Changed items

== AddedPrivateItemsCategory ==
Added and private

== DeletedItemsCategory ==
Deleted items

== MovedItemsCategory ==
Moved items

== DeleteConfirmationMessage ==
You are about to delete

== DeleteOthersMessage ==
...and {0} others.

== DeletingObjectsSingular ==
Deleting 1 object

== DeletingObjectsPlural ==
Deleting {0} objects

== DeleteObjectsSingular ==
Deleting 1 object. Are you sure?

== DeleteObjectsPlural ==
Deleting {0} objects. Are you sure?

== ConfirmRevertTitle ==
Confirm revert

== ConfirmRevertMessage ==
Contents will be reverted to revision {0}. Continue?

== ConfirmRevertToClientDiffsMessage ==
The selected files will be reverted. Continue?

== ConfirmUndeleteTitle ==
Confirm undelete

== ConfirmUndeleteMessage ==
The selected files will be undeleted. Continue?

== FromTo ==
{0} to {1}

== From ==
from

== To ==
to

== AddProgress ==
Adding items...

== CheckoutProgress ==
Checking out items...

== UndoingPendingChanges ==
Undoing changes...

== UndoingUnchanged ==
Undoing unchanged items...

== ShelvingPendingChanges ==
Shelving changes...

== UndoingChangedFiles ==
Undoing changes...

== UndoingChangedFilesError ==
Some error has occurred during the undo changes operation:

== New ==
New...

== ItemsMenuItemHistory ==
History

== ItemsMenuItemAnnotate ==
Annotate

== NoItemsAreSelected ==
No items are selected. Use the items' checkboxes to select them. This can also occur if you have selected items, but they are all ignored.

== RepositoryNameLabel ==
Repository name:

== NewRepositoryTitle ==
Create a new repository

== RenameRepositoryTitle ==
Rename Repository

== NewWorkspace ==
Create a new workspace

== OpenRepository ==
Open repository

== DownloadRepository ==
Download repository

== DownloadRepositoryIntoWorkspace ==
Download repository into a new workspace

== DownloadRepositoryTitle ==
Create a new working copy to work with the repository locally

== RenameWorkspaceTitle ==
Rename Workspace

== RepositoryServerLabel ==
Server:

== RepositoryServerOrOrganizationLabel ==
Server or organization:

== OrganizationProjectLabel ==
Organization project:

== CreateOrganizationProjectLabel ==
Create new organization project?

== RepositoryLabel ==
Repository:

== BrowseLabel ==
Browse...

== CouldNotGetOrganizationNameFromCredentials ==
Invalid credentials. Couldn't get the organization name

== CouldNotGetOrganizationName ==
Unable to get the organization name from the cloud server. Maybe your credentials are wrong. Please run again the configuration tool to enter new credentials ('{0} --configure')

== RepositoryServerIsNotAvailable ==
The repository server is not available

== RepositoryNameEmpty ==
Repository name must not be empty

== WorkspaceNameEmpty ==
Workspace name must not be empty

== WorkspacePathEmpty ==
Workspace path must not be empty

== WorkspacePathUnauthorizedAccess ==
Unauthorized access to create the workspace in the specified path

== InvalidCharsInPath ==
Workspace path contains invalid characters

== WorkspaceNameAlreadyExists ==
The workspace {0} already exists

== WorkspacePathAlreadyUsed ==
The path {0} is already contained in a workspace

== RepositoryMenuItemCreate ==
Create new repository...

== RepositoryMenuItemOpenRepository ==
Open this repository

== RepositoryMenuItemDownloadRepository ==
Download this repository

== RenameMenuItem ==
Rename

== WorkspaceMenuItemOpen ==
Open

== WorkspaceMenuItemOpenInNewWindow ==
Open in a new window

== OpenInExplorerMenuItem ==
Open in explorer

== DeleteMenuItem ==
Delete

== UndeleteMenuItem ==
Undelete

== WorkspaceMenuItemCreate ==
Create a new workspace...

== WorkspaceMenuItemRename ==
Rename

== WorkspaceMenuItemDelete ==
Delete

== BranchMenuItemCreateBranch ==
Create child branch...

== BranchMenuItemCreateTopLevelBranch ==
Create top-level branch...

== BranchMenuItemMergeFromBranch ==
Merge from this branch...

== BranchMenuItemAdvancedMerge ==
Advanced merge

== BranchMenuItemCherrypickFromBranch ==
Cherry pick from this branch...

== BranchMenuItemMergeToFromBranch ==
Merge from this branch to...

== BranchMenuItemPushPull ==
Push/Pull

== BranchMenuItemPullBranch ==
Pull this branch...

== BranchMenuItemPullRemoteBranch ==
Pull remote branch...

== BranchMenuItemSyncWithGit ==
Sync with Git...

== BranchMenuItemPushBranch ==
Push this branch...

== BranchMenuItemRenameBranch ==
Rename...

== BranchMenuItemHideBranch ==
Hide

== BranchMenuItemUnhideBranch ==
Unhide

== BranchMenuItemDeleteBranch ==
Delete

== BranchMenuItemSwitchToBranch ==
Switch workspace to this branch

== BranchMenuItemDiff ==
Diff branch

== BranchMenuItemDiffWithAnotherBranch ==
Diff with another branch...

== BranchMenuItemViewChangesets ==
View changesets in this branch

== ChangesetMenuItemCreateBranch ==
Create branch from this changeset...

== ChangesetMenuItemDiff ==
Diff with previous

== ChangesetMenuItemDiffWithAnother ==
Diff with another changeset...

== ChangesetMenuItemDiffSelected ==
Diff selected changesets

== ChangesetMenuItemBrowseRepositoryOnChangeset ==
Browse repository in this changeset

== ChangesetMenuItemLabelChangeset ==
Label this changeset...

== ChangesetMenuItemSwitchToChangeset ==
Switch workspace to this changeset

== ChangesetMenuItemMergeFromChangeset ==
Merge from this changeset...

== ChangesetMenuItemCherrypickFromChangeset ==
Cherry pick from this changeset...

== ChangesetMenuItemAdvancedMerge ==
Advanced merge

== ChangesetMenuItemSubtractiveFromChangeset ==
Subtractive merge from this changeset...

== ChangesetMenuItemSubtractiveFromChangesetInterval ==
Subtractive merge from this changeset interval...

== ChangesetMenuItemCherrypickFromChangesetInterval ==
Cherry pick from this changeset interval...

== ChangesetMenuItemMergeToFromChangeset ==
Merge from this changeset to...

== ChangesetMenuItemAdvancedOptions ==
Advanced options

== ChangesetMenuItemMoveChangeset ==
Move from this changeset to a different branch...

== ChangesetMenuItemDeleteChangeset ==
Delete changeset

== CheckoutChangesetMenuItemShowPendingChangesView ==
Show pending changes view

== LabelMenuItemCreateLabel ==
Create new label...

== LabelMenuItemApplyLabelToWorkspace ==
Apply label to workspace content

== LabelMenuItemSwitchToLabel ==
Switch workspace to this label

== LabelMenuItemBrowseRepositoryOnLabel ==
Browse repository in this label

== LabelMenuItemDiffWithAnother ==
Diff with another label...

== LabelMenuItemDiffSelected ==
Diff selected labels

== LabelMenuItemMergeFromLabel ==
Merge from this label...

== LabelMenuItemMergeToFromLabel ==
Merge from this label to...

== LabelMenuItemCreateBranchFromLabel ==
Create branch from this label...

== LabelMenuItemRenameLabel ==
Rename...

== LabelMenuItemDeleteLabel ==
Delete

== ShelveMenuItemViewShelve ==
View shelve

== ShelveMenuItemApplyShelveInWorkspace ==
Apply shelve in my workspace...

== ShelveMenuItemOpenShelveInNewWindow ==
Open shelve in a new window

== ShelveMenuItemDeleteShelve ==
Delete

== AttributeMenuItemCreate ==
Create

== AttributeMenuItemEdit ==
Edit

== AttributeMenuItemDelete ==
Delete

== PendingChangesMenuItemOpen ==
Open

== PendingChangesMenuItemOpenWith ==
Open with...

== PendingChangesMenuItemDiffAnnotateHistory ==
Diff / Annotate / History

== PendingChangesMenuItemDiffWorkspace ==
Diff workspace contents

== PendingChangesMenuItemAnnotate ==
Annotate

== PendingChangesMenuItemViewHistory ==
History

== PendingChangesMenuItemUndoChanges ==
Undo changes

== PendingChangesMenuItemDelete ==
Delete

== PendingChangesMenuItemCheckout ==
Checkout

== PendingChangesMenuItemSearchMatches ==
Search matches...

== PendingChangesMenuItemHideIgnoreCloak ==
Hide / Ignore / Cloak

== PendingChangesMenuItemHideIgnore ==
Hide / Ignore

== DirectoryConflictsTitleSingular ==
1 directory conflict

== DirectoryConflictsTitlePlural ==
{0} directory conflicts

== FileConflictsTitleSingular ==
1 file conflict

== FileConflictsTitlePlural ==
{0} file conflicts

== NoActionMenuItem ==
No action available

== ItemsMenuItemNew ==
New

== ItemsMenuItemNewDirectory ==
Directory...

== ItemsMenuItemNewFile ==
File...

== ItemsMenuAdd ==
Add to source control

== ItemsMenuAddRecursive ==
Add directory tree to source control

== ItemsMenuCheckin ==
Check in

== ItemsMenuItemOpen ==
Open

== ItemsMenuItemOpenWith ==
Open with...

== ItemsMenuItemOpenWithCustom ==
Open with {0}

== ItemsMenuItemRevealInFinder ==
Reveal in Finder

== ItemsMenuItemCopy ==
Copy

== ItemsMenuItemCut ==
Cut

== ItemsMenuItemDelete ==
Delete...

== ItemsMenuItemPaste ==
Paste

== ItemsMenuItemCreateXlink ==
Create Xlink...

== ItemsMenuItemEditXlink ==
Edit Xlink...

== BrowseRepositoryMenuItemDiffWithPrevious ==
Diff with previous revision

== BrowseRepositoryMenuItemSaveAs ==
Save as...

== BrowseRepositoryMenuItemViewHistory ==
View history

== HistoryMenuItemOpen ==
Open

== HistoryMenuItemOpenWith ==
Open with...

== HistoryMenuItemSaveRevisionAs ==
Save this revision as...

== HistoryMenuItemDiffWithPrevious ==
Diff with previous revision

== HistoryMenuItemDiffSelectedRevisions ==
Diff selected revisions

== HistoryMenuItemDiffChangeset ==
Diff changeset

== HistoryMenuItemRevertToThisRevision ==
Revert to this revision

== ItemsMenuItemRename ==
Rename...

== ItemsMenuItemDiffWithPrevious ==
Diff with previous revision

== HidePrivateItems ==
Hide private items

== ExcludePrivateItemsSize ==
Exclude privates from directory size

== MergeWithPendingChangesTitle ==
Changed files detected

== MergeWithPendingChangesMessage ==
You have changes pending that need to be checked in into your workspace. It is recommended that you check in prior to merge to avoid possible issues undoing merges.
If you are familiar with merge, you can change this behavior by going to Preferences / Diff and merge / Allow to merge with pending changes.

Note: We changed this behavior to avoid issues for new users, although it is not hazardous to enable it if you are familiar with how merge works.

== MergeNotificationMessageTitle ==
Notification message

== BaseContributor ==
Base

== SrcContributor ==
Source

== DstContributor ==
Destination

== DeleteDialogTitleSingular ==
Delete item

== DeleteDialogTitlePlural ==
Delete items

== DeleteFromDiskSingular ==
Delete item from disk

== DeleteFromDiskPlural ==
Delete items from disk

== DoNotDeleteFromDiskSingular ==
Do not delete item from disk

== DoNotDeleteFromDiskPlural ==
Do not delete items from disk

== DeletingProgress ==
Deleting ({0}/{1}) {2}...

== RemoveFromVersionControlPlural ==
Remove items from version control

== DirectoryConflictsCaption ==
These are conflicts in the directory structure. Typically involve moves, renames, deletes. They must be solved before applying other changes.

== DirectoryConflictsUnsolvedCaptionSingular ==
directory conflict to solve.

== DirectoryConflictsUnsolvedCaptionPlural ==
directory conflicts to solve.

== MergeAlreadyConnected ==
The merge search has finished; merge destination already contains source changes.

== MergeAlreadySubtracted ==
The merge search has finished; merge source was already subtracted from the destination.

== MergeInvalidInterval ==
The merge search has finished; the given interval is not valid.

== MergeNoMergesDetected ==
The merge search has finished. No items to merge were detected.

== MergeNoMergesDetectedCreateLink ==
The merge search has finished. No items to merge were detected, however the source and destination contributors are different. This is normally the case when the exact same changes were made in source and destination.

Click the "Explain merge" button to see more details on the contributors involved.

Click the "Apply changes" button to unify the source and destination (creating a merge link between them).


== MergeNoMergesDetectedCreateLinkForIncomingView ==
Nothing to download

Just click "Update workspace" to move your workspace to the head of the branch.


Detailed explanation: Your changes match the ones done in the head of the branch, so there is really nothing to download, but metadata needs to be synchronized.


== MergeNothingToDownloadForIncomingView ==
Nothing to download


== MergeNotConnected ==
The merge search has finished; subtractive merge source and destination are not connected.

== MergeAlreadyProcessed ==
Merge finished. Your changes are displayed ​on the pending changes view. Close this view and you will see them!

== MergeAlreadyProcessedOpenPendingChangesText ==
Merge finished. Your changes are displayed on the

== MergeAlreadyProcessedOpenPendingChangesLinkText ==
pending changes view.

== MergeAlreadyProcessedOpenPreferencesText ==
You can configure Unity VCS to automatically switch to that view when a merge is completed

== MergeAlreadyProcessedOpenPreferencesLinkText ==
here.

== FileMergeTitleSingular ==
1 change to apply

== FileMergeTitlePlural ==
{0} changes to apply

== MergeChangedOnSrc ==
Changes only in source contributor

== MergeChangedOnSrcForIncomingView ==
Will update

== MergeWarningsTitleSingular ==
1 discarded conflict.

== MergeWarningsTitlePlural ==
{0} discarded conflicts.

== MergeWarningsCaption ==
Conflicts that no longer apply and possible warnings.

== XlinkWarningMessageSingular ==
{0} (1 item)

== XlinkWarningMessagePlural ==
{0} ({{0}} items)

== DivergentMoveConflictsSingular ==
Divergent move conflicts (1 item)

== DivergentMoveConflictsPlural ==
Divergent move conflicts ({0} items)

== CyleMoveConflictsSingular ==
Cycle move conflicts (1 item)

== CyleMoveConflictsPlural ==
Cycle move conflicts ({0} items)

== EvilTwinsSingular ==
Evil twin conflicts (1 item)

== EvilTwinsPlural ==
Evil twin conflicts ({0} items)

== MoveDeleteConflictsSingular ==
Move/Delete conflicts (1 item)

== MoveDeleteConflictsPlural ==
Move/Delete conflicts ({0} items)

== DeleteMoveConflictsSingular ==
Delete/Move conflicts (1 item)

== DeleteMoveConflictsPlural ==
Delete/Move conflicts ({0} items)

== MovedEvilTwinsSingular ==
Moved evil twin conflicts (1 item)

== MovedEvilTwinsPlural ==
Moved evil twin conflicts ({0} items)

== AddMoveConflictsSingular ==
Add/Move conflicts (1 item)

== AddMoveConflictsPlural ==
Add/Move conflicts ({0} items)

== MoveAddConflictsSingular ==
Move/Add conflicts (1 item)

== MoveAddConflictsPlural ==
Move/Add conflicts ({0} items)

== LoadedTwiceConflictsSingular ==
Items loaded twice conflicts (1 item)

== LoadedTwiceConflictsPlural ==
Items loaded twice conflicts ({0} items)

== MoveSourceDeleteConflictsSingular ==
Move/Delete, move out of delete conflicts (1 item)

== MoveSourceDeleteConflictsPlural ==
Move/Delete, move out of delete conflicts ({0} items)

== DeleteMoveSourceConflictsSingular ==
Delete/Move, move out of delete conflicts (1 item)

== DeleteMoveSourceConflictsPlural ==
Delete/Move, move out of delete conflicts ({0} items)

== ChangeDeleteConflictsSingular ==
Change/Delete conflicts (1 item)

== ChangeDeleteConflictsPlural ==
Change/Delete conflicts ({0} items)

== DeleteChangeConflictsSingular ==
Delete/Change conflicts (1 item)

== DeleteChangeConflictsPlural ==
Delete/Change conflicts ({0} items)

== ReadonlyXlinkConflictsSingular ==
Readonly Xlink conflicts (1 item)

== ReadonlyXlinkConflictsPlural ==
Readonly Xlink conflicts ({0} items)

== ResolveDirectoryConflict ==
Resolve directory conflict

== ResolveDirectoryConflictChooseOption ==
In order to resolve the conflict, choose one of the following options:

== SelectAMatch ==
Please select a change to be matched

== SearchMatchesDialogTitle ==
Search moved candidates

== SearchMatchesDialogExplanation ==
All possible sources for the new item are displayed. Slide the bar below to change the similarity threshold.

== MinSimilarityAccepted ==
Min. accepted similarity: {0:0.0}

== LoadingMatches ==
Loading matches...

== SrcPathColumn ==
Source path

== DstPathColumn ==
Destination path

== SimilarityColumn ==
Similarity

== DiffCategoryHeaderSingular ==
{0}: 1 item

== DiffCategoryHeaderPlural ==
{0}: {1} items

== MergeDiffCategory ==
Merged from {0}

== AddedMergeDiffCategory ==
Added (files added in the merge source)

== DeletedMergeDiffCategory ==
Deleted (files deleted in the merge source)

== MergedMergeDiffCategory ==
Merged (files changed in the merge source and in the merge destination)

== MovedMergeDiffCategory ==
Moved (files moved in the merge source)

== ReplacedMergeDiffCategory ==
Changed (files changed in the merge source)

== AddedDiffCategory ==
Added

== ChangedDiffCategory ==
Changed

== DeletedDiffCategory ==
Deleted

== MovedDiffCategory ==
Moved

== MergedDiffCategory ==
Merged

== FSProtectionDiffCategory ==
Only filesystem permissions changed

== Source ==
Source/theirs:

== Destination ==
Destination/yours:

== KeepSrcChanges ==
Keep source changes

== KeepSrcNotSupported ==
Keeping the source item is not supported for this type of conflict

== KeepDstChanges ==
Keep destination changes

== KeepDstNotSupported ==
Keeping the destination item is not supported for this type of conflict

== RenameOnDst ==
Rename item in your workspace

== RenameNotSupported ==
Renaming of destination item is not supported for this type of conflict

== InputItemNameMessage ==
Please input an item name

== HelpButton ==
Help

== AdvancedButton ==
Advanced

== BranchExplorerSearchFieldTooltip ==
Type a search pattern. Use cs: lb: or br: to restrict results

== BranchColumn ==
Branch

== DestinationBranchColumn ==
Destination branch

== CancelButton ==
Cancel

== FindButton ==
Find

== CloseButton ==
Close

== OkButton ==
OK

== YesButton ==
Yes

== NoButton ==
No

== CheckinButton ==
Check in

== CheckinToADifferentBranchButton ==
Check in changes to a different branch...

== CheckinToADifferentBranch ==
Check in changes to a different branch

== CheckinToADifferentBranchNewBranchWaterMark ==
task001

== MoveChangesetsToADifferentBranch ==
Move changesets to a different branch

== ShelveButton ==
Shelve

== ShowShelvesButton ==
Show shelves

== HideShelvesButton ==
Hide shelves

== ShowShelvesViewTooltip ==
Show the shelves view

== NoShelvesCreatedExplanation ==
There are no shelves created yet

== NoContentToCompareExplanation ==
There is no content to compare

== NoContentToBrowseExplanation ==
There is no content to browse

== NoLocksCreatedExplanation ==
There are no file locks created yet.

== LoadLocksError ==
Cannot retrieve the locks information

== LoadLocksErrorExplanation ==
Cannot retrieve the locks information - {0}

== ShowLinkedTasksButton ==
Show tasks

== OptionsTooltip ==
Configure the pending changes options

== ChooseMessage ==
Choose...

== GluonConfigurationSelectRepositoryButton ==
Select...

== GluonConfigurationBrowseWorkspacePathButton ==
Choose Path...

== GluonConfigurationCreateRepositoryButton ==
Create...

== ChangesetColumn ==
Changeset

== ExecuteButton ==
Execute

== FileConflictsCaptionSingular ==
file conflict to solve.

== FileConflictsCaptionPlural ==
file conflicts to solve.

== FileConflictsCaptionForIncomingViewSingular ==
file in conflict with your changes.

== FileConflictsCaptionForIncomingViewPlural ==
files in conflict with your changes.

== FilterLabel ==
Filter:

== Controlled ==
Controlled

== ItemsMenuCheckout ==
Checkout

== ItemsMenuLockAndCheckout ==
Lock and checkout

== ItemsMenuCheckoutRecursive ==
Checkout recursively

== CheckedOut ==
Checked-out

== Deleted ==
Deleted

== ChangedOnDisk ==
Changed on disk

== ContainsChanges ==
Contains changes

== Private ==
Private

== Ignored ==
Ignored

== Cloaked ==
Cloaked

== HiddenChanged ==
Hidden changed

== MainWindowTitle ==
Unity DevOps Version Control - wk: {0} - {1} - {2}: {3}@{4}

== StatusInfo ==
You are browsing {0} {1}@{2}

== DiffWindowTitle ==
Comparing previous content with revisions on {0}

== ChangesetsDiffWindowTitle ==
Comparing revisions on {0} with revisions on {1}

== FsProtectionDifferences ==
Filesystem permissions changed from {0} to {1}

== NoItemsToUndo ==
No items are selected. Use the items' checkboxes to select them. This can also occur if you have selected items, but they are all private or ignored.

== ResolveConflictsButton ==
Resolve conflicts

== ApplyChangesButton ==
Apply changes

== DiscardChangesButton ==
Discard changes

== QueryLabel ==
Query:

== RefreshButton ==
Refresh

== RefreshButtonAmp ==
&Refresh

== GoHomeTooltip ==
Go to working changeset

== CloudAccount ==
Cloud

== OnPremAccount ==
On-premises

== AddNewAccount ==
Add new account

== ResolutionMethodColumn ==
Resolution method

== UndoButton ==
Undo

== UndoUnchangedButton ==
Undo unchanged

== UndoRequest ==
Confirm undoing changes

== UndoRequestMessageSingular ==
You are going to undo 1 change. This operation can't be undone. Continue?

== UndoRequestMessagePlural ==
You are going to undo {0} changes. This operation can't be undone. Continue?

== CheckoutIgnoringFailedRequest ==
Some files can't be checkedout because they are locked

== CheckoutIgnoringFailedRequestMessage ==

Do you want to skip the locked files and checkout the rest?

The locked files are: {0}

== UndoUnchangedRequestMessage ==
You are going to undo unchanged items. This operation can't be undone. Continue?

== UndoWithMergeLinkMessageSingular ==
You are going to undo 1 change and the related merge links. This operation can't be undone. Continue?

== UndoWithMergeLinkMessagePlural ==
You are going to undo {0} changes and the related merge links. This operation can't be undone. Continue?

== PendingChangesOptionsTitle ==
Pending changes options

== PendingChangesOptionsSectionTitle ==
Pending Changes Options

== DiffAndMergeOptionsSectionTitle ==
Diff and Merge Options

== ShelveAndSwitchOptionsSectionTitle ==
Shelve and Switch Options

== OtherOptionsSectionTitle ==
Other Options

== CreatingRepository ==
Creating repository {0}...

== UnresolvedRepository ==
Can't resolve the repository {0}

== CreatingWorkspace ==
Creating workspace {0}...

== WaitingForDynamicWorkspaceToBeMounted ==
Waiting to mount dynamic workspace {0}...

== CreatingBranch ==
Creating branch {0}...

== CreatingLabel ==
Creating label {0}...

== RepositoryNameAlreadyExists ==
The repository {0} already exists

== RenamingRepository ==
Renaming repository...

== RenamingWorkspace ==
Renaming workspace {0}...

== RenamingBranch ==
Renaming branch {0}...

== RenamingLabel ==
Renaming label {0}...

== EditingAttribute ==
Editing attribute '{0}'...

== ChangingRevisionType ==
Changing the revision type...

== DeletingWorkspacesSingular ==
Deleting workspace{0}...

== DeletingWorkspacesPlural ==
Deleting workspaces{0}...

== DeletingRepositoriesSingular ==
Deleting repository{0}...

== DeletingRepositoriesPlural ==
Deleting repositories{0}...

== UndeletingRepositoriesSingular ==
Undeleting repository{0}...

== UndeletingRepositoriesPlural ==
Undeleting repositories{0}...

== HidingBranchesSingular ==
Hiding branch{0}...

== HidingBranchesPlural ==
Hiding branches{0}...

== UnhidingBranchesSingular ==
Unhiding branch{0}...

== UnhidingBranchesPlural ==
Unhiding branches{0}...

== DeletingBranchesSingular ==
Deleting branch{0}...

== DeletingBranchesPlural ==
Deleting branches{0}...

== DeletingLabelsSingular ==
Deleting label{0}...

== DeletingLabelsPlural ==
Deleting labels{0}...

== DeletingShelvesSingular ==
Deleting shelve...

== DeletingShelvesPlural ==
Deleting shelves...

== RemovingLocksSingular ==
Removing lock...

== RemovingLocksPlural ==
Removing locks...

== ReleasingLocksSingular ==
Releasing lock...

== ReleasingLocksPlural ==
Releasing locks...

== ApplyingShelve ==
Applying shelve...

== DeletingAttributesSingular ==
Deleting attribute {0}...

== DeletingAttributesPlural ==
Deleting attributes {0}...

== DeletingChangeset ==
Deleting changeset...

== MovingChangeset ==
Moving changeset...

== PreviousLabel ==
Previous result

== NextLabel ==
Next result

== BrexSearchFieldLabel ==
Branch Explorer search

== BrexDisplayOptions ==
Display options

== SelectAll ==
Select all

== MergeSelectedFiles ==
Merge selected files

== MergeKeepingSourceChanges ==
Merge keeping source changes (theirs)

== MergeKeepingWorkspaceChanges ==
Merge keeping workspace changes (yours/destination)

== MergeOptionsButton ==
Merge Options

== MergeOptionsTitle ==
Merge Options

== MergeOptionsExplanation ==
Use the following options to customize the merge operation to your needs

== MergeOptionsContributorsTitle ==
Select merge contributor

== MergeOptionBoth ==
Merge changes from both contributors

== MergeOptionFromDestination ==
Preserve workspace changes (discarding the source contributor's changes)

== MergeOptionFromSource ==
Preserve source changes (discarding your changes)

== MergeOptionSkipMergeTrackingTitle ==
Merge tracking

== MergeOptionSkipMergeTrackingExplanation ==
Ignore merge tracking

== MergeOptionSkipMergeTrackingHelpTitle ==
Merge information

== MergeOptionSkipMergeTrackingHelp ==
Unity VCS usually bypasses items whose changes don't need to be merged (typically because those changes have been merged previously).
If you check this option, all items that can be merged will be shown, regardless of whether they have been merged previously. This way you can integrate again changes that were integrated in the past.
This option is only available for cherry-pick and revision-interval merges.

== MergeOptionAdvancedTitle ==
Advanced

== MergeOptionAutomaticCalculateAncestor ==
Automatic calculate the common ancestor changeset for merge

== MergeOptionManualSpecifyAncestor ==
Specify a common ancestor changeset manually:

== MergeOptionManualSpecifyAncestorExample ==
(i.e 4572)

== DiffDestinationWithAncestor ==
Diff ancestor with destination contributor

== DiffSourceWithAncestor ==
Diff ancestor with source contributor

== DiffSourceWithDestination ==
Diff source with destination

== DiffSourceWithDestinationEvilTwin ==
Diff item added in source with item added in destination

== DiffSourceWithDestinationAddMove ==
Diff item added in source with item moved in destination

== DiffSourceWithDestinationCycleMove ==
Diff item moved in source with item moved in destination

== DiffSourceWithDestinationDeleteMove ==
Diff item deleted in source with item moved in destination

== DiffSourceWithDestinationMoveAdd ==
Diff item moved in source with item added in destination

== DiffSourceWithDestinationMoveDelete ==
Diff item moved in source with item deleted in destination

== DiffSourceWithDestinationMoveEvilTwin ==
Diff item moved in source with item moved in destination

== DiffSourceWithDestinationChangeDelete ==
View changes on source item

== DiffSourceWithDestinationDeleteChange ==
View changes on destination item

== DiffEntireBranch ==
Diff entire branch

== DiffChangesetByChangeset ==
Diff changeset by changeset

== RetryUpdate ==
Retry update

== UpdateForced ==
Update forced

== LoadingWorkspaces ==
Loading workspaces...

== LoadingHistory ==
Loading history...

== LoadingAnnotations ==
Loading annotations...

== LoadingBrowseRepository ==
Loading changeset tree...

== ProcessingMerge ==
Processing merge...

== CalculatingMerge ==
Calculating merges...

== CalculatingIncomingChanges ==
Calculating incoming changes...

== LoadingFindMerge ==
Finding elements to merge...

== LoadingPendingChanges ==
Finding changes in the workspace...

== LabellingWorkspace ==
Applying the label {0} to the workspace...

== LabellingChangeset ==
Applying the label {0} to the changeset {1}...

== ApplyingLocalChanges ==
Applying local changes...

== ApplyingConflicts ==
Applying conflicts...

== LoadingRepositories ==
Loading repositories...

== NewRepositoryExplanation ==
Type a name for the new repository and ensure you will create it on the correct server or organization.

== NewWorkspaceExplanation ==
Download a working copy of the repository {0}. It will just contain the files to work, not the entire history. A workspace is just the directory on disk that you use to make changes, edit, build and test locally.

== RenameWorkspaceExplanation ==
Renaming the workspace won’t modify its path.

== ItemSearchField ==
Item search

== SearchInProgress ==
Searching...

== SearchIncludePrivateItems ==
Include private items

== SizeColumn ==
Size

== BinaryFile ==
Binary

== Directory ==
Directory

== TextFile ==
Text

== LinkFile ==
Symlink

== Unknown ==
Unknown

== CustomExtensionFile ==
Custom extension

== CreateChildBranchTitle ==
Create Branch

== CreateChildBranchExplanation ==
Create a new child branch from

== ReplicateBranchTitle ==
Unity VCS Replication

== ReplicateBranchExplanation ==
Replicate a branch from/to another repository.

== UsingProfile ==
Profile: {0}

== DefaultProfile ==
Using default configuration

== LabelChangesetExplanation ==
Label changeset {0} of {1}@{2}

== RenameBranchTitle ==
Rename Branch

== CreateLabelTitle ==
Apply a label to a changeset

== CreateLabelExplanation ==
Labels help highlight important changesets. Typically, labels mark stable baselines and releases to customers.

== RenameLabelTitle ==
Rename Label

== EditAttributeTitle ==
Edit attribute

== LastChangeset ==
last changeset

== BranchNameEntry ==
Branch name:

== BranchNameAlreadyExists ==
The branch {0} already exists

== LabelNameAlreadyExists ==
The label {0} already exists ({1})

== LabelNameEntry ==
Label name:

== ChangesetToLabelEntry ==
Changeset to label:

== CommentsEntry ==
Comments:

== SwitchToBranchCheckButton ==
Switch workspace to this branch

== SwitchToLabelCheckButton ==
Switch workspace to this label

== SwitchToBranchTitle ==
Switch to branch

== SwitchToChangesetTitle ==
Switch to changeset

== SwitchToLabelTitle ==
Switch to label

== SwitchToExplanation ==
There are pending changes in your workspace.

You can continue with the switch operation or cancel the switch and open a Pending Changes view, so that you can check in (or undo) the pending changes. Then, you can run the "{0}" command again.

If you continue with the switch operation, your local changes won't be lost, and will still appear as pending changes after the switch operation.

== LeaveChangesButton ==
Leave changes on {0}

== LeaveChangesExplanation ==
Your changes will be saved on a shelve. You can apply or discard them later

== BringChangesButton ==
Bring changes to {0}

== BringChangesExplanation ==
Your changes will be applied automatically on the destination. You will be notified if any conflicts need to be resolved

== ApplyShelveReportTitle ==
Apply shelve report

== ApplyShelveReportExplanation ==
The apply shelve operation found some issues. Please review them.

== ApplyShelveWithConflictsTitle ==
Conflict Resolution Required

== ApplyShelveWithConflictsExplanation ==
Conflicts were found while bringing your changes to {0}. The switch has been completed.

Would you like to resolve these conflicts now, or handle them later?

== ResolveConflictsNow ==
Yes, resolve conflicts now

== ResolveConflictsLater ==
No, I'll resolve conflicts later

== ShelveAndSwitchDialogTitle ==
Handle pending changes

== ShelveAndSwitchExplanation ==
You have pending changes on your workspace. How would you like to handle them?

== SwitchToLabelExplanation ==
There are pending changes in your workspace.

You can continue with the switch operation or cancel the switch and open a Pending Changes view, so that you can check in (or undo) the pending changes. Then, you can run the "Switch workspace to this label" command again.

If you continue with the switch operation, your local changes won't be lost, and will still appear as pending changes after the switch operation. But, you won't be able to submit these changes, because a label configuration is a read-only configuration.

To modify a labeled configuration, create a branch, setting the label as the branch base. Then, switch the workspace to that branch and make changes as usual.

== SwitchToConfirmationCheckButton ==
Don't show this dialog again

== SwitchToConfirmationContinueButton ==
Continue with the switch

== SwitchToConfirmationCancelViewChangesButton ==
Cancel and view changes

== SwitchToLabelConfirmation ==
Switching your workspace to a label, which loads the labeled revision of each item. All the loaded revisions will be read-only.

To modify a labeled configuration, create a branch, setting the label as the branch base. Then, switch the workspace to that branch and make changes as usual.

Continue?

== SwitchActionOptionsTitle ==
Behavior when trying to switch/update the workspace with changed items

== SwitchActionAllowOption ==
Allow

== SwitchActionWarnOption ==
Allow, showing a warning

== SwitchActionFailOption ==
Do not allow, show an error

== SwitchActionShelveOption ==
Shelve

== SwitchActionAllowExplanation ==
Switch without any restrictions, keeping pending changes as they are

== SwitchActionWarnExplanation ==
Switch with a warning about pending changes in the current workspace

== SwitchActionFailExplanation ==
Prevent switching and display an error message about pending changes

== SwitchActionShelveExplanation ==
Save your pending changes before switching. Select how you want to handle the shelved changes

== UpdateResultsTitle ==
Update results

== UpdateResultsExplanation ==
The update operation found some issues. Please review them.

== CheckinConflictsTitle ==
Check-in conflicts

== CheckinConflictsExplanation ==
The check-in operation found the following conflicts that can't be checked in.
Click "OK" to continue the operation for the items not in conflict.
 Click "Cancel" to abort the entire operation.

== CheckinConflictsToMergeTitle ==
Files need merging

== CheckinConflictsToMergeExplanation ==
There are newer changes that need merging with yours.
Click "Merge" to merge the files now.
Otherwise, click "Cancel".

== EditAttributeExplanation ==
You can give a new name to the attribute and change its comment text.

== EmptyAttributes ==
No attributes applied yet

== ResetQueryButton ==
Reset query

== ClearHistoryButton ==
Clear history

== SetAsDefaultQuery ==
Set as default query

== NewDirectoryTitle ==
Create a new directory

== NewFileTitle ==
Create a new empty file

== NewDirectory ==
New directory

== NewFile ==
New file

== RenameDirectoryTitle ==
Rename Directory

== RenameFileTitle ==
Rename File

== NewSyncTitle ==
Create a new Sync View

== DependenciesTitle ==
Undo checkout dependencies

== DependenciesDialogTitle ==
Pending dependencies

== AddChildBranches ==
Add child branches

== AddParentBranches ==
Add parent branches

== AddBranchesSourceOfMerge ==
Add branches source of merge

== AddBranchesDestinationOfMerge ==
Add branches destination of merge

== TypeDescription ==
Type a description here

== ConditionalBranchRulesHelp ==
You can type a query in 'cm find' format but without having to specify 'find branch'.
Check the 'find guide' for more information.
Examples:
  changesets >= '2 months ago'
  attribute = 'status' and attrvalue='finished'

== ConditionalChangesetRulesHelp ==
You can type a query in 'cm find' format but without having to specify 'find changeset'.
Check the 'find guide' for more information.
Examples:
  branch = 'main'
  attribute = 'status' and attrvalue='finished'

== ConditionalLabelRulesHelp ==
You can type a query in 'cm find' format but without having to specify 'find label'.
Check the 'find guide' for more information.
Examples:
  date >= '2 months ago'
  attribute = 'status' and attrvalue='finished'

== DependenciesExplanation ==
Some selected items depend on others that need to be included in the operation as well.
Click "{0}" to proceed with all dependent items (recommended), or cancel the operation.

== MergeLinkDescriptionColumn ==
Merge link description

== TaskIdColumn ==
ID

== TaskTitleColumn ==
Title

== AssigneeColumn ==
Assignee

== LocationColumn ==
Location

== LineColumn ==
Line

== ColumnColumn ==
Column

== ContributorColumn ==
Contributor

== LineContentColumn ==
Line content

== Merge ==
Merge

== CherryPick ==
Cherry pick

== IntervalMerge ==
Interval merge

== SubtractiveMerge ==
Subtractive merge

== SubtractiveIntervalMerge ==
Subtractive interval merge

== UnsupportedMergeType ==
Unsupported merge type

== MergeSourceFormat ==
{0} at {1}@{2}

== ConfirmDeleteTitle ==
Confirm delete

== ConfirmApplyLabelTitle ==
Confirm labeling operation

== ConfirmApplyLabelExplanation ==
Applying label "{0}" to all revisions currently in your workspace. Continue?

== BranchNameEmpty ==
Branch name must not be empty

== LabelNameEmpty ==
Label name must not be empty

== SyncViewNameEmpty ==
Sync view name must not be empty

== AttributeNameEmpty ==
Attribute name must not be empty

== LabelButton ==
Label

== LabelAllXlinksCheckButton ==
Label all writable xlinked repositories

== RenameButton ==
Rename

== Rename ==
Rename

== CreateButton ==
Create

== SaveButton ==
Save

== DeleteButton ==
Delete

== SwitchButton ==
Switch

== DiffWindowMenuItemDiff ==
Diff

== ApplyButton ==
Apply

== MergeButton ==
Merge

== BrExNewChangesetWillBeCreated ==
New changeset

== BrexMergeLinkTitle ==
Merge link {0}

== BrexMergeLinkCaptionBody ==
Source: {0}{1}Destination: {2}

== BrexMergeLinkPending ==
, pending

== BrexMergeChangesetInterval ==
Changeset interval ({0} - {1}]

== BrexMergeCouldNotMerge ==
[Oops, could not get the interval data!]

== BrexMergeChangesetOnBranch ==
changeset {0} on branch {1}

== BrexCherryPickLinkType ==
(Cherry pick)

== BrexIntervalCherryPickLinkType ==
(Cherry pick from changeset interval)

== BrexIntervalCherryPickSubstractiveLinkType ==
(Subtractive from changeset interval)

== BrexCherryPickSubstractiveLinkType ==
(Subtractive)

== BrexProperties ==
Properties

== BrexAttributes ==
Attributes

== CredentialsDialogTitle ==
Connect to server

== CredentialsDialogExplanation ==
Please enter your credentials to connect to {0}

== UserName ==
User name:

== Password ==
Password:

== RememberCredentialsAsProfile ==
Remember credentials as a connection profile

== CredentialsErrorSavingProfile ==
Error saving the connection profile: {0}

== BrexObjectNameProperty ==
Object name:

== BrexCreationDateProperty ==
Creation date:

== BrexOwnerProperty ==
Owner:

== BrexReplicationSourceProperty ==
Replication src:

== BrexGuidProperty ==
Guid:

== BrexCommentsProperty ==
Comments:

== BrexDisplayBranches ==
Display branches

== BrexDisplayFullBranchNames ==
Display full branch names

== BrexDisplayMergeLinks ==
Display merge links

== BrexDisplayCrossBranchCsetLinks ==
Display cross-branch changeset links

== BrexDisplayLabels ==
Display labels

== BrexDisplayBranchTaskInfo ==
Display branch task info

== NoReplicationSources ==
Not replicated

== GluonConfiguration ==
Gluon Configuration

== EnterServer ==
Please enter a server name

== EnterServerFormat ==
Please specify the server name in server:port format. The default port is 8087

== EnterProxy ==
Please enter a proxy name

== EnterProxyFormat ==
Please specify the proxy name in server:port format. The default port is 8087

== ConnectingToProxy ==
Connecting to proxy

== EnterUserName ==
Please enter a user name

== EnterPassword ==
Please enter a password

== EnterRepository ==
Please enter a repository

== EnterCloudRepository ==
Please enter a Cloud repository name

== EnterLocalRepository ==
Please enter a local repository name

== NoValidPath ==
No valid path

== Connected ==
Connected OK ({0})

== CredentialsOK ==
Credentials checked OK

== Connecting ==
Connecting...

== CheckingCredentials ==
Checking credentials...

== Connect ==
Connect

== ScanNetwork ==
Scan network...

== Check ==
Check

== ChooseRepositoryCloudEdition ==
Join an existing project or create a new one

== EnterRepositoryWaterMark ==
Enter a repository name or choose from the list

== GluonConfigurationWorkspaceLabel ==
Workspace:

== GluonConfigurationWorkspaceExplanation ==
Select your working directory (a.k.a workspace). This is the directory that will contain the files that you will be working with.

== ApplyingChanges ==
Applying changes...

== Apply ==
Apply

== CouldNotFindWorkingRepositoryOrBranch ==
Could not find working repository or branch

== AvailableChangesets ==
Available Unity VCS changesets

== SelectChangesetBelow ==
Please select a changeset from the list below:

== AvailableLabels ==
Available Unity VCS labels

== SelectLabelBelow ==
Please select a label from the list below:

== AvailableBranches ==
Available Unity VCS branches

== AvailableRepositories ==
Available Unity VCS repositories

== SelectBranchesBelow ==
Please select a branch from the list below:

== SelectRepositoryBelow ==
Please select a repository from the list below:

== AvailableServers ==
Available Unity VCS servers

== SelectServerBelow ==
Please select a server from the list below:

== ScanningForServers ==
Scanning for servers...

== AddressColumn ==
Address

== PortColumn ==
Port

== VersionColumn ==
Version

== FilterWaterMark ==
Filter

== QuickFilterWaterMark ==
Quick filter

== ExploreWorkspace ==
Explore workspace

== CheckinChanges ==
Check in Changes

== Configure ==
Configure

== Loading ==
Loading...

== OperationRunning ==
Operation running

== OperationInProgress ==
There is one operation currently running. Please wait until it is finished.

== ConfirmClosingRunningOperation ==
An operation is currently running. Are you sure you want to quit?

== SearchFiles ==
Search files...

== SearchFilesHint ==
You can use '*' and '?' to match file names. You can also use directories in your search.

== SearchForFiles ==
Search for files

== Searching ==
Searching

== IncludePrivateFiles ==
Include private files

== NoMatchesFound ==
No matches found

== ContainerFolderColumn ==
Container folder

== CheckinComment ==
Add a comment to your check-in or shelve...

== CheckinOnlyComment ==
Add a comment to your check-in...

== BrowseForFolderTitle ==
Browse for folder

== BrowseForSaveFileTitle ==
Save file

== PlasticSCMServerLabel ==
Enter the name/IP and port of your server

== PreferencesWindowTitle ==
Unity DevOps Version Control Preferences - {0}

== GeneralPreferences ==
General

== DiffAndMerge ==
Diff and merge

== DiffAndMergeExplanation ==
Preferences for the Unity VCS built-in diff and merge tool (Xmerge):

== ComparisonMethodAndEncodingTitle ==
Comparison method and encoding

== MergeViewBehaviorTitle ==
Merge view behavior

== ConflictResolutionTitle ==
Merge conflict resolution

== ComparisonMethod ==
Comparison method:

== ManualConflictResolution ==
Manual conflict resolution

== ManualConflictResolutionTooltip ==
Always launch the merge tool to review conflicts, even if all of them are automatic conflicts

== AutomaticConflictResolution ==
Automatic conflict resolution (if possible)

== AutomaticConflictResolutionTooltip ==
Launch the merge tool only when user interaction is required to resolve a conflict

== CloseMergeAndOpenPendingChanges ==
Close merge view and open pending changes when a merge is completed

== MergeWithPendingChanges ==
Allow merge with pending changes

== MergeWithPendingChangesExplanation ==
disabled by default so new users don't have problems undoing merges

== CommentsOptions ==
Comments

== OtherOptions ==
Other options

== MergeBotsUserProfileOptions ==
Mergebots

== MergeBotsUserProfileOptionsExplanation ==
This tab lets you configure your Mergebots user profile to receive notifications via email or Slack.

== DynamicWorkspaces ==
Dynamic Workspaces

== IssueTrackers ==
Issue trackers

== CodeEditor ==
Code editor

== MiscOptionsTitle ==
Miscellaneous

== CommentsAutoTextLabel ==
Comments auto-text

== CommentsAutoTextNoneOption ==
None

== CommentsAutoTextUserNameOption ==
UserName

== CommentsAutoTextDateOption ==
Date

== CommentsAutoTextCustomOption ==
Custom

== CommentsAutoTextBranchNameOption ==
Branch Name

== CommentsAutoTextHelpTitle ==
Information

== CommentsAutoTextHelpMessage ==
Format string syntax:
{0} = UserName
{1} = Date
{2} = New line character
{3} = Branch name

Example: "{0} - {1} {2} br:{3} {2} Summary: "
will show:

    charlie - 05/10/2011 10:30
    br:/main/task1673
    Summary:

== CommentsAutoTextFormatErrorMessage ==
{0} is not a valid format string for auto-text comments

== SetFilesAsReadOnlyAfterUpdateOrCi ==
Update and Check-in operations set files as read-only

== CompareChangedFilesContents ==
Check content (hash) when the file timestamp is modified to set it as "Changed"

== CompareChangedFilesContentsTooltip ==
This is used to established the "Changed" item status

== UpdateSetsTimestamps ==
Update operation sets repository timestamps on files

== EnableSharedWorkspaces ==
Enable shared workspaces support (Samba/WinShare)

== LanguageTitle ==
Set the language for Unity Version Control

== Language ==
Language

== LanguageChangeInfoMessage ==
An application restart is required to apply the language settings

== LanguageRestartInfoMessage ==
Would you like to restart the application now to apply the new language setting?

== RestartNow ==
Restart now

== RestartMessageTitle ==
Restart application

== SelectLanguage ==
Select language

== DisplayErrorOnSameCaseFiles ==
Display error for same file name with different case

== DisplayErrorOnSameCaseFilesTooltip ==
Applies only to Unix-like filesystems

== ShowEmptyCommentWarning ==
Remind to add a comment before the following actions

== ShowEmptyShelveCommentWarning ==
Remind to add a comment before creating a new shelve

== ShowEmptyCommentWarning_ForGluon ==
Warn about empty comment in Checkin view

== DefaultWorkspaceRoot ==
Default Workspace root:

== CommandHelpDialogTitle ==
Command help

== MergeToolsConfiguration ==
Merge tools

== DiffToolsConfiguration ==
Diff tools

== DiffToolsTableTitle ==
Configure a diff tool for different file types and extensions. The rules are applied in the order below, top to bottom.

== DiffToolsExplanation ==
Select a file type or extension and enter the command to run the diff tool:

== DiffToolsCommandHelp ==
You can use the following command-line parameters when invoking the external diff tool.
            You must ensure that the specified files have the actual revision contents to be compared.

            @sourcefile: Full path of file whose contents will be displayed in the panel on the left.
            @destinationfile: Full path of file whose contents will be displayed in the panel on the right.

            @sourcesymbolic: Symbolic name for the source file.
            @destinationsymbolic: Symbolic name for the destination file.

            @sourcehash: MD5 string calculated from the source file contents.
            @destinationhash: MD5 string calculated from the destination file contents.

            @filetype: MIME type, used for syntax highlighting.
            @comparationmethod: Comparison method used.
            @fileencoding: Default file encoding to use when no encoding is detected.

== MergeToolsTableTitle ==
Configure a merge tool for different file types and extensions. The rules are applied in the order below, top to bottom.

== MergeToolsExplanation ==
Select a file type or extension and enter the command to run the merge tool:

== MergeToolsCommandHelp ==
The following variables are available to use as arguments when invoking the external merge tool in the command line:

            @sourcefile: Full path of the source merge file.
            @destinationfile: Path of the destination merge file.
            @basefile: Path of the common ancestor merge file.
            @output: Path of the merge result file.

            @sourcesymbolic: Symbolic name for the source file.
            @destinationsymbolic: Symbolic name for the destination file.
            @basesymbolic: Symbolic name for the base file.

            @sourcehash: MD5 string calculated from the source file contents.
            @destinationhash: MD5 string calculated from the destination file contents.
            @basehash: MD5 string calculated from base file contents.

            @filetype: MIME type used for syntax highlight.
            @fileencoding: Default file encoding to use when no encoding is detected.
            @resultencoding: Result file encoding (if NONE selected, it will be calculated from contributors).

            @comparationmethod: Comparison method used.
            @mergetype: Type of merge used.

            @progress: A progress string indicating the current operation, e.g. 'Merging file 3/10'.
            @extrainfofile: Path of a file which contains extra information about the current merge.

== TypeSuffixColumn ==
Type/Suffix

== DiffCommandColumn ==
Diff tool command line

== MergeCommandColumn ==
Merge tool command line

== AddToolButton ==
Add

== RemoveToolButton ==
Remove

== FileTypeQuestion ==
Which files should this command match?

== ExtensionTooltip ==
.extension

== ExternalTool ==
External tool:

== ExternalToolNotFoundError ==
Tool not found, please introduce a valid one

== BrowseForExecutableFile ==
Browse for executable file

== CommandTooltip ==
Type your command here

== FileCorruptError ==
The file {0} contains errors and its configuration cannot be used. Please review its contents. Error: {1}

== UndoCheckoutTitle ==
Undo checkout

== UndoCheckoutQuestion ==
Undoing the checkout of the selected items will discard any changes made to them since the last check-in. This operation can't be undone. Continue?

== RenameSyncTitle ==
Rename Sync View

== RenameTitle ==
Rename item

== ApplyLocalChanges ==
Apply local changes

== SearchingDependencies ==
Searching dependencies...

== ApplyingDependencies ==
Applying dependencies...

== CheckinViewOptions ==
Checkin view options

== Canceling ==
Canceling...

== CancelingMerge ==
Canceling... (wait until current merge operation finishes)

== KeepLocked ==
Keep items locked

== StatusCannotMerge ==
Cannot merge

== StatusOutOfDate ==
Out of date

== StatusDeletedOnServer ==
Deleted on server

== StatusLockedBy ==
Locked by '{0}'

== StatusRetainedBy ==
Retained by '{0}'

== StatusRetained ==
Retained

== StatusCheckedOutChanged ==
Checked-out (changed)

== StatusCheckedOutUnchanged ==
Checked-out (unchanged)

== StatusChanged ==
Changed

== StatusReplaced ==
Replaced

== StatusNotOnDisk ==
Not on disk

== StatusAdded ==
Added

== StatusIgnored ==
Ignored

== UpdatingWorkspace ==
Updating your workspace...

== ApplyingConfiguration ==
Applying configuration...

== SwitchingToBranch ==
Switching to branch {0}...

== SwitchingToChangeset ==
Switching to changeset {0}...

== SwitchingToLabel ==
Switching to label {0}...

== StatusUnderUnresolvedXlink ==
The xlinked cset '{0}@{1}' is not available

== MovedHistoryDescription ==
Moved from {0} to "{1}"

== RemovedHistoryDescription ==
Removed {0}

== SelectProgramToLaunch ==
Select a program to launch

== ItemsMenuUpdate ==
Update

== ConfigurationUnresolvedXlink ==
The xlinked cset '{0}@{1}' is not available

== WorkspacesViewTitle ==
Workspaces

== RepositoriesViewTitle ==
Repositories

== PendingChangesViewTitle ==
Pending Changes

== BranchesViewTitle ==
Branches

== ChangesetsViewTitle ==
Changesets

== SyncViewTitle ==
Sync Repositories

== ShelvesViewTitle ==
Shelves

== ShelveAndSwitchViewTitle ==
Shelve & Switch

== LocksViewTitle ==
File locks

== HistoryViewTitle ==
History of {0}

== AnnotateViewTitle ==
Annotate of {0}

== BrowseRepositoryViewTitle ==
Browse repository - {0}

== ConfigurationModeExplanationAutomatic ==
Directory update mode: Automatic

Automatic mode is set when all the children of a directory are selected.
Automatic means that all new files and directories will be downloaded during the update.

== ConfigurationModeExplanationManual ==
Directory update mode: Manual

Manual mode is set when not all the children of a directory are selected.
Manual means new files and directories won’t be automatically downloaded by the update.

== PendingDirectoryConflictsResolvingConflictsSingular ==
There is 1 directory conflict that must be resolved before resolving file conflicts.

== PendingDirectoryConflictsResolvingConflictsPlural ==
There are {0} directory conflicts that must be resolved before resolving file conflicts.

== PendingDirectoryConflictsApplyingChangesSingular ==
There is 1 directory conflict that must be resolved before applying changes.

== PendingDirectoryConflictsApplyingChangesPlural ==
There are {0} directory conflicts that must be resolved before applying changes.

== PendingDirectoryConflictsUpdatingWorkspaceSingular ==
There is 1 directory conflict that must be resolved before updating the workspace.

== PendingDirectoryConflictsUpdatingWorkspacePlural ==
There are {0} directory conflicts that must be resolved before updating the workspace.

== CreatedByColumn ==
Created by

== OwnerColumn ==
Owner

== UnresolvedXlink ==
unresolved xlink

== UnexpectedError ==
An unexpected error has occurred.

== SaveRequest ==
Confirm saving comments

== SaveRequestMessage ==
The comment of the current selected object was modified. Save the changes?

== ProvideDifferentWorkspaceName ==
Please provide a different workspace name or cancel the operation.

== ProvideDifferentRepositoryName ==
Please provide a different repository name or cancel the operation.

== ProvideDifferentItemName ==
Please provide a different item name or cancel the operation.

== ProvideDifferentBranchName ==
Please provide a different branch name or cancel the operation.

== ProvideDifferentLabelName ==
Please provide a different label name or cancel the operation.

== ProvideDifferentSyncViewName ==
Please provide a different sync view name or cancel the operation.

== ProvideDifferentItemNameForRenameResolution ==
Please provide a different item name.

== ProvideUserName ==
Please input an user name.

== ProvidePassword ==
Please input a password.

== ReplicationSource ==
Replication source

== ReplicationDestination ==
Replication destination

== PullingProgressTitle ==
Pulling branch {0}

== PushingProgressTitle ==
Pushing branch {0} to {1}

== CurrentOfTotalReplicationProgressTitle ==
   {0} of {1}

== PullBranch ==
Pull branch {0}

== PullRemoteBranch ==
Pull remote branch

== PushBranch ==
Push branch {0}

== ReplicateButton ==
Replicate

== GenericChooseButton ==
...

== ServerEmpty ==
The server field cannot be empty.

== RepositoryEmpty ==
The repository field cannot be empty.

== BranchEmpty ==
The branch field cannot be empty.

== OperationStarting ==
Operation starting

== FetchingMetadata ==
Fetching metadata

== PushingMetadata ==
Pushing metadata

== IntroducingData ==
Introducing data

== OperationStartingFetch ==
Operation starting fetch

== CalculatingInitialChangeset ==
Calculating initial changeset

== FetchingRevisions ==
Fetching revisions ({0})

== FetchingBranches ==
Fetching branches ({0})

== FetchingItems ==
Fetching items ({0})

== FetchingLabels ==
Fetching labels ({0})

== FetchingLinks ==
Fetching links ({0})

== FetchingChildren ==
Fetching children ({0})

== FetchingChangeSets ==
Fetching changesets ({0})

== FetchingMoveRealizations ==
Fetching move realizations ({0})

== FetchingReviews ==
Fetching reviews ({0})

== FetchingAttributes ==
Fetching attributes ({0})

== FetchingACLs ==
Fetching ACLs ({0})

== FetchingReferences ==
Fetching references ({0})

== FetchingSEIDs ==
Fetching SEIDs ({0})

== FetchingFinished ==
Sending metadata ({0})

== OperationStartingPush ==
Operation starting push

== ReconcilingObjects ==
Reconciling objects ({0})

== PushingReferences ==
Pushing references ({0} %)

== ReconcilingACLs ==
Reconciling ACLs ({0} %)

== PushingBranches ==
Pushing branches ({0} %)

== PushingItems ==
Pushing items ({0} %)

== PushingLinks ==
Pushing links ({0} %)

== PushingLabels ==
Pushing labels ({0} %)

== PushingAttributes ==
Pushing attributes ({0} %)

== ProcessingBranches ==
Processing branches ({0} %)

== PushingChangeSets ==
Pushing changesets ({0} %)

== PushingRevisions ==
Pushing revisions ({0} %)

== PushingMoveRealizations ==
Pushing move realizations ({0} %)

== PushingTrees ==
Pushing trees ({0} %)

== PushingACLs ==
Pushing ACLs ({0} %)

== PushingFinished ==
Pushing finished

== IntroducingDataProgress ==
Introducing data ({0} %)

== DataWritten ==
Data written

== OperationAbandoned ==
Operation abandoned

== OperationFinished ==
Operation finished

== ReplicationError ==
An error occurred during the replication: {0}

== UnknownStatus ==
Unknown status

== RetryReplicaButton ==
Retry

== SkipReplicaButton ==
Skip

== SourceReplicationServer ==
Source server

== SourceReplicationRepository ==
Source repository

== SourceReplicationBranch ==
Source branch

== DestinationReplicationServer ==
Destination server

== DestinationReplicationRepository ==
Destination repository

== MenuAddToIgnoreList ==
Add to ignored list

== MenuRemoveFromIgnoreList ==
Remove from ignored list

== MenuAddToCloakedList ==
Add to cloaked list

== MenuRemoveFromCloakedList ==
Remove from cloaked list

== MenuAddToHiddenChangesList ==
Add to hidden changes list

== MenuRemoveFromHiddenChangesList ==
Remove from hidden changes list

== UsingItemExtension ==
Using the item extension

== UsingItemFullPath ==
Using the item full path

== UsingItemName ==
Using the item name

== FilterRulesConfirmationTitle ==
Filter rules confirmation

== FilterRulesConfirmationAddMessage ==
The following rules will be added to the configuration file. Continue?

== FilterRulesConfirmationRemoveMessage ==
The following rules will be removed from the configuration file. Continue?

== ApplyRulesToAllWorkspaceCheckButton ==
Apply rules to all my workspaces

== ShelveWithPendingMergeLinksRequest ==
Shelve with pending merge links

== ShelveWithPendingMergeLinksRequestMessage ==
You have pending merge links in the workspace. The shelve operation doesn't shelve pending merge links. Do you want to shelve anyway?

== UndoChangesAfterShelveRequest ==
Undo changes after shelve

== UndoChangesAfterShelveRequestMessage ==
Do you want to keep or delete (undo) these changes from the pending changes list after shelving?

== UndoChangesAfterShelve ==
Undo changes

== KeepChangesAfterShelve ==
Keep changes

== AskAlwaysAfterShelve ==
Always ask me

== UndoChangesAfterShelveExplanation ==
Selected changes will be deleted from the pending changes list after shelving

== KeepChangesAfterShelveExplanation ==
Selected changes will be kept in the pending changes list after shelving

== AskAlwaysAfterShelveExplanation ==
Always ask me what to do before creating the shelve

== UndoChangesAfterShelveDefaultBehavior ==
Behavior for pending changes after shelving

== ShelveSettingsHint ==
You can change the shelve settings from the main {0} menu under {1}

== ShelveSettingsHintPreferences ==
preferences

== ShelveCreatedMessage ==
The changes are shelved under {0}

== NewCertificateTitle ==
Server SSL certificate is not in the store

== NewCertificateMessageUnityVCS ==
The server you are connecting to has sent a certificate which is not in the store. This is normal if it is the first time connecting to this server.

            Certificate details:
            - Issued to: {0}
            - Issued by: {1}
            - Expiration date: {3}
            - Certificate hash: {5}

            If you trust this host, click "Yes" to add the key to the Unity Version Control key store (recommended if it is the first time connecting to this server).
            If you want to carry on connecting just once, without adding the key to the store, click "No".
            If you do not trust this host, click "Cancel" to abandon the connection.

== ExistingCertificateChangedTitle ==
Server certificate has changed!

== ExistingCertificateChangedMessageUnityVCS ==
WARNING: This host certificate does NOT match the one you have in the key store. It may mean that the server identity has been compromised or a man-in-the-middle attack is being performed on you.

            Certificate details:
            - Issued to: {0}
            - Issued by: {1}
            - Expiration date: {3}
            - Certificate hash: {5}

            If you were expecting this change and trust the new certificate, click "Yes" to add the key to the Unity Version Control key store and trust this certificate in the future.
            If you want to carry on connecting just once, without adding the key to the store, click "No".
            If you want to abandon this connection, click "Cancel". This is the ONLY guaranteed safe choice.

== InvalidCertificateHostnameTitle ==
Hostname mismatch in secure connection

== InvalidCertificateHostnameMessage ==
WARNING: The hostname provided in the server certificate does NOT match the server hostname. This means that the certificate was not issued to this hostname or that there is a network configuration problem with this host.

            - Certificate hostname: {0}
            - Server hostname: {1}

            If you want to continue connecting to this host, click "Yes". The certificate validation will continue (not recommended).
            If you want to abandon the connection, click "No" (recommended).

== RepositoryExplorerServerLabel ==
Server:

== RetrievingServerProjects ==
Retrieving server projects...

== NoServerProjectsFound ==
No server projects were found

== ErrorRetrievingServerProjects ==
Could not retrieve the server projects

== ShowUndeleteButton ==
Deleted items

== UndeleteButtonTooltip ==
Use it to recover deleted files

== UndeleteViewTitle ==
Deleted items

== UndeleteMenuItemUndeleteRevision ==
Undelete revision

== UndeleteMenuItemUndeleteRevisionToThisPath ==
Undelete revision to this path...

== UndeletingItem ==
Undeleting item...

== UndeletingItemsSingular ==
Undeleting 1 item...

== UndeletingItemsPlural ==
Undeleting {0} items...

== EnterRestorePathFormTitle ==
Undelete item

== EnterRestorePathItemFormTitle ==
Undelete item: {0}

== EnterRestorePathFormParentDoesNotExistExplanation ==
The parent directory of the item does not exist, please enter a new location

== EnterRestorePathFormIsPathAlreadyUsedExplanation ==
There is already an item in the same path, please enter a new location

== EnterRestorePathFormTextBoxExplanation ==
Enter the restore path for the element to be undeleted

== EnterRestorePathFormLabel ==
Path:

== EnterValidRestorePath ==
Please enter a valid workspace path

== RestorePathShouldNotExist ==
The restore path should not exist

== RestorePathParentShouldExist ==
The parent directory of the restore path should exist

== DeletedItemsOwner ==
Owner:

== DeletedItemsSince ==
Since:

== MeOwnerOption ==
me

== EveryoneOwnerOption ==
everyone

== ItemUndeletedCorrectly ==
Undeleted ok: '{0}'

== ItemCannotBeUndeleted ==
Item '{0}' (ID: {1}) could not be undeleted because it was not found in its parent directory revision (ID: {2})

== CheckinViewTitle ==
Checkin changes

== UserDefinedSyncView ==
User defined sync view:

== AddSyncViewButton ==
Add

== RenameSyncViewButton ==
Rename

== DeleteSyncViewButton ==
Delete

== SyncViewDetailsTitle ==
Sync view details

== AddSrcSyncViewRepoButton ==
Add src repo

== AddSrcSyncViewRepoButtonTooltip ==
Add an existing repository to this sync view as replication source

== RemoveSrcSyncViewRepoButton ==
Remove src repo

== RemoveSrcSyncViewRepoButtonTooltip ==
Remove the selected repository from this sync view

== AddDstSyncViewRepoButton ==
Add dst repo

== AddDstSyncViewRepoButtonTooltip ==
Add an existing repository to this sync view as replication destination

== RemoveDstSyncViewRepoButton ==
Remove dst repo

== RemoveDstSyncViewRepoButtonTooltip ==
Remove the selected repository from this sync view

== PushVisibleButton ==
Push visible

== PushVisibleButtonTooltip ==
Push all visible outgoing branches

== PullVisibleButton ==
Pull visible

== PullVisibleButtonTooltip ==
Pull all visible incoming branches

== ShowExcludedBranchesCheckButton ==
Show excluded branches

== NewSyncView ==
New sync view

== NewSyncViewName ==
Sync view name

== NewSyncViewTitle ==
Create a new Sync View to push/pull groups of branches

== NewSyncViewTitleCloud ==
Create a new Sync View to push/pull groups of branches to Unity VCS Cloud

== NewSyncViewExplanation ==
Instead of pushing/pulling one by one, with Sync Views you can easily replicate branches in batches

== NewSyncViewCheckingRepositories ==
Checking repositories...

== NewSyncViewNoSourceRepository ==
There is no {0} repository to use as source

== NewSyncViewNoDestinationRepository ==
There is no {0} repository to use as destination

== NewSyncViewSourceRepository ==
Source repository

== NewSyncViewDestinationRepository ==
Destination repository

== NewSyncViewEnterSyncViewName ==
Please enter a sync view name

== NewSyncViewEnterRepositorySpec ==
Please enter a repository spec

== NewSyncViewInvalidRepositorySpec ==
Invalid repository spec: {0}

== NewSyncViewCheckingSourceRepositoryExists ==
Checking source repository exists...

== NewSyncViewCheckingDestinationRepositoryExists ==
Checking destination repository exists...

== NewSyncViewSourceRepositoryNotExist ==
Source repository {0} does not exist

== NewSyncViewDestinationRepositoryNotExist ==
Destination repository {0} does not exist

== NewSyncViewCreateSyncView ==
Create new sync view (push/pull)

== SyncStatusLoading ==
Loading...

== SyncStatusUpToDate ==
All branches are up to date

== SyncStatusError ==
Error: {0}

== OutgoingBranchesSingular ==
Outgoing branches - 1 branch

== OutgoingBranchesPlural ==
Outgoing branches - {0} branches

== IncomingBranchesSingular ==
Incoming branches - 1 branch

== IncomingBranchesPlural ==
Incoming branches - {0} branches

== ExcludedBranchesSingular ==
Excluded branches - 1 branch

== ExcludedBranchesPlural ==
Excluded branches - {0} branches

== SyncMenuItemSyncAllMenuItem ==
Synchronize all

== SyncMenuItemPushAllMenuItem ==
Push all outgoing branches

== SyncMenuItemPullAllMenuItem ==
Pull all incoming branches

== SyncMenuItemRefreshReplicationTarget ==
Refresh

== SyncMenuItemPushBranch ==
Push branch

== SyncMenuItemPullBranch ==
Pull branch

== SyncMenuItemExcludeBranch ==
Exclude branches

== SyncMenuItemIncludeBranch ==
Include branches

== CantShowBinaryContent ==
This file is detected as binary. Can't show file content; the file metadata is displayed instead. You can right click it and use "Open" to see its content. Check the online doc for more info.

== CantShowBinaryDiff ==
This file is detected as binary. No diffs are calculated; the file metadata is displayed instead. You can right click it and use "Diff" to launch an external diff viewer (must be configured). Check the online doc for more info.

== Content ==
Content of {0}

== UpdateMergeExplanation ==
New changesets were detected in the repository; you can update to/merge from the latest changeset.

== ViewNewChangesButton ==
View new changes

== RetrievingBranchHeads ==
Retrieving branch heads...

== RetrievingNewChanges ==
Retrieving new changes...

== RetrievingObjectInfo ==
Retrieving object info...

== WorkspaceRevisionKey ==
Workspace Revision

== MergeToDiffWithDst ==
Revision on destination branch

== EncryptionConfiguration ==
Server encryption

== RetypePassword ==
Retype Password:

== EncryptionConfigurationExplanation ==
The server '{0}' requires data encryption.
            Your client is not yet configured to handle encrypted data from '{0}'.

== EncryptionConfigurationEnterPassword ==
Enter a password to generate the encryption key:

== EncryptionConfigurationRemarks ==
Remarks:
            * Be very careful with the encryption password; it's the only way to decrypt your data on server '{0}'.
            * If you're decrypting data and you enter a wrong password, the data will fail to decrypt and you will have to edit cryptedservers.conf on your client to fix it.
            * Be careful to use the same password (encryption key) company-wide to work with '{0}'. Otherwise, data will be encrypted differently in each client.

== InvalidEmptyPassword ==
Password cannot be empty

== PasswordDoesntMatch ==
Passwords don't match

== ApplyActionForNextConflictsCheckButtonSingular ==
Apply this action for the next 1 conflict.

== ApplyActionForNextConflictsCheckButtonPlural ==
Apply this action for the next {0} conflicts.

== FsProtectionChanged ==
Filesystem permissions changed from {0} to {1}.

== CreateXlinkTitle ==
Create Xlink

== CreateXlinkExplanation ==
An Xlink is a directory that links to a changeset in a different repository. It's the way to load several repositories in a workspace.

== XlinkTargetServerEntry ==
Target server, choose or type one

== XlinkTargetServerExplanation ==
server:port format, i.e: myserver:8087

== XlinkTargetRepositoryEntry ==
Target repository

== XlinkTargetChangesetEntry ==
Target changeset

== XlinkNameEntry ==
Xlink name (name of the directory entry)

== XlinkHint ==
Remember that the xlinked contents will not be downloaded until you check in the xlink and update your workspace

== SelectARepository ==
You need to select a repository before selecting a changeset inside it

== MainMainRuleCreated ==
You're creating a XLink to the main branch at the target repository.
            Most likely you're looking to link main branches between the two repositories, so an expansion rule will be created for that matter.
            If this layout is not what you expected, please edit the expansion rules manually.

== XlinkOptions ==
Xlink options

== WritableXlinkCheckButton ==
Writable (otherwise this part of the code tree will be readonly)

== RelativeServerCheckButton ==
Use relative server - the Xlink tries to get its repository from the local server (recommended if repositories are replicated)

== BranchExpansionRules ==
Branch expansion rules

== BranchExpansionRulesExplanation ==
These rules define how source and destination branches are linked. For writable Xlinks you can define a link between main@src and main/fix@dst.

== SelectARepositoryForRule ==
You need to select a repository before creating a rule

== AddExpansionRuleTitle ==
Add expansion rule

== AddExpansionRuleExplanation ==
Define how the branches are expanded between xlinked repositories

== SourceBranch ==
Source branch

== DestinationBranch ==
Destination branch

== IsDefinedByUser ==
Is defined by user

== Yes ==
Yes

== No ==
No

== AlreadyDefinedRuleForBranch ==
You have already defined a rule for this source branch

== XlinkNoChangesetSelected ==
No changeset has been selected as the target of the Xlink

== EmptyXlinkName ==
The Xlink directory name is empty

== NoRulesDefined ==
You have not defined branch expansion rules for this xlink, are you sure you want to continue?

== NoRulesDefinedCaption ==
No rules defined

== EditXlinkTitle ==
Edit Xlink

== EditButton ==
Edit

== EmptySourceBranch ==
The source branch field cannot be empty.

== EmptyDestinationBranch ==
The destination branch field cannot be empty.

== LoadingXlinkData ==
Loading xlink data...

== CreatingDefaultRule ==
Creating default rule...

== NoContentToCompare ==
There is no content to compare.

== CreateBranchManualMode ==
Manual

== CreateBranchTaskMode ==
From task

== LoadingIssueTrackerExtension ==
Loading issue tracker extension...

== MarkingTaskAsOpen ==
Marking '{0}' task as open in '{1}'

== PendingTasks ==
Pending tasks

== RetrievingTasks ==
Retrieving pending tasks...

== RetrievingUserTasks ==
Retrieving pending tasks for user '{0}'...

== DisplayPendingTasksFromAllUsers ==
Display pending tasks from all users

== MarkAsOpenInIssueTracker ==
Mark as open in issue tracker

== LinkedTasksViewTitle ==
Linked tasks

== PlasticTaskIdProperty ==
ID:

== PlasticTaskOwnerProperty ==
Owner:

== PlasticTaskStatusProperty ==
Status:

== PlasticTaskTitleProperty ==
Title:

== PlasticTaskDescriptionProperty ==
Description:

== DeleteTaskTooltip ==
Delete this issue

== LoadingLinkedTasks ==
Loading tasks to be linked in the issue tracker...

== AddTaskTooltip ==
Add new issue

== SelectTasksToBeLinkedDialogTitle ==
Select issues from {0}

== SelectTasksToBeLinkedExplanation ==
Choose issues to be linked to the new changeset

== TaskIdEntryTitle ==
Issue IDs:

== ManualTaskIdEntryExplanation ==
You can manually enter a comma-separated issue ID list if you can't find yours above

== UnknownTaskIdsInManualEntry ==
Invalid issue IDs were found in the manual entry field.

== OpenTaskInBrowserTooltip ==
Open task info page in a web browser

== DoNotCheckinChangesJustMove ==
Do not check in the changes, just move them to the specified branch.

== CreateANewBrachRadioButton ==
Create a new branch

== CreateANewBranchToCheckinExplanationLabel ==
Create a child branch of {0} and check in your workspace changes to that branch.

== CreateANewBranchToMoveChangesetExplanationLabel ==
Create a child branch of {0} to move changeset cs:{1}.

== CreateANewBranchLabel ==
Name for child branch

== SelectExistingBranchRadioButton ==
Select an existing branch (must be empty)

== SelectExistingBranchExplanationLabel1 ==
Select a previously created branch and check in your workspaces changes to that branch.

== SelectExistingBranchExplanationLabel2 ==
The branch base of the selected branch will be changed to be your current base

== SelectExistingBranchToMoveExplanationLabel ==
Select a previously created branch and move the selected changesets inside it.

== SelectExistingBranchLabel ==
Selected branch

== MoveChangesNoBranchName ==
Please specify a child branch name

== MoveChangesSelectABranch ==
Please select a branch

== MoveChangesBranchMustBeEmpty ==
The selected branch must be empty

== CantUpdateBraseChangesetBranchWithRevisions ==
Can't update base changeset for branch because it must be empty

== RetrievingWorskpaceInfo ==
Retrieving workspace info

== MovingChangesToTargetBranch ==
Moving changes to target branch...

== UpdatingTargetBranchBase ==
Updating target branch base...

== OrganizationUserBelongsTo ==
Your organization is {0}

== OrganizationNotFoundLine1 ==
It seems you don't have an organization for {0}

== NameWorkingMode ==
User name from the computer

== NameIdWorkingMode ==
Name + user ID

== ADWorkingMode ==
Active Directory

== LDAPWorkingMode ==
LDAP

== UPWorkingMode ==
Built-in user and password

== SSOWorkingMode ==
Single Sign On

== OIDCWorkingMode ==
OpenID Connect

== RepositoryWillBeKnownAs ==
Will be known as {0}

== CantCreateLocalRepository ==
Can't create local repository

== CantCreateCloudRepository ==
Can't create cloud repository

== CantCreateWorkspace ==
Can't create workspace

== CantCreateSyncView ==
Can't create sync view

== CantRetrieveCloudOrganization ==
Can't retrieve your cloud organization

== SyncViewNameAlreadyExists ==
Sync view {0} already exists

== StartANewProject ==
Start a new project

== Distributed ==
Distributed

== JoinExistingProject ==
Join an existing project

== Centralized ==
Centralized

== WelcomeToCloudEdition ==
Welcome to Unity Version Control Cloud Edition!

== WelcomeToUnityVCS ==
Welcome to Unity VCS!

== WelcomeToGluonTeamEdition ==
Welcome to Gluon!

== WelcomeToGluonCloudEdition ==
Welcome to Gluon - Cloud Edition!

== TeamEditionFirstSteps1stCaption ==
You will be working on your local workspaces connected to your Unity VCS server

== TeamEditionFirstSteps2ndCaption ==
You can create different repositories for different projects, and normally one workspace for each repository

== CloudEditionFirstSteps1stCaption ==
You can work centralized or distributed

== CloudEditionFirstSteps2ndCaption ==
Centralized means no local repo

== CloudEditionFirstStepsChooseOption ==
Please choose one path to start working with Unity VCS Cloud

== TeamEditionFirstStepsChooseOption ==
Please choose any of the following options to start working with Unity Version Control

== TeamEditionFirstStepsJoinExistingProjectExplanation ==
Just select an existing repository on your Unity VCS server and create a new workspace to download it.

== CloudRepository ==
Cloud repository

== LocalRepositoryName ==
Local repository name

== NewRepositoryName ==
New repository name

== WorkspaceName ==
Workspace name

== PathOnDisk ==
Path on disk. The place where your files will be on your disk. If you already have a project on disk, you can select the existing path

== MakeWorkspaceDynamic ==
Make this workspace dynamic

== CreateSyncViewToPushPullCloud ==
Create a Sync View to push/pull changes to Cloud

== BrowseButton ==
Browse...

== CheckingLocalRepositoryAvailable ==
Checking cloud repository name is available...

== CheckingWorkspaceAvailable ==
Checking workspace is available...

== CheckingSyncViewNameAvailable ==
Checking sync view name is available...

== CreatingLocalRepository ==
Creating repository {0}@local...

== CreatingSyncView ==
Creating sync view...

== CloudEditionFirstStepsDistributedExplanation ==
This is the Git way of working, with a local repo and push/pull to a repo in the Cloud.

== CloudRepositoryDoNotExist ==
The repository {0} does not exist on your cloud organization

== CheckingCloudRepositoryExists ==
Checking cloud repository exists...

== CloudEditionFirstStepsCreateWorkspaceExplanation ==
Just your workspace connected to the cloud repo. If you are not sure, then this is your option.

== NoRepositoriesFound ==
No repositories found. Check that your Unity VCS server is running and that there are repositories on it

== SyncFromLocalToCloudViewName ==
Sync {0} from local to cloud

== SyncToRepository ==
Sync to {0}

== SyncFromRepository ==
Sync from {0}

== LocalServerDetected ==
Local server {0} detected

== LocalServerDetectedDvcsMode ==
Local server detected

== UseSsl ==
Use SSL

== TeamEditionFirstStepsJoinTeamExplanation ==
To start a new project, you can create a new repository in your Unity VCS server.

== ConfirmDeleteChangesetCaption ==
Confirm changeset delete

== DeleteChangesetWarning ==
Are you sure you want to delete the changeset:
{0}?

== CantDeleteChangesetCaption ==
Unable to delete the changeset

== CantDeleteChangeset ==
The current changeset cannot be deleted because it is referenced by another item.

Check that:
 * The changeset is not the source of a merge link
 * The changeset has no children
 * The changeset is not labelled
 * No shelves were created from the changeset

Note, you can find shelves created from a given changeset by running the following query from the cli or Shelves view:

    find shelve where parent=changesetspec

== CantDeleteChangesetInPendingMerge ==
The current changeset cannot be deleted because it is referenced by a pending merge link.

You will need to undo your pending changes before deleting this changeset.

== DeleteWorkingChangesetCaption ==
Delete working changeset

== DeleteWorkingChangeset ==
Deleting the changeset you are changing in your workspace configuration. Continue?

== CannotDeleteCsetWithPendingChanges ==
Cannot delete the selected changeset since there are pending changes. Please review the pending changes and retry the operation.

== CannotMoveCsetWithPendingChanges ==
Cannot move the selected changeset since there are pending changes. Please review the pending changes and retry the operation.

== ReportChanged ==
Changed items

== ReportAdded ==
Added items

== ReportDeleted ==
Deleted items

== ReportMoved ==
Moved items

== ReportLoaded ==
Loaded items

== ReportUnloaded ==
Unloaded items

== ShowUpdateReportButton ==
Show update report

== HideUpdateReportButton ==
Hide update report

== ReportUpdatedHeader ==
Updated

== ConfigurationAppliedHeader ==
Configuration applied

== ZoomIn ==
Zoom in

== ZoomOut ==
Zoom out

== RemoveFilter ==
Remove filter

== SelectedBranchesAndRelated ==
{0} and related branches

== SelectedBranchesAndPendingMerges ==
{0} and pending merges

== DownloadingRevisions ==
Downloading revisions...

== GoToSourceChangesetMenuItem ==
Go to source changeset

== GoToDestinationChangesetMenuItem ==
Go to destination changeset

== Name ==
Name:

== NewName ==
New name:

== NoCsetCreatedWarning ==
A new changeset was not created because the file data did not change

== AllFileConflictsWereMergedWarning ==
All files merged and are ready for check-in

== SomeFileConflictsWereMergedWarning ==
Only some files were merged and are ready for check-in

== NoFileConflictsWereMergedWarning ==
No files were merged

== SaveDiffChangesTitle ==
Do you want to save the changes you made to the current file?

== SaveDiffChangesMessage ==
Your changes will be lost if you don't save them

== DontSaveButton ==
Don't save

== SelectFileToSeeDifferences ==
Select a file to see its differences

== SaveThisRevisionAs ==
Save this revision as...

== SaveRevisionAs ==
Save revision as...

== SavingRevision ==
Saving revision to '{0}'...

== DownloadFilesProgress ==
Downloaded {0} of {1} ({2} of {3} files to download)

== DownloadInfoSingular ==
Will download 1 file {0}

== DownloadInfoPlural ==
Will download {0} files {1}

== NotifyLinuxWatchLimitWarning ==
The maximum number of watched items was reached. You can increase its value executing 'sudo sysctl fs.inotify.max_user_watches=524288'

== CheckinProgressMultiThreadUploading ==
Multi-thread check-in

== CheckinProgressMultiThreadNumOfBlocks ==
uploading {0} blocks in parallel

== SkipDiffMergeTracking ==
Skip merge tracking

== SkipDiffMergeTrackingTooltip ==
Do not group changes by merge source. Each file is displayed once, with its 'total' changes

== PendingToIntegrate ==
Show pending to integrate

== PendingToIntegrateTooltip ==
Shows the branch changes that are pending to be merged into its parent branch. It takes into account any rebase or prior merge already done from/to its parent branch

== FetchNoDataCheckButton ==
Do not replicate data (you need to be the admin of your server)

== FetchNoDataNoPermissions ==
You need to be the administrator of the destination server to use the 'do not replicate data' option.

== CheckingFetchNoDataNoPermissions ==
Checking permissions...

== CheckinProgressUploadingFiles ==
Uploading {0} files

== CheckinProgressUploadingFileData ==
Uploading file {0} ({1}) to the repository

== CheckinProgressOf ==
of

== ToggleDetails ==
Toggle details

== ShowDetails ==
Show details

== ShowLocks ==
Show locks

== ToggleDetailsButtonTooltip ==
Hide/show details area

== NoDiffToolDefined ==
There aren't any diff tools defined in the client configuration file

== History ==
History

== OpenButton ==
Open

== ApplyAttributesButton ==
Apply attributes...

== ApplyAttribute ==
Apply an attribute to the selected object

== ApplyAttributeExplanation ==
Select the attribute type to apply in the "Attribute" dropdown list; the "Value" field lets you choose an existing value from the drop down list or type a new value.

== AttributeValueLabel ==
Value:

== LoadingAttributes ==
Loading attributes...

== LoadingAttributeValues ==
Loading attribute values...

== SelectAttribute ==
Select an attribute

== SelectAttributeValue ==
Enter an attribute value

== ApplyingAttribute ==
Applying attribute...

== SettingAttributeValue ==
Setting new value...

== DeletingAttribute ==
Deleting attribute...

== AnnotateDays ==
days

== AnnotateLocalRevision ==
local

== AnnotateAuthorField ==
Author:

== AnnotateDateField ==
Date:

== AnnotateBranchField ==
Branch:

== AnnotateChangesetField ==
Changeset:

== AnnotateChangedDuringMerge ==
Changed during merge:

== CreateAttribute ==
Create a new attribute

== CreateAttributeExplanation ==
Type a name for the new attribute. It will be created in '{0}'

== AttributeNameLabel ==
Attribute name:

== AttributeCommentLabel ==
Comments:

== AttributeHelpLabel ==
Add a line using the format 'default: value1, "value two", ..., valueN' to define default values for this attribute.

== CreatingAttribute ==
Creating attribute...

== AttributeNameAlreadyExists ==
The attribute {0} already exists ({1})

== MergeToPendingFileConflicts ==
There are pending file conflicts that must be resolved before processing the merge-to.

== CommentsDialogTitle ==
Comments

== CommentsDialogExplanation ==
Enter a comment for merge-to check-in operation:

== BranchMergeTo ==
Server side merge – from branch {0} to branch {1}

== LabelMergeTo ==
Server side merge – from label {0} to branch {1}

== ChangesetMergeTo ==
Server side merge – from changeset cs:{0} to branch {1}

== CheckinMergeTo ==
Check in merge!

== MergeBranch ==
Merge from branch {0}

== ChangesetIntervalCherryPick ==
Cherry pick from changeset interval (cs:{0} - cs:{1}]

== BranchCherryPick ==
Cherry pick from branch {0}

== ChangesetCherryPick ==
Cherry pick from changeset cs:{0}

== ApplyShelve ==
Apply shelve sh:{0}

== LabelMerge ==
Merge from label {0}

== CherrypickSubtractiveMerge ==
Subtractive merge from changeset cs:{0}

== IntervalCherrypickSubtractiveMerge ==
Subtractive merge from changeset interval (from cs:{0} to cs:{1})

== ChangesetMerge ==
Merge from changeset cs:{0}

== PleaseTypeAPath ==
Please enter a path

== Copy ==
Copy

== AnnotateOptions ==
Annotate options

== DiffBranchMenuItem ==
Diff branch {0}

== DiffChangesetMenuItem ==
Diff changeset {0}

== AnnotateParentRevisionMenuItem ==
Annotate parent revision

== ShowSemanticHistoryMenuItem ==
Show semantic history

== AnnotateParentChangesetMenuItem ==
Annotate before these changes (parent of cs:{0})

== AnnotateShowDate ==
Show date modified

== AnnotateShowChangeset ==
Show changeset

== AnnotateShowBranch ==
Show branch

== AnnotateShowAuthor ==
Show author

== AnnotateShowLineAge ==
Show line age

== AnnotateShowRevInMerge ==
Show whether line was changed in a merge

== AnnotateDisplayShortBranchNames ==
Display short branch names

== AnnotateDisplayFullBranchNames ==
Display full branch names

== Shelve ==
Shelve

== AnnotateDialogTitle ==
Annotate

== AnnotateCantFindRevisionAtChangeset ==
Cannot find revision in changeset cs:{0}

== AnnotateNoPreviousRevision ==
There is no previous version for this item.

== AnnotateCantRunOnBinaryFiles ==
Binary files can not be annotated. If the file is actually plain text, you can change its revision type from the context menu in the Workspace Explorer or force it using the filetypes.conf configuration file.

== ChangesetsDynamicViewTitle ==
Changesets {0}

== UserProfileServerLabel ==
Server:

== UserProfileServerProfileNotFound ==
No connection profile found for server '{0}', please configure one.

== UserProfileWrongAuthMode ==
The user profile can only be configured using LDAP or User/Password authentication.

== UserProfileCloudServer ==
The user profile can't be configured for cloud servers.

== LoadingUserProfile ==
Loading user profile...

== NoProfileToShow ==
There is not defined profile to show

== SavingUserProfile ==
Saving user profile...

== EnterValidEmailAddress ==
Please enter a valid email address.

== EnterValidIntValue ==
Please enter a valid integer value.

== UserProfileLoadError ==
Cannot retrieve the profile from {0}. Details: {1}

== UserProfileSaveError ==
Cannot save the profile for {0}. Details: {1}

== PendingChangesOptionsDialogTitle ==
Pending Changes - Options

== PendingChangesWhatToFindTab ==
What to find

== PendingChangesWhatToShowTab ==
What to show

== PendingChangesVisualizationTab ==
Visualization

== PendingChangesMoveDetectionTab ==
Move detection

== PendingChangesWhatToFindDialogTitle ==
Configure how to find changes in the workspace

== PendingChangesWhatToFindDialogExplanation ==
You can finely tune how Unity VCS finds changes on disk and what to skip

== PendingChangesWhatToShowDialogTitle ==
Configure the types of changes to display

== PendingChangesWhatToShowDialogExplanation ==
From auto-refresh to deciding if you want to see private files, ignored and more

== PendingChangesMoveDetectionDialogTitle ==
Configure how Unity VCS finds moved files and directories in your workspace

== PendingChangesMoveDetectionDialogExplanation ==
It can detect files and directories that you moved or renamed on disk

== PendingChangesFilesystemWatcherEnabled ==
File system watcher is enabled

== PendingChangesFilesystemWatcherDisabled ==
File system watcher is disabled

== PendingChangesFilesystemWatcherEnabledExplanationUnityVCS ==
Unity Version Control watches the workspace for changes to make the operation faster. This removes the need to walk the directory structure, although move matching can still consume time.

== PendingChangesFilesystemWatcherDisabledExplanationUnityVCS ==
This is not normal and can affect performance. Unity Version Control watches the workspace for changes using the Windows API. This way it doesn't need to walk the directory structure. If it is disabled, the operation can be much slower. Contact support for help unless it was disabled in client.conf intentionally. [[HELP_URL|{0}]]

== PendingChangesShowCheckouts ==
Show checkouts

== PendingChangesShowCheckoutsExplanation ==
Shows files that you explicitly checkedout in Unity VCS. Also, files that you moved using Unity VCS GUIs or command line. And finally, files involved in a merge in progress.
Checkouts display super-fast because they are on a list; no need to walk the directory structure to find them.
Tip: With a workspace larger than 800k files, it may make sense to use only checkouts for faster operation. Checkouts are also needed if you use file locks.

== PendingChangesFindChanged ==
Find changed files in the workspace

== PendingChangesFindChangedExplanation ==
Walks the workspace directory structure to find changed files (timestamp changed).

== PendingChangesCheckFileContent ==
Check the content to determine files as changed, not just timestamp

== PendingChangesCheckFileContentExplanation ==
When the timestamp of a file is modified, calculate the hash with the previous before setting it as changed. Useful with applications that save files without performing any changes. Warning: It can make "pending changes" slower because it requires reading and hashing files.

== PendingChangesAutoRefresh ==
Auto-refresh

== PendingChangesAutoRefreshExplanation ==
Let Unity VCS refresh the Pending Changes list as needed to avoid manual refreshing.

== PendingChangesGroupInChangeLists ==
Group changes in "change lists"

== PendingChangesGroupInChangeListsExplanation ==
Unity VCS can group the changes in the workspace in user created lists. This is useful if you miss the feature from previous version control.

== PendingChangesGroupInCategories ==
Group changes by categories

== PendingChangesGroupInCategoriesExplanation ==
Unity VCS can group the changes in the workspace by categories like "changed", "added", "moved", "deleted". It is useful if you want to see the changes in a more organized way.

== PendingChangesViewAsATree ==
View as a tree

== PendingChangesViewAsATreeExplanation ==
Show the changes in the workspace in a tree view mode.

== PendingChangesShowPrivateFiles ==
Show private files

== PendingChangesShowPrivateFilesExplanation ==
List files that are not under source control. They can be recently added files, but sometimes are tool-generated content that you want to ignore. (Adding to "ignored" can be a better option.)

== PendingChangesAlwaysSelectPrivateFiles ==
Always select private files

== PendingChangesAlwaysSelectPrivateFilesExplanation ==
This way you won't forget new files on check-in. But you need to have a good ignore.conf to avoid adding temporary files to version control.

== PendingChangesShowIgnoredFiles ==
Show ignored files

== PendingChangesShowIgnoredFilesExplanation ==
Show files that you previously ignored by adding them to ignore.conf.

== PendingChangesShowCloakedFiles ==
Show cloaked files

== PendingChangesShowCloakedFilesExplanation ==
Show files that you previously cloaked by adding them to cloaked.conf.

== PendingChangesShowHiddenFiles ==
Show hidden files

== PendingChangesShowHiddenFilesExplanation ==
Show files that you previously hid by adding them to hidden_changes.conf.

== PendingChangesShowDeletedFiles ==
Show deleted files and directories

== PendingChangesShowDeletedFilesExplanation ==
Show files that you deleted manually in the workspace outside of Unity VCS control. (Not using a GUI or a command, but simply deleting them from disk.)

== PendingChangesMoveDetectionExplanation ==
You can tell Unity VCS that you moved a file using "cm mv" or moving files from the GUIs or IDE integrations. This method doesn't require "detection". But, if you move files directly on disk, Unity VCS can still detect the operation. It applies to directories too.

== PendingChangesFindMovedFiles ==
Find moved and renamed files and directories

== PendingChangesFindMovedFilesExplanation ==
Walks the workspace finding possible moves. For example, if you rename foo.c to bar.c, Unity VCS will find bar.c as added, foo.c as deleted, and it will try to match them. It can be slow if you have lots of private files.

== PendingChangesMoveDetectionFineTune ==
You can fine tune how "find moves" works:

== PendingChangesMatchBinarySameExtension ==
Match binary files only when they have the same extension

== PendingChangesMatchBinarySameExtensionExplanation ==
Doesn't try to match a .png with a .jpg. But it won't find a .doc to .docx rename.

== PendingChangesMatchTextSameExtension ==
Match text files only when they have the same extension

== PendingChangesMatchTextSameExtensionExplanation ==
Doesn't try to match a .cs with a .c. It can be useful in some circumstances.

== PendingChangesSimilarityPercentage ==
Similarity percentage

== PendingChangesSimilarityPercentageExplanation ==
Defines "how similar" the comparison needs to be for two files to be detected as moved or renamed. For example, if you move foo.c to bar.c and modify it later, the % defines how similar they need to be so that Unity VCS considers them the same file.
It applies to directories too: how similar the directory structure needs to be: how many moved children relative to the total of directory entries.
For binaries, the % means the allowed difference in size.

== PendingChangesINotifyEnabled ==
inotify change watcher is enabled

== PendingChangesINotifyDisabled ==
inotify change watcher is disabled

== PendingChangesINotifyEnabledExplanation ==
Unity VCS watches the workspace for changes to make the operation faster. This removes the need to walk the directory structure, although move matching can still consume time. Unity VCS takes advantage of inotify, but it needs some tuning to work correctly. Check: {0}

== PendingChangesINotifyDisabledExplanation ==
Pending changes performance improves greatly when it can take advantage of the inotify-based detection system. Check the following instructions to enable it: {0} and restart the GUI

== ProjectSettingsAutomaticAdd ==
Automatically add new files to source control

== ProjectSettingsAutomaticAddExplanation ==
Let Unity VCS add new files to source control automatically.

== MergeToMergeNeededExplanation ==
While the merge-to operation was running, new changesets were created in the destination of the merge-to operation, creating more than one head. Consider unifying them by running a new merge (or merge-to) operation. Affected branches:

== MergeToMergeNeededBranch ==
{0}@{1}@{2} (mount '{3}')

== MergeToNeededQuestion ==
A new changeset has been created at branch {0}@{1}@{2} (mount '{3}') during the merge-to operation. You need to merge again to complete the operation. Do you want to merge again now?

== DontShowItAgain ==
Got it, don't show me again

== LearnMore ==
Learn more

== HelpActionNotFound ==
Sorry we forgot to add the action :(

== RemovedLocallyStatus ==
Removed locally

== RemovedStatus ==
Removed

== MovedLocallyStatus ==
Moved locally

== MovedStatus ==
Moved

== AddedStatus ==
Added

== IgnoredStatus ==
Ignored

== PrivateStatus ==
Private

== ReplacedStatus ==
Replaced

== CopiedStatus ==
Copied (new)

== CheckedOutStatus ==
Checked-out

== ChangedStatus ==
Changed

== HiddenChangedStatus ==
Hidden changed

== UnsupportedImageFormat ==
Unsupported image format

== ShowPropertiesButton ==
Show properties

== PreviewConfigAlreadyExist ==
Another configuration exists with this name, please choose other name

== UserDefinedPreview ==
User-defined tool

== ConfirmDeleteDiffTool ==
Do you want to delete this diff tool configuration?

== ConfirmDeleteMergeTool ==
Do you want to delete this merge tool configuration?

== CloudEditionConfigInsertValidEmail ==
Enter a valid email address, or username

== CloudEditionConfigInsertValidPassword ==
Enter a valid password

== CloudEditionConfigurationWelcome ==
Welcome to Cloud Edition!

== CloudEditionConfigurationExplanation ==
Please enter the user and password you use to connect to Unity VCS Cloud (same as plasticscm.com). This way you won't be prompted again when replicating to Cloud.

== CloudEditionConfigurationExtraExplanation ==
This is also the user name that will identify you each time you check in, so choose wisely.

== CloudEditionConfigCheckingCredentials ==
Checking credentials...

== CloudEditionConfigEmail ==
User name (typically your plasticscm.com email)

== CloudEditionConfigPassword ==
Password

== CloudEditionConfigCheckCredentials ==
Check credentials

== SelectFieldsMergeDiffOptions ==
Fill in all Merge/Diff option fields

== SelectFieldsMergeDiffOptionsCaption ==
Fill in all fields

== EOLMethod ==
Ignore EOLs

== WhitespacesMethod ==
Ignore whitespaces

== EOLWhitespacesMethod ==
Ignore EOLs and whitespaces

== NoneMethod ==
Recognize all

== DiffVariables ==
You can use the following command-line parameters when invoking the external diff tool.
It's up to you to ensure that the specified files have the contents of the revisions to be compared.

@sourcefile: full path of file whose contents will be displayed in left panel
@destinationfile: full path of file whose contents will be displayed in right panel

@sourcesymbolic: "left file" string to be displayed in control panel
@destinationsymbolic: "right file" string to be displayed in control panel

@sourcehash: MD5 string calculated from @leftrev file contents
@destinationhash: MD5 string calculated from @rightrev file contents

@filetype: MIME type, used for syntax highlighting
@comparationmethod: comparison method used
@fileencoding: default file encoding to use when no encoding is detected

Examples:

  Semantic Merge Tool:
  https://semanticmerge.com/documentation/how-to-configure/semanticmerge-configuration-guide#ConfiguringSemanticMergeastheDiffTool13

    <semanticmerge-path>\semanticmergetool.exe -s="@sourcefile"
      -sn="@sourcesymbolic" -d="@destinationfile" -dn="@destinationsymbolic" -i="@comparationmethod"
      -e="@fileencoding" -edt=default

  Kdiff3
  http://kdiff3.sourceforge.net/doc/documentation.html

    <kdiff3-path>\kdiff3.exe" "@sourcefile" "@destinationfile" --L2 "@sourcesymbolic" --L3 "@destinationsymbolic"

  Araxis Merge
  https://www.araxis.com/merge/documentation-windows/command-line.en

    <araxis-path>\compare.exe" /title1:"@sourcesymbolic" /title2:"@destinationsymbolic" "@sourcefile" "@destinationfile"

== NoDiffType ==
Specify the file type that will be processed by this diff tool.

== NoDiffTool ==
Specify a command line for this diff tool.

== DiffToolError ==
Diff tool error

== DiffNoExt ==
No suffix specified for the diff tool.

== NoTxtDiff ==
No diff tool specified for text files.

== NoBinDiff ==
No diff tool specified for binary files.

== NoDiffForExt ==
No diff tool specified for the {0}.

== DiffToolsError ==
Diff tools error

== NoDiscoveredServers ==
No servers were found.

== DownloadPreviewSoftware ==
You need to install [[{0}|{1}]] to generate previews with this provider

== ExecutableOpenFileDialogFilterWin ==
Executable files (*.exe; *.bat;)|*.exe; *.bat|All files (*.*)|*.*

== ExecutableOpenFileDialogFilterNoWin ==
All files (*.*)|*.*

== ValidatePreviewTypeName ==
Please type a name

== ValidatePreviewAddExtension ==
Please add at least one supported extension

== NullRefText ==
An error occurred processing your request.

== PathTooLong ==
The path is too long

== ServerNullRefText ==
An unexpected error has occurred on server. For more information check the server log.

== MsgInformative ==
Information

== MsgWarning ==
Warning

== MsgError ==
Error

== MergeVariables ==
The following variables are available to use as arguments when invoking the external merge tool in the command line:

@sourcefile: full path of the source merge file
@destinationfile: path of the destination merge file
@basefile: path of the common ancestor merge file

@output: path of the merge result file

@sourcesymbolic: symbolic name of the source contributor
@destinationsymbolic: symbolic name of the destination contributor
@basesymbolic: symbolic name of the common ancestor contributor

@sourcehash: MD5 string calculated from the source file content
@destinationhash: MD5 string calculated from the destination file content
@basehash: MD5 string calculated from the common ancestor file content

@filetype: MIME type used for the syntax highlight
@comparationmethod: comparison method used
@fileencoding: default file encoding to use when no encoding is detected
@resultencoding: result file encoding (NONE -> it will be calculated from contributors encodings)
@mergetype: type of merge used
@progress: a progress string indicating the current operation, for example Merging file 3/10
@extrainfofile: path of a file that contains extra information about the current merge

Examples:

  Semantic Merge Tool:
  https://semanticmerge.com/documentation/how-to-configure/semanticmerge-configuration-guide#ConfiguringSemanticMergeastheMergeTool15

    <semanticmerge-path>\semanticmergetool.exe -b="@basefile" -bn="@basesymbolic" -s="@sourcefile" -sn="@sourcesymbolic"
      -d="@destinationfile"  -dn="@destinationsymbolic" -r="@output" -i="@comparationmethod"
      -e="@fileencoding" -emt=default -edt=default

  Kdiff3:
  http://kdiff3.sourceforge.net/doc/documentation.html

    <kdiff3-path>\kdiff3.exe" "@basefile" "@sourcefile" "@destinationfile"
      -o @output --L1 "@basesymbolic"  --L2 "@sourcesymbolic" --L3 "@destinationsymbolic"

  Araxis Merge:
  https://www.araxis.com/merge/documentation-windows/command-line.en

    <araxis-path>\compare.exe" /wait /a1 /3 /title1:"@basesymbolic"
      /title2:"@sourcesymbolic" /title3:"@destinationsymbolic" "@basefile" "@sourcefile"
      "@destinationfile" "@output"

== NoMergeType ==
Specify the file type that will be processed by this merge tool.

== NoMergeTool ==
Specify the command line for this merge tool.

== MergeToolError ==
Merge tool error

== MergeNoExt ==
No suffix specified for the merge tool.

== NoTxtMerge ==
No suffix specified for the merge tool.

== NoBinMerge ==
No merge tool specified for binary files.

== NoMergeForExt ==
No merge tool specified for the {0}.

== MergeToolsError ==
Merge tools error

== ConfirmDeletePreviewProvider ==
Are you sure you want to delete the selected preview tool?

== CreateNewProfileTitle ==
Add a new profile to connect to a server

== EditProfileTitle ==
Edit an existing server connection profile

== ProfileNameInputLabel ==
Give a name to the profile

== ProfileConnectionTypeLabel ==
Select the connection type

== ProfileConnOnDemand ==
On demand

== ProfileConnOnDemandTooltip ==
The profile will be only used when a connection to the stored server is established.

== ProfileConnAuto ==
Automatic

== ProfileConnAutoTooltip ==
The stored server will show up in the repositories view list. Use this options for always-reachable servers.

== ProfileDialogExplanation ==
A connection profile enables you to store remote servers authentication parameters.

== ProfileNameEmpty ==
Profile name cannot be empty.

== ProfileNameAlreadyExists ==
The profile name already exists

== DeleteProfileConfirmation ==
You are going to delete the selected profile. Continue?

== DeleteProfileCaption ==
Delete profile

== NotValidProxyServer ==
The specified server {0} is not a valid Proxy server.

== ConnectedOk ==
Connected OK

== ConfigurationUserNameInfo ==
The following username will be used:

== ConfigurationCredentialsInfo ==
Configure your credentials

== PlasticConfiguration ==
Unity DevOps Version Control Configuration

== ConfigurationServerInfo ==
Enter the name (or IP address) and port of your Unity VCS server. You can use "scan network" to find available Unity VCS servers on your network

== ConfigureProxy ==
Use a Proxy server

== SwitchModeConfirmationDialogTitle ==
Switch mode confirmation

== SwitchModeConfirmationDialogExplanation ==
You are in {0} mode. Do you really want to switch to {1} mode?

Learn more about Gluon here:

== DeveloperMode ==
Developer

== GluonMode ==
Gluon

== ReloadButton ==
Reload

== NotConnectedTryingToReconnect ==
Not connected. Trying to re-connect...

== TryNowButton ==
Try now

== SearchTooltip ==
Search

== CannotLaunchInProgressMergeFromNotRootMountPoint ==
Can't continue with this merge. The merge in progress takes place in a xlinked repository. This option is only available when the merge in progress takes place in the repository of the root workspace. Please launch the merge manually to continue.

== OneVersionBehind ==
you are 1 version behind

== TwoOrMoreVersionsBehind ==
you are {0} versions behind

== GitSyncAllRefsRejected ==
All sent references (branches or tags) were rejected by the server, so nothing was pushed. Check the configuration of your Git server provider for more info. Failed to push:{0}

== LoggingIn ==
Logging in as '{0}'...

== SigningUp ==
Creating account '{0}'...

== GettingOrganizationsCompatibleWithAuthProvider ==
Getting organizations compatible with '{0}'...

== GettingDatacenters ==
Getting datacenters ...

== EnterOrganizationName ==
Please enter a organization name

== NoDatacenterSelected ==
Please select a datacenter

== CreatingOrganization ==
Creating organization '{0}'...

== OrganizationNameTooShort ==
Organization name must be at least three characters long

== SignUp ==
Sign up

== SignUpNeeded ==
Can't find the account, please create a Unity VCS account {0}

== SignUpNeededNoArgs ==
Can't find the account, please create a Unity VCS account.

== SignUpButton ==
Sign up

== SignIn ==
Sign in

== SignInToUnityVersionControl ==
Sign in to Unity Version Control

== SignInToUnityVCS ==
Sign in to Unity VCS

== SignInWithGoogle ==
Sign in with Google

== SignInWithUnityID ==
Sign in with Unity ID

== SignInWithEmail ==
Sign in with Email

== SignInWith ==
Sign in with {0}

== SignInWithOIDC ==
Sign in with OpenID

== SignInWithSSO ==
Sign in with SSO

== ConfirmPassword ==
Confirm password:

== SignedUpTitle ==
You correctly signed up to plasticscm.com!

== LoggedInTitle ==
You correctly logged in to plasticscm.com!

== CreateFirstOrganizationLabel ==
Now it is time to create your Cloud Organization

== CreateOtherOrganizationLabel ==
Or maybe you prefer to create your own

== JoinOrganizationTitle ==
It's time to join an organization!

== YouBelongToOrganization ==
You belong to the organization: {0}

== YouBelongToSeveralOrganizations ==
You belong to several organizations:

== JoinButton ==
Join

== CreateOrganizationTitle ==
Create your cloud organization

== OrganizationName ==
Organization name:

== Datacenter ==
Datacenter:

== DatacenterExplanation ==
The closest is already selected :)

== EncryptionCheckButton ==
Encrypt all data sent to Unity VCS Cloud

== EncryptionCheckButtonExplanation ==
You can force that all data sent to Unity VCS Cloud is encrypted before leaving your computers. This way you ensure your data can't be compromised. If you select this option, all the organization members will have to share the same secret key to encrypt/decrypt data.
{0}

== CreatedOrganizationTitle ==
Organization created correctly

== CreatedOrganizationExplanation ==
The organization {0} was created correctly

== ContinueButton ==
Continue

== RetryButton ==
Retry

== CloudEditionOrganization ==
You are part of the organization {0}

== SignUpAgreeToShort ==
By clicking 'Sign up', you agree to our {0} and {1}

== SignInAgreeToShort ==
By clicking 'Sign in', you agree to our {0} and {1}

== TermsOfService ==
Terms of Service

== PrivacyPolicy ==
Privacy Policy

== LoginWithYourMail ==
or login with your email address

== Email ==
Email:

== BackButton ==
< Back

== LastThingTitle ==
One last thing

== LastThingExplanation ==
There are two Unity VCS flavors.
You can use both, anytime.

== PlasticForDevelopersTitle ==
Unity VCS for developers

== PlasticForDevelopersExplanation ==
The classic Unity VCS GUI, good for branches, merges and push/pull

== PlasticForArtistsTitle ==
Gluon - Unity VCS for artists

== PlasticForArtistsExplanation ==
Super simple GUI for artists in game dev, or anyone who prefers to simply check in and forget

== LaunchButton ==
Launch

== UpgradePlan ==
Upgrade Plan

== ReplyButton ==
Reply

== ReplyPlaceHolder ==
Reply...

== CommentButton ==
Comment

== DiscardUnsavedChangesTitle ==
Discard changes

== DiscardUnsavedChangesQuestion ==
Are you sure you want to discard your unsaved changes?

== DiscardButton ==
Discard

== DeleteCommentQuestion ==
Are you sure you want to delete this?

== CheckingLockRulesProgress ==
Checking if item can be locked...

== ConfigLocksNoPermissions ==
You don't have the configlocks permission. Please ask your administrator to grant you the 'configlocks' permission on repository '{0}', or to configure a lock rule for this file.

== AddLockRuleAndCheckoutTitle ==
Add lock rule and checkout

== AddLockRuleAndCheckoutMessage ==
To lock a file, you must first configure a lock rule.{0}{0}Choose one of the following options to configure which files are automatically locked on checkout.

== AddLockRuleRadioGroup ==
Choose a locking rule or create your own

== AddLockRuleExtensionOption ==
Lock files matching {0}

== AddLockRuleNameOption ==
Lock any file with name '{0}'

== AddLockRuleDirectoryOption ==
Lock all files in directory{0}{1}

== AddLockRuleFullPathOption ==
Lock only{0}{1}

== AddLockRuleEditOption ==
Let me edit the lock rule

== AddLockRuleLearnMore ==
Click here to learn how to configure lock rules

== AddLockRuleLearnMoreUrl ==
https://www.plasticscm.com/download/help/locking

== AddLockRuleEmptyRuleMessage ==
The lock rule can not be empty.

== UnsupportedServerOperation ==
The server doesn't support this operation. Please upgrade the server to version {0} or greater.

== KnownServersListRetrievingCloudOrgsProgress ==
Retrieving Cloud organizations...

== EditCommentButton ==
Edit comment

== DiffChangedWithCommentDirty ==
You have edited the comment, do you want to save the changes?

== ReviewOfChangesetTitle ==
Review of changeset {0}

== ReviewOfBranchTitle ==
Review of branch {0}

== Title ==
Title

== ReviewStatus ==
Status

== ReworkRequired ==
Rework required

== UnderReview ==
Under review

== Reviewed ==
Reviewed

== Reviewer ==
Reviewer

== ChangeButton ==
Change

== ReviewCommentsLabel ==
Comments for line {0}

== NoReviewCommentsToShow ==
Click an icon on the right side of the diff to see the review comment for a given line

== NoReviewCommentsForSelectedFile ==
There are no review comments for the selected file

== NoReviewComments ==
Click the '+' sign on the right side of the diff to create a new comment for a given line

== NoReviewCommentsSupportedXlinkedRepo ==
Review comments for Xlinks are not supported yet

== NoReviewCommentsSupportedDifferentRepos ==
Review comments for xlinked files are not supported yet

== NoReviewCommentsSupportedUnsupportedFileType ==
Review comments for this file type are not supported yet

== DirtyCodeReviewTitleTitle ==
Save code review title

== DirtyCodeReviewTitleQuestion ==
You have edited the title, do you want to save the changes?

== DirtyCodeReviewDescriptionTitle ==
Save code review description

== DirtyCodeReviewDescriptionQuestion ==
You have edited the description, do you want to save the changes?

== DirtyReviewCommentTitle ==
Save review comment

== DirtyReviewCommentQuestion ==
You have edited the review comment, do you want to save the changes?

== DirtyCommentQuestion ==
You have edited the comment, do you want to save the changes?

== NullRepSpec ==
The repository information required to calculate the differences is missing.
Please report this case to support to get it fixed.

== CommentsLabelSingular ==
Comment

== CommentsLabelPlural ==
Comments

== ChangesLabelSingular ==
Change

== ChangesLabelPlural ==
Changes

== ChangeColumn ==
Change requested

== RevisionWithComments ==
Revision with comments

== LastInBranch ==
Last in branch

== DiffWithHead ==
Diff with head

== ThisFileHasNewChangesInTheBranch ==
This file has new changes in the branch

== ThisFileHasNewChangesInTheBranchExplanation ==
You are diffing the revision where the comments were entered. But there are new changes. You can diff the original revision, the newest in the branch or the changes between the two.

== AskQuestionButton ==
Ask a question

== AskQuestionButtonToolTip ==
When you need an answer before approving

== RequestChangeButton ==
Request a change

== RequestChangeButtonToolTip ==
When you need the author to change something before approving

== CopyCommentId ==
Copy comment ID to clipboard

== ChangeStatusDone ==
Done

== ChangeStatusPending ==
Pending

== ChangeStatusDiscarded ==
Discarded

== QuestionsLabelSingular ==
Question

== QuestionsLabelPlural ==
Questions

== QuestionColumn ==
Question

== AnswerColumn ==
Answer

== ExploreChangesetsPanelWaitingAnimation ==
Loading changesets...

== ChangesetReview ==
Changeset review

== ReviewEntireBranch ==
Review entire branch

== ReviewChangesetByChangeset ==
Review changeset by changeset

== UpdateStatusTitle ==
Confirm status update

== UpdateStatusPendingChangesSingular ==
You have 1 change to apply.

== UpdateStatusPendingChangesPlural ==
You have {0} changes to apply.

== UpdateStatusPendingQuestionsSingular ==
You have 1 question to answer.

== UpdateStatusPendingQuestionsPlural ==
You have {0} questions to answer.

== UpdateStatusPendingChangeAndQuestion ==
You have 1 change to apply and 1 question to answer.

== UpdateStatusPendingChangesAndQuestion ==
You have {0} changes to apply and 1 question to answer.

== UpdateStatusPendingChangeAndQuestions ==
You have 1 change to apply and {0} questions to answer.

== UpdateStatusPendingChangesAndQuestions ==
You have {0} changes to apply and {1} questions to answer.

== UpdateStatusQuestion ==
You should not mark as reviewed a code review with pending comments. Continue anyway?

== SinceOneWeekAgo ==
Last week

== SinceFifteenDaysAgo ==
Last 15 days

== SinceOneMonthAgo ==
Last month

== SinceThreeMonthsAgo ==
Last 3 months

== SinceOneYearAgo ==
Last year

== SinceAGivenDate ==
A given date

== CloseChangeRequestedInReview ==
Close change requested in review

== CollapseReviewCommentsTooltip ==
Collapse review comments

== ExpandReviewCommentsTooltip ==
Expand review comments

== CollapseCommentsSummaryTooltip ==
Collapse comments summary

== ExpandCommentsSummaryTooltip ==
Expand comments summary

== ReviewCommentsSummaryTitle ==
Review comments summary

== EmptyChangeList ==
The list of changes is empty

== EmptyQuestionList ==
The list of questions is empty

== MergeFileChangesInConflictCategory ==
File changes in conflict, might require manual intervention

== ChangesWithDirectoryConflictsCategory ==
Changes with directory structure conflicts

== MergeDiscardedConflictsCategory ==
Conflicts that no longer apply and possible warnings

== MergeFileChangesInConflictForIncomingViewCategory ==
Files to download that are in conflict with your changes

== MergeFileChangesInConflictRemark ==
Changes in both source and destination contributors

== MergeFileChangesInConflictRemarkForIncomingView ==
Requires merge

== MergeChangesMadeInSourceOfMergeCategory ==
Changes made in the source of the merge. No conflicts

== MergeChangesMadeInSourceOfMergeForIncomingViewCategory ==
Files that just need update

== MergeChangesMadeInSourceOfMergeOverviewSingular ==
1 changed file updated ({1})

== MergeChangesMadeInSourceOfMergeOverviewPlural ==
{0} changed files updated ({1})

== MergeMovesToApplyCategory ==
Moves to apply

== MergeMovesToApplyRemark ==
Moved on source to {0}

== MergeMovesToApplyRemarkForIncomingView ==
Will apply the move to your workspace

== MergeDeletesToApplyCategory ==
Deletes to apply

== MergeDeletesToApplyOverviewSingular ==
1 file deleted ({1})

== MergeDeletesToApplyOverviewPlural ==
{0} files deleted ({1})

== MergeDeletesToApplyRemark ==
Deleted on source

== MergeDeletesToApplyRemarkForIncomingView ==
Will apply the delete to your workspace

== MergeNewItemsToDownloadCategory ==
New items to download

== MergeNewItemsToDownloadOverviewSingular ==
1 file added ({1})

== MergeNewItemsToDownloadOverviewPlural ==
{0} files added ({1})

== MergeNewItemsToDownloadRemark ==
Added on source

== MergeNewItemsToDownloadRemarkForIncomingView ==
Will apply the add to your workspace

== MergeFileSystemPermissionsChangesToApplyCategory ==
Filesystem permissions changes to apply

== MergeFileSystemPermissionsChangesToApplyRemark ==
Changes in the filesystem permissions

== MergeFileSystemPermissionsChangesToApplyRemarkForIncomingView ==
Will apply the filesystem permissions change to your workspace

== MergeFilesToDownloadCaptionSingular ==
1 file to download ({0})

== MergeFilesToDownloadCaptionPlural ==
{0} files to download ({1})

== MergeFilesToDeleteCaptionSingular ==
1 file to delete ({0})

== MergeFilesToDeleteCaptionPlural ==
{0} files to delete ({1})

== MergeToEnterCheckinComment ==
Enter your check-in comment

== MergeToEnterCheckinCommentExplanation ==
Enter a check-in comment for the new changeset created by the merge-to.

== IncomingChangesTitle ==
Incoming changes from branch {0}

== IncomingChangesCannotBeApplied ==
Incoming changes that cannot be applied

== ChangesCannotBeApplied ==
Changes that cannot be applied

== ConfirmRecalculateMergeSingular ==
You already resolved 1 directory conflict. Recalculate merge?

== ConfirmRecalculateMergePlural ==
You already resolved {0} directory conflicts. Recalculate merge?

== ConfirmRecalculateMergeMessage ==
If you continue, you will lose the chosen resolution for resolved conflicts. Then, you can choose them again.

== RecalculateButton ==
Recalculate

== MergeProgressStatus ==
Updated {0} of {1} ({2} of {3} files to download / {4} of {5} operations to apply)

== MergeProgressDownloadingFile ==
Downloading file {0} ({1})

== MergeProgressDownloadingBlock ==
Downloading block of {0} files ({1})

== IncomingChangesAvailableSingular ==
1 new changeset available

== IncomingChangesAvailablePlural ==
{0} new changesets available

== IncomingChangesConflictsSingular ==
1 new changeset - conflicts

== IncomingChangesConflictsPlural ==
{0} new changesets - conflicts

== IncomingChangesAvailableForGluon ==
new changes available

== IncomingChangesConflictsForGluon ==
new conflicting changes

== UpdateButton ==
Update

== ViewButton ==
View

== DeleteRepositoryTitle ==
Confirm repository deletion

== UndeleteRepositoryTitle ==
Confirm undelete the repository

== DeleteRepositoryWarningText ==
You are about to delete the repository '{0}'.

== DeleteRepositoriesWarningText ==
You are about to delete the following repositories:

== DeleteRepositoryConfirmText ==
To confirm, type the name of the repository

== DeleteRepositoriesConfirmText ==
To confirm the delete, type '{0}'

== DeleteCannotUndo ==
This action cannot be undone.

== DeleteRepositoryConfirmEntry ==
delete

== UndeleteRepositoryConfirmText ==
You are about to undelete the following repositories: {0}. Do you want to proceed?

== IncomingChangesMenuItemMergeKeepingSourceChanges ==
Merge keeping the incoming changes (head/source)

== IncomingChangesMenuItemMergeKeepingWorkspaceChanges ==
Merge keeping workspace changes (yours/destination)

== IncomingChangesMenuItemDiffIncomingChanges ==
Diff the incoming change (head/source)

== IncomingChangesMenuItemDiffYoursWithIncoming ==
Diff yours with incoming (head/source)

== IncomingSymbolicName ==
Incoming Changes

== WorkingChangesetMissing ==
Working changeset is missing

== WorkingChangesetDeletedTitle ==
The working changeset was deleted

== WorkingChangesetDeletedMessage ==
The changeset you were working on was deleted.

Your checkouts and local changes are not valid anymore.

Do you want to go to the Pending Changes to undo your changes to return to a valid configuration?

== IncomingChangesUpdateTooltip ==
There are new changes in the branch that you can preview and download

== IncomingChangesResolveTooltip ==
There are files that are in conflict with your current changes

== IncomingChangesWorkingChangesetMissingTooltip ==
The changeset you were working on was deleted. Click the "Update" button to return to a valid configuration

== UndoAllWorkingChangesetDeletedMessage ==
Undoing your changes requires updating your workspace because the changeset you were working on was deleted.

Do you want to continue?

== PartialUndoWorkingChangesetDeletedMessage ==
You need to undo all your changes because the changeset you were working on was deleted.

Do you want to undo all your changes and update your workspace to a valid configuration?

== OutOfDateIncomingChangesView ==
This view is not valid anymore because the changeset you were working on was deleted.

You need to update your workspace.

== Warning ==
Warning

== IncomingChangesViewTitle ==
Incoming Changes

== Reason ==
Reason

== LoadingCodeReviews ==
Loading code reviews...

== ReviewTitleColumn ==
Title

== ReviewStatusColumn ==
Status

== MergingFile ==
Merging file {0} of {1}: {2}

== CodeReviewMenuItemOpen ==
Open

== CodeReviewMenuItemDelete ==
Delete

== DeletingCodeReview ==
Deleting code review with title {0} ...

== BranchMenuCreateANewCodeReview ==
New code review for this branch...

== ChangesetMenuCreateANewCodeReview ==
New code review for this changeset...

== CreatingCodeReview ==
Creating code review with title {0} ...

== ObtainingCodeReviewInfo ==
Loading code review with title {0} ...

== UpgradeServerForNewCodeReviewError ==
You need to upgrade the server to version 8.0.16.3691 or higher to use the new code review system!

== UpgradeServerTo110168321ForNewCodeReviewError ==
You need to upgrade the server to version 11.0.16.8321 or higher to use the new code review system!

== SelectNewCodeReviewBehaviorTitle ==
New code review

== SelectNewCodeReviewBehaviorExplanation ==
Do you want to create a new code review in the Desktop app or the Unity Cloud website?

== NewCodeReviewDefaultBehavior ==
Behavior for new code review

== NewCodeReviewAskAlways ==
Always ask me

== NewCodeReviewAskAlwaysExplanation ==
Always ask me how to create a code review

== CreateAndOpenCodeReviewInDesktopExplanation ==
Create a new code review and open it in the Desktop Application

== RequestCodeReviewFromUnityCloudExplanation ==
Open Unity Cloud to request a new code review

== LoadingMembers ==
Loading members...

== UserGroupChooserDialogTitle ==
Please select a user or group from the list below:

== UserChooserDialogTitle ==
Please select a user from the list below:

== SelectUsersDialogTitle ==
Select users

== SelectUserGroupDialogTitle ==
Select user or group

== SelectUserDialogTitle ==
Select user

== NoUserSelectedErrorMessage ==
Select a user from the list

== UserNotFoundErrorMessage ==
Could not find any user or group with name '{0}'

== UserSizeLimitExceededErrorMessage ==
The authentication server has exceeded the maximum limit of entries for a single query. Use the search filter to retrieve fewer entries (click the refresh button to trigger the retrieval).

== CreateRepositoryDialogDetailedExplanation ==
A repository stores the entire history of a particular directory tree, including all the revisions of its files and directories, revision labels, merge links, and other metadata.

== MergeToMenuItemMergeKeepingDestinationChanges ==
Merge keeping destination changes

== MergeToMenuItemPreserveDestinationChanges ==
Preserve destination changes

== MergeToMenuItemMergeKeepingSourceChanges ==
Merge keeping source changes

== MergeToMenuItemPreserveSourceChanges ==
Preserve source changes

== DetailsColumn ==
Details

== AuthorColumn ==
Author

== PrivacyStatementText ==
Controller: Unity Technologies ApS. Purpose of the processing: Create your account and provide the requested services. You may exercise your rights of access, rectification, erasure and other data protection rights as described in the Unity Privacy Policy. For additional information please see the Unity {0}

== PrivacyStatement ==
Privacy Statement

== GitSyncDialogTitle ==
Unity VCS synchronization with Git

== GitSyncDialogExplanation ==
Synchronize repository '{0}'

== GitSyncDialogDetailedExplanation ==
It will sync the required changesets between the Unity VCS and Git repositories

== SyncButton ==
Sync

== PlasticRepository ==
Unity VCS repository:

== GitRepositoryUrl ==
Git repository URL:

== GitRepositoryCannotBeEmpty ==
The repository URL field cannot be empty.

== LoadingStoredGitUrls ==
Loading stored Git URLs for repository '{0}'

== SyncStatusPullRepository ==
Pulling "{0}" from {1}

== SyncStatusPushRepository ==
Pushing "{0}" to {1}

== SyncStatusSyncRepository ==
Synchronizing "{0}" with {1}

== SyncStatusConflict ==
Warning: there are branches that were modified concurrently on both Git and Unity VCS sides.
These conflicts are handled as a "subbranch" in Unity VCS, and they'll need to be merged.
To be able to sync again, you need to solve the conflicts in these branches first.

== SyncPhaseUpgradingMappings ==
Upgrading mappings

== SyncPhaseReceivingReferences ==
Receiving references

== SyncPhaseSyncType ==
Compressing objects

== SyncPhaseCompressing ==
Compressing

== SyncPhaseDownloading ==
Downloading

== SyncPhaseProcessing ==
Processing

== SyncPhaseImporting ==
Importing

== SyncPhaseExporting ==
Exporting

== SyncPhasePackaging ==
Packaging

== SyncPhaseUploading ==
Sending

== SyncPhaseCompleted ==
Completed

== Users ==
Users

== Groups ==
Groups

== Both ==
Both

== SelectedUserOrGroup ==
Selected user or group:

== SelectedUser ==
Selected user:

== User ==
User

== Group ==
Group

== SpecialUser ==
Special user

== SpecialGroup ==
Special group

== AddButton ==
Add...

== RemoveButton ==
Remove

== AllowAll ==
Allow All

== DenyAll ==
Deny All

== RepositoryServerPermissions ==
Repository server permissions

== PermissionsFor ==
Permissions for {0}

== ConfigureObjectPermissions ==
Configure object permissions

== PermissionsColumn ==
Permissions

== AllowedColumn ==
Allowed

== DeniedColumn ==
Denied

== SecurityRepserverEntriesCannotBeEmpty ==
The repository server must have permissions defined for at least one valid user or group.
Please set permissions for one of them and try again.

== PermissionsCantRemoveInheritedSeid ==
Cannot remove an inherited entry

== MsgAllowDeny ==
The denied permission will override the Allowed permission

== ChangeOwner ==
Change...

== UsersAndGroups ==
Users and groups

== PermissionsMenuItem ==
Permissions

== Override ==
override

== OverrideHelp ==
Permissions can be overridden to bypass inheritance. Mark as overriden, then set the new value.

== DeleteBranchGroup ==
Delete group of branches

== DeleteSelectedBranchGroup ==
You are going to delete the selected group of branches.

== ContinueQuestion ==
Do you want to continue?

== DoNotShowMessageAgain ==
Do not show this message again

== PathPermissions ==
Path permissions

== DeleteSecuredPath ==
Delete path

== DeleteSelectedSecuredPath ==
You are going to delete the selected path.

== AllBranches ==
All branches

== NewBranchGroupTitle ==
Select branches

== NewBranchGroupExplanation ==
Select the branches where the new path permissions will be applied

== NewBranchGroupLabel ==
Type the branches

== BranchesRadioButton ==
Branches:

== NewBranchGroupIdentificationTag ==
Type an identification tag for the branches group:

== NoBranchSelected ==
No branch selected

== EditBranchGroupLabel ==
Edit branch group

== EditBranchGroupExplanation ==
Add or remove branches from the branch group

== PathPermissionFormExplanation ==
Configure secured path permissions. Note that these permissions will be merged with the corresponding branch and parent paths.

== RetrievingRepositoryServerInfoProgress ==
Retrieving repository server info...

== CalculatingPermissionsInfo ==
Calculating permissions info...

== RetrievingSecuredPathsProgress ==
Retrieving secured paths...

== RetrievingAllSecuredPathsProgress ==
Retrieving all secured paths...

== NewSecuredPathTitle ==
Create new secured path

== NewSecuredPathExplanation ==
Create new secured path where you can define path permissions

== NewSecuredPathLabel ==
Please type a path

== DefineBranchesCheckBox ==
Configure branches after creating path

== PartialWorkspaceStripLabel ==
The workspace is in partial mode. [[UpdateWorkspace|Run an update]] (get latest) to switch to full mode. This GUI does not work well in partial mode

== FullWorkspaceStripLabel ==
The workspace is in full mode. [[UpdateWorkspace|Run an update]] (get latest) to switch to partial mode. This GUI does not work well in full mode

== RemainingProgressMessage ==
remaining

== DiffLinkButtonTooltip ==
Copy link to share diffs

== CodeReviewLinkButtonTooltip ==
Copy link to share the code review

== GluonFilePlasticLinkText ==
Get link - copy a link to this file so you can easily share it with others

== OAuthSignInCheckMessage ==
Waiting for a session...

== RetrievingOrgsProgress ==
Retrieving organizations...

== RetrievingWorkspacesProgress ==
Retrieving cloud workspaces...

== CompleteSignInOnBrowser ==
Complete your sign in using your Browser.

== Or ==
or

== OrganizationNameWatermark ==
Your organization name

== SSOSignInTitle ==
Sign in with Single Sign On

== SignInToOrganization ==
Sign in to {0}

== DontKnowOrganizationName ==
I don't know the organization name

== KnowOrganizationName ==
I know the organization name

== CompanyEmail ==
Company Email

== SignInWithSingleSignOn ==
Sign in with Single Sign On

== SentEmailStatementText ==
We've sent an email to {0} with the organizations you belong. Check it and then, sign in using your {1}

== OrganizationNameStatementText ==
organization name.

== SendingEmail ==
Sending email...

== EmailNotSent ==
The email couldn't be sent

== SignInTo ==
Sign in to {0}

== ConnectToServer ==
Connect to {0}

== SignedInAs ==
Signed in as {0}

== SignInWasNotCompleted ==
Sign in was not completed

== OnDemandConnectionTypeHelp ==
On demand: Use this option when the Unity VCS server is not always reachable from this client and Unity VCS will not try to connect to it unless explicitly told so.

== AutomaticConnectionTypeHelp ==
Automatic: Use this option when the server is always online (maybe on the same local network, when connecting from a desktop machine).

== SignOut ==
Sign Out

== FastEnough ==
Fast enough!

== NotBad ==
This is not bad.

== TooMuch ==
This is too much.

== ErrorAddPlasticSCMMenuItem ==
Cannot add Unity VCS menu item: {0} Method Menu.AddSubMenu not found

== ErrorRemovePlasticSCMMenuItem ==
Cannot remove Unity VCS menu item: {0} Method MenuType.RemoveMenuItem not found

== ErrorUpdatePlasticSCMMenus ==
Cannot update all menus: Method EditorUtility.Internal_UpdateAllMenus not found

== CheckoutButton ==
Checkout

== UpdateResultsError ==
Some items were not correctly updated. You may want to check the errors and retry the operation after solving the problems

== PluginModalInformation ==
The modal dialog is not available in this version and it is required for the Unity VCS plugin works fine. \n <NAME_EMAIL> for further info.

== HideChanges ==
Hide changes

== ShowChanges ==
Show changes

== ChooseRepositoryTitle ==
Choose repository

== ExploreRepositories ==
Explore repositories

== WorkspacesExplanationLabel ==
Workspaces are used to store the local copies of your project files that you can check in to a repository.

== RepositoryName ==
Repository Name

== RepositoryNameShortLabel ==
Repository name:

== NewButton ==
New

== WorkPreferenceQuestion ==
How do you prefer to work?

== WorkPreferenceAnswerUnityVCS ==
I need branching, merging, and the ability to push/pull (Unity VCS workspace)

== WorkPreferenceAnswerGluon ==
I only need to check in my work (Gluon workspace)

== CreateWorkspace ==
Create workspace

== LearnMoreDifferencesUnityVCS ==
Learn more about the differences between Unity VCS/Gluon workspaces

== HereLink ==
here.

== PlasticSCMFullVsPartialWorkspaceLink ==
https://www.plasticscm.com/book/#_full_workspaces_vs_partial_workspaces

== SelectPathToRestore ==
Select path to restore

== SkipRestoreButton ==
Skip

== RevertThisFilePlusMeta ==
Revert this file +meta to their previous revisions

== RevertSelectedFiles ==
Revert these files to their previous revisions

== RevertThisFile ==
Revert this file to the previous revision

== UndeleteRevisionPlusMeta ==
Undelete revision +meta

== UndeleteSelectedRevisions ==
Undelete selected revisions

== UndeleteRevisions ==
Undelete revision

== UndeleteRevisionPlusMetaPath ==
Undelete revision +meta to specified path...

== UndeleteSelectedRevisionsPaths ==
Undelete selected revisions to specified paths...

== UndeleteRevisionPath ==
Undelete revision to specified path...

== DiffMenuItem ==
Diff

== DiffMenuItemSaveRevisionAs ==
Save this revision as...

== DiffMetaMenuItem ==
Diff .meta

== ViewHistoryMenuItem ==
History

== ViewHistoryMetaMenuItem ==
View .meta file history

== SolveConflictsInLable ==
Solve conflicts in the directory structure first

== OpenMeta ==
Open .meta

== OpenMetaWith ==
Open .meta with...

== OpenMetaInExplorer ==
Open .meta in explorer

== DownloadingProgress ==
Downloading

== CreatingWorkspaceProgress ==
Creating workspace

== CreateWorkspaceProgressStarting ==
Starting...

== CreateWorkspaceProgressConfiguringCredentials ==
Configuring credentials...

== CreateWorkspaceProgressCreatingWorkspace ==
Creating workspace...

== CreateWorkspaceProgressPerformingInitialCheckin ==
Checking in the packages and project settings files...

== CreateWorkspaceProgressFinished ==
Finished

== ConnectingError ==
There was an error connecting. Please check your internet.

== InstallingProgress ==
Installing...

== SkippingDownloadFileExists ==
Skipping download. File {0} already exists

== InstallUnityVersionControl ==
This feature is available on the desktop application of Unity Version Control.

== UnityVersionControlInstalled ==
Unity Version Control installed. You can now use the feature.

== LoginOrSignUp ==
Login or sign up

== CreateAUnityVersionControlWorkspace ==
 Create a Unity Version Control workspace for this project

== NextStepsToSetup ==
 Next steps to complete setup

== LoginOrSignUpUnityVersionControl ==
 Login or sign up for a Unity Version Control account

== InstallConfirmationMessage ==
You computer does not seem to have the application installed yet.
Would you like to download the application and its advanced tools?

Once the download is finished, simply install it on your computer.
Then, click the feature button again in the Unity Editor package.

== NeedEnterprise ==
Need Unity VCS Enterprise?

== PrefixUnityVersionControlMenu ==
Assets/Unity Version Control

== PendingChangesPlasticMenu ==
Show pending changes view

== AddPlasticMenu ==
Add to source control

== CheckoutPlasticMenu ==
Checkout

== CheckinPlasticMenu ==
Check in

== UndoPlasticMenu ==
Undo changes

== DiffPlasticMenu ==
Diff with previous revision

== HistoryPlasticMenu ==
History

== StatusCheckout ==
Checked out

== StatusConflicted ==
Conflicted

== StatusLockedByMe ==
Locked by me

== StatusLockedRemote ==
Locked remote

== AssetOverlayTooltipStatus ==
{0} by:

== AssetOverlayTooltipOn ==
on {0}

== TokenExchangeResponseNull ==
Token exchange response null

== TokenExchangeResponseError ==
Unable to exchange token: {0} [code {1}]

== TokenExchangeAccessEmpty ==
Access token is empty for user: {0}

== ErrorDownloadingProjectFromRepository ==
Error downloading Project from repository: {0}

== ErrorCreatingWorkspaceForProject ==
Error creating workspace for Project: {0}

== OpenInDesktopApp ==
Open in Desktop App

== OpenInGluon ==
Open in Gluon

== OpenInUnityCloud ==
Open in Unity Cloud

== OpenRepositoryInUnityCloudError ==
It was not possible to open the current repository in Unity Cloud.

== GetProjectInfoError ==
It was not possible to get project information from the Unity organization.

== PlasticConfigurationTitleUnityVCS ==
Configure the connection to the Unity VCS server

== PlasticConfigurationExplanation ==
Enter the server name or IP and the credentials

== UnityDiffShortcut ==
%d

== UnityAssetDiffShortcut ==
#%d

== UnityHistoryShortcutForWindows ==
%h

== UnityHistoryShortcutForMacOS ==
#%h

== UnityDeleteShortcutForWindows ==
_Del

== UnityDeleteShortcutForMacOS ==
%Del

== UnityMergeShortcutForWindows ==
%m

== UnityMergeShortcutForMacOS ==
#%m

== UnityHideUnhideShortcutForWindows ==
%h

== UnityHideUnhideShortcutForMacOS ==
#%h

== UnityOpenShortcut ==
#o

== CheckinInFilesProgress ==
Checking in files...

== Files ==
Files

== UnityCheckinConflictsExplanation ==
Some files you're trying to check in are in conflict. You can resolve conflicts using the Incoming Changes view.

== CheckinShowIncomingChangesView ==
Show incoming changes view

== UnityInitialCheckinComment ==
Add packages and project settings to Unity Version Control.

== UnityInitialCheckinProgress ==
Checking in the packages and project settings files...

== UnityUpdateVersionControlPackage ==
Update Unity Version Control

== UnityUpdateVersionControlPackageTooltip ==
Update Unity Version Control {0} to {1}

== UnityVersionControlPackageIsUpToDate ==
Unity Version Control is up to date

== UnityVersionControlPackageIsUpToDateTooltip ==
Unity Version Control {0} is up to date

== Default ==
Default

== FileMerge ==
File Merge

== KDiff3 ==
KDiff3

== SemanticMerge ==
Semantic Merge

== UnitySmartMerge ==
Unity Smart Merge

== Custom ==
Custom

== NotCloudRepository ==
The selected repository must be a cloud repository

== DisableAllHelpTips ==
Disable all tips

== Available ==
Available

== Unavailable ==
Unavailable

== CheckinChangelist ==
Check in changelist

== ShelveChangelist ==
Shelve changelist

== UndoChangesChangelist ==
Undo changes

== CreateNewChangelist ==
Create new changelist

== EditChangelist ==
Edit

== DeleteChangelist ==
Delete changelist

== CreateChangelistTitle ==
Create new changelist

== EditChangelistTitle ==
Edit changelist

== CreateChangelistExplanation ==
Provide a name and a description to create a new changelist

== EditChangelistExplanation ==
Change the desired values, and press ok when you're ready

== ChangelistNameEntry ==
Name

== ChangelistDescriptionEntry ==
Description

== ChangelistPersistentCheckBoxEntry ==
Persistent. The changelist won't be deleted when it's empty

== PlasticOpenShortcutForWindows ==
Shift+Ctrl+O

== PlasticOpenShortcutForMacOS ==
Shift+Cmd+O

== PlasticOpenShortcutForLinux ==
Shift+Ctrl+O

== PlasticOpenInNewWindowShortcutForWindows ==
Ctrl+O

== PlasticOpenInNewWindowShortcutForMacOS ==
Cmd+O

== PlasticOpenInNewWindowShortcutForLinux ==
Ctrl+O

== PlasticOpenInExplorerShortcutForWindows ==
Shift+Ctrl+S

== PlasticOpenInExplorerShortcutForMacOS ==
Shift+Cmd+S

== PlasticOpenInExplorerShortcutForLinux ==
Shift+Ctrl+S

== PlasticNewShortcutForWindows ==
Ctrl+N

== PlasticNewShortcutForMacOS ==
Cmd+N

== PlasticNewShortcutForLinux ==
Ctrl+N

== PlasticCopyShortcutForWindows ==
Ctrl+C

== PlasticCopyShortcutForMacOS ==
Cmd+C

== PlasticCopyShortcutForLinux ==
Ctrl+C

== PlasticNewSyncViewShortcutForWindows ==
Ctrl+C

== PlasticNewSyncViewShortcutForMacOS ==
Cmd+C

== PlasticNewSyncViewShortcutForLinux ==
Ctrl+C

== PlasticRenameShortcutForWindows ==
F2

== PlasticRenameShortcutForMacOS ==
Alt+R

== PlasticRenameShortcutForLinux ==
F2

== PlasticDeleteShortcutForWindows ==
Delete

== PlasticDeleteShortcutForMacOS ==
Cmd+Back

== PlasticDeleteShortcutForLinux ==
Delete

== PlasticPermissionsShortcutForWindows ==
Ctrl+P

== PlasticPermissionsShortcutForMacOS ==
Cmd+P

== PlasticPermissionsShortcutForLinux ==
Ctrl+P

== PlasticExpandAllShortcutForWindows ==
Ctrl+Shift+E

== PlasticExpandAllShortcutForMacOS ==
Cmd+Shift+E

== PlasticExpandAllShortcutForLinux ==
Ctrl+Shift+E

== PlasticCollapseAllShortcutForWindows ==
Ctrl+Shift+C

== PlasticCollapseAllShortcutForMacOS ==
Cmd+Shift+C

== PlasticCollapseAllShortcutForLinux ==
Ctrl+Shift+C

== PlasticMergeShortcutForWindows ==
Ctrl+M

== PlasticMergeShortcutForMacOS ==
Cmd+M

== PlasticMergeShortcutForLinux ==
Ctrl+M

== PlasticDiffShortcutForWindows ==
Ctrl+D

== PlasticDiffShortcutForMacOS ==
Cmd+D

== PlasticDiffShortcutForLinux ==
Ctrl+D

== PlasticApplyLabelShortcutForWindows ==
Ctrl+L

== PlasticApplyLabelShortcutForMacOS ==
Cmd+L

== PlasticApplyLabelShortcutForLinux ==
Ctrl+L

== PlasticSelectAllShortcutForWindows ==
Ctrl+A

== PlasticSelectAllShortcutForMacOS ==
Cmd+A

== PlasticSelectAllShortcutForLinux ==
Ctrl+A

== PlasticSwitchShortcutForWindows ==
Shift+Ctrl+W

== PlasticSwitchShortcutForMacOS ==
Shift+Cmd+W

== PlasticSwitchShortcutForLinux ==
Shift+Ctrl+W

== PlasticGoToLineShortcutForWindows ==
Ctrl+G

== PlasticGoToLineShortcutForMacOS ==
Ctrl+G

== PlasticGoToLineShortcutForLinux ==
Ctrl+G

== PlasticBranchShortcutForWindows ==
Ctrl+B

== PlasticBranchShortcutForMacOS ==
Cmd+B

== PlasticBranchShortcutForLinux ==
Ctrl+B

== PlasticFindShortcutForWindows ==
Ctrl+F

== PlasticFindShortcutForMacOS ==
Cmd+F

== PlasticFindShortcutForLinux ==
Ctrl+F

== PlasticReplaceShortcutForWindows ==
Ctrl+H

== PlasticReplaceShortcutForMacOS ==
Cmd+H

== PlasticReplaceShortcutForLinux ==
Ctrl+H

== PlasticLabelShortcutForWindows ==
Ctrl+L

== PlasticLabelShortcutForMacOS ==
Cmd+L

== PlasticLabelShortcutForLinux ==
Ctrl+L

== PlasticSaveShortcutForWindows ==
Ctrl+S

== PlasticSaveShortcutForMacOS ==
Cmd+S

== PlasticSaveShortcutForLinux ==
Ctrl+S

== PlasticAnnotateShortcutForWindows ==
Ctrl+T

== PlasticAnnotateShortcutForMacOS ==
Cmd+T

== PlasticAnnotateShortcutForLinux ==
Ctrl+T

== PlasticHistoryShortcutForWindows ==
Ctrl+H

== PlasticHistoryShortcutForMacOS ==
Cmd+Y

== PlasticHistoryShortcutForLinux ==
Ctrl+H

== PlasticCheckinShortcutForWindows ==
Ctrl+I

== PlasticCheckinShortcutForMacOS ==
Cmd+I

== PlasticCheckinShortcutForLinux ==
Ctrl+I

== PlasticCheckoutShortcutForWindows ==
Ctrl+O

== PlasticCheckoutShortcutForMacOS ==
Cmd+O

== PlasticCheckoutShortcutForLinux ==
Ctrl+O

== PlasticUndoShortcutForWindows ==
Ctrl+Z

== PlasticUndoShortcutForMacOS ==
Cmd+Z

== PlasticUndoShortcutForLinux ==
Ctrl+Z

== PlasticRedoShortcutForWindows ==
Ctrl+Y

== PlasticRedoShortcutForMacOS ==
Shift+Cmd+Z

== PlasticRedoShortcutForLinux ==
Ctrl+Y

== PlasticCutShortcutForWindows ==
Ctrl+X

== PlasticCutShortcutForMacOS ==
Cmd+X

== PlasticCutShortcutForLinux ==
Ctrl+X

== PlasticPasteShortcutForWindows ==
Ctrl+V

== PlasticPasteShortcutForMacOS ==
Cmd+V

== PlasticPasteShortcutForLinux ==
Ctrl+V

== PlasticBinTypeShortcutForWindows ==
Shift+Ctrl+B

== PlasticBinTypeShortcutForMacOS ==
Shift+Cmd+B

== PlasticBinTypeShortcutForLinux ==
Shift+Ctrl+B

== PlasticTxtTypeShortcutForWindows ==
Shift+Ctrl+T

== PlasticTxtTypeShortcutForMacOS ==
Shift+Cmd+T

== PlasticTxtTypeShortcutForLinux ==
Shift+Ctrl+T

== PlasticNewFileShortcutForWindows ==
Shift+Ctrl+N

== PlasticNewFileShortcutForMacOS ==
Shift+Cmd+N

== PlasticNewFileShortcutForLinux ==
Shift+Ctrl+N

== PlasticNewDirectoryShortcutForWindows ==
Shift+Ctrl+D

== PlasticNewDirectoryShortcutForMacOS ==
Shift+Cmd+D

== PlasticNewDirectoryShortcutForLinux ==
Shift+Ctrl+D

== PlasticAddShortcutForWindows ==
Shift+Ctrl+A

== PlasticAddShortcutForMacOS ==
Shift+Cmd+A

== PlasticAddShortcutForLinux ==
Shift+Ctrl+A

== PlasticAddRecursiveShortcutForWindows ==
Shift+Ctrl+R

== PlasticAddRecursiveShortcutForMacOS ==
Shift+Cmd+R

== PlasticAddRecursiveShortcutForLinux ==
Shift+Ctrl+R

== PlasticSearchShortcutForWindows ==
Ctrl+F

== PlasticSearchShortcutForMacOS ==
Cmd+F

== PlasticSearchShortcutForLinux ==
Ctrl+F

== PlasticFindInFilesShortcutForWindows ==
Shift+Ctrl+F

== PlasticFindInFilesShortcutForMacOS ==
Shift+Cmd+F

== PlasticFindInFilesShortcutForLinux ==
Shift+Ctrl+F

== PlasticRefreshShortcutForWindows ==
F5

== PlasticRefreshShortcutForMacOS ==
Cmd+R

== PlasticRefreshShortcutForLinux ==
F5

== PlasticShowItemsViewShortcutForWindows ==
Ctrl+D1

== PlasticShowItemsViewShortcutForMacOS ==
Cmd+D1

== PlasticShowItemsViewShortcutForLinux ==
Ctrl+D1

== PlasticShowPendingChangesViewShortcutForWindows ==
Ctrl+D2

== PlasticShowPendingChangesViewShortcutForMacOS ==
Cmd+D2

== PlasticShowPendingChangesViewShortcutForLinux ==
Ctrl+D2

== PlasticShowBranchExplorerViewShortcutForWindows ==
Ctrl+D3

== PlasticShowBranchExplorerViewShortcutForMacOS ==
Cmd+D3

== PlasticShowBranchExplorerViewShortcutForLinux ==
Ctrl+D3

== PlasticShowChangesetsViewShortcutForWindows ==
Ctrl+D4

== PlasticShowChangesetsViewShortcutForMacOS ==
Cmd+D4

== PlasticShowChangesetsViewShortcutForLinux ==
Ctrl+D4

== PlasticShowBranchesViewShortcutForWindows ==
Ctrl+D5

== PlasticShowBranchesViewShortcutForMacOS ==
Cmd+D5

== PlasticShowBranchesViewShortcutForLinux ==
Ctrl+D5

== PlasticShowLabelsViewShortcutForWindows ==
Ctrl+D6

== PlasticShowLabelsViewShortcutForMacOS ==
Cmd+D6

== PlasticShowLabelsViewShortcutForLinux ==
Ctrl+D6

== PlasticShowAttributesViewShortcutForWindows ==
Ctrl+D7

== PlasticShowAttributesViewShortcutForMacOS ==
Cmd+D7

== PlasticShowAttributesViewShortcutForLinux ==
Ctrl+D7

== PlasticShowReviewsViewShortcutForWindows ==
Ctrl+D8

== PlasticShowReviewsViewShortcutForMacOS ==
Cmd+D8

== PlasticShowReviewsViewShortcutForLinux ==
Ctrl+D8

== PlasticShowSyncViewShortcutForWindows ==
Ctrl+D9

== PlasticShowSyncViewShortcutForMacOS ==
Cmd+D9

== PlasticShowSyncViewShortcutForLinux ==
Ctrl+D9

== PlasticShowRepositoriesViewShortcutForWindows ==
Ctrl+Shift+D1

== PlasticShowRepositoriesViewShortcutForMacOS ==
Cmd+Shift+D1

== PlasticShowRepositoriesViewShortcutForLinux ==
Ctrl+Shift+D1

== PlasticShowWorkspacesViewShortcutForWindows ==
Ctrl+Shift+D2

== PlasticShowWorkspacesViewShortcutForMacOS ==
Cmd+Shift+D2

== PlasticShowWorkspacesViewShortcutForLinux ==
Ctrl+Shift+D2

== PlasticShowGluonWorkspaceExplorerViewShortcutForWindows ==
Ctrl+D1

== PlasticShowGluonWorkspaceExplorerViewShortcutForMacOS ==
Cmd+D1

== PlasticShowGluonWorkspaceExplorerViewShortcutForLinux ==
Ctrl+D1

== PlasticShowGluonCheckinViewShortcutForWindows ==
Ctrl+D2

== PlasticShowGluonCheckinViewShortcutForMacOS ==
Cmd+D2

== PlasticShowGluonCheckinViewShortcutForLinux ==
Ctrl+D2

== PlasticShowGluonIncomingChangesViewShortcutForWindows ==
Ctrl+D3

== PlasticShowGluonIncomingChangesViewShortcutForMacOS ==
Cmd+D3

== PlasticShowGluonIncomingChangesViewShortcutForLinux ==
Ctrl+D3

== PlasticShowGluonChangesetsViewShortcutForWindows ==
Ctrl+D4

== PlasticShowGluonChangesetsViewShortcutForMacOS ==
Cmd+D4

== PlasticShowGluonChangesetsViewShortcutForLinux ==
Ctrl+D4

== PlasticShowGluonRepositoriesViewShortcutForWindows ==
Ctrl+Shift+D1

== PlasticShowGluonRepositoriesViewShortcutForMacOS ==
Cmd+Shift+D1

== PlasticShowGluonRepositoriesViewShortcutForLinux ==
Ctrl+Shift+D1

== PlasticShowGluonWorkspacesViewShortcutForWindows ==
Ctrl+Shift+D2

== PlasticShowGluonWorkspacesViewShortcutForMacOS ==
Cmd+Shift+D2

== PlasticShowGluonWorkspacesViewShortcutForLinux ==
Ctrl+Shift+D2

== PlasticCheckinActionShortcutForMacOS ==
Shift+Cmd+I

== PlasticMergeActionShortcutForMacOS ==
Shift+Cmd+M

== PlasticSwitchToActionShortcutForMacOS ==
Shift+Cmd+W

== PlasticNewFileActionShortcutForMacOS ==
Shift+Cmd+N

== PlasticNewDirectoryActionShortcutForMacOS ==
Shift+Cmd+D

== PlasticSaveActionShortcutForMacOS ==
Cmd+S

== PlasticCloseActionShortcutForMacOS ==
Cmd+W

== PlasticUndoActionShortcutForMacOS ==
Cmd+Z

== PlasticRedoActionShortcutForMacOS ==
Shift+Cmd+Z

== PlasticCutActionShortcutForMacOS ==
Cmd+X

== PlasticCopyActionShortcutForMacOS ==
Cmd+C

== PlasticPasteActionShortcutForMacOS ==
Cmd+V

== PlasticSelectAllActionShortcutForMacOS ==
Cmd+A

== PlasticFindActionShortcutForMacOS ==
Cmd+F

== PlasticFindNextActionShortcutForMacOS ==
Cmd+G

== PlasticFindPreviousActionShortcutForMacOS ==
Shift+Cmd+G

== PlasticUseSelectionActionShortcutForMacOS ==
Cmd+E

== PlasticJumpToSelectionActionShortcutForMacOS ==
Cmd+J

== PlasticPreferencesShortcutForMacOS ==
Cmd+,

== PlasticPreferencesShortcut ==
Ctrl+,

== BranchExplorerSearchFieldShortcutsTooltipForWindows ==
Keyboard shortcuts:
  Enter: focus the next search result
  Ctrl+Enter: focus the previous one

== BranchExplorerSearchFieldShortcutsTooltipForMacOS ==
Keyboard shortcuts:
  Enter: focus the next search result
  Cmd+Enter: focus the previous one

== BranchExplorerSearchFieldShortcutsTooltipForLinux ==
Keyboard shortcuts:
  Enter: focus the next search result
  Ctrl+Enter: focus the previous one

== DisableManualCheckout ==
Disable Manual Checkout for Unity Assets

== EnableManualCheckout ==
Enable Manual Checkout for Unity Assets

== RecommendToEnableManualCheckoutSinceLockRulesAreConfigured ==
Locks are currently configured for your project. 
We recommend that you enable ‘Manual Checkout’.

This will reduce conflicts, preventing other team members from modifying an asset that is locked. 

Do you want to enable Manual Checkout?

== StatusBarReady ==
Ready

== PendingMergeLinksCaption ==
Pending merge links will be committed when you check in your changes or reverted if you undo your changes

== PendingMergeLinksCountSingular ==
1 pending merge link

== PendingMergeLinksCountPlural ==
{0} pending merge links

== NewReleaseProgressDownloading ==
Downloading update - {0} / {1} ({2} left)

== NewReleaseProgressDownloadFinished ==
Finished downloading. Ready to install.

== NewReleaseProgressRestartAndUpgrade ==
New version {0} ready to install

== RestartAndUpgradeButton ==
Restart and Upgrade

== ItemDeletedDialogTitle ==
Could not show item

== ItemDeletedDialogMessageForMacOS ==
Item has either been deleted or is excluded by the current filter.

== ItemDeletedDialogMessage ==
The item for this comment cannot be shown as it has been deleted.

== CannotConnectToWebadmin ==
Cannot connect to the webadmin server

== MergeDiagramButton ==
Merge diagram

== GoToButton ==
Go to {0}

== PlusMore ==
+{0} more

== CheckoutChangesetComment ==
The selected changeset represents the pending changes in your workspace. It's not checked in into the repository yet.

== NoIncomingChanges ==
No incoming changes

== NoMergeChanges ==
No merge changes

== WorkspaceIsUpToDate ==
Workspace is up to date.

== DarkTheme ==
Dark theme

== CheckinCompleted ==
Check-in successfully completed.

== CheckinChangesetWasCreatedPart ==
was created.

== UndoCompleted ==
Undo changes successfully completed

== WorkspaceUpdateCompleted ==
Workspace successfully updated

== MainMenuView ==
View

== MainMenuActions ==
Actions

== MainMenuWindow ==
Window

== ActionsMenuCheckin ==
Check in

== ActionsMenuMerge ==
Merge

== ActionsMenuCreateBranch ==
Create Branch

== ActionsMenuCreateLabel ==
Create Label

== ActionsMenuSwitchTo ==
Switch To

== ActionsMenuCreateRepository ==
Create Repository

== ActionsMenuCreateWorkspace ==
Create Workspace

== ActionsMenuRefresh ==
Refresh

== MainMenuFile ==
File

== FileMenuNewFile ==
New File

== FileMenuNewDirectory ==
New Directory

== FileMenuSave ==
Save

== FileMenuClose ==
Close

== MainMenuEdit ==
Edit

== EditMenuUndo ==
Undo

== EditMenuRedo ==
Redo

== EditMenuFind ==
Find

== EditMenuReplace ==
Replace

== EditMenuFindFind ==
Find…

== EditMenuFindFindNext ==
Find Next

== EditMenuFindFindPrevious ==
Find Previous

== EditMenuFindUseSelection ==
Use Selection for Find

== EditMenuFindJumpToSelection ==
Jump to Selection

== EditMenuCut ==
Cut

== EditMenuCopy ==
Copy

== EditMenuPaste ==
Paste

== EditMenuDelete ==
Delete

== EditMenuSelectAll ==
Select All

== EditMenuGoToLine ==
Go to line...

== SavingComment ==
Saving comment...

== DeletingComment ==
Deleting comment...

== AppMenuAbout ==
About Unity DevOps Version Control

== AppMenuAboutGluon ==
About GluonX

== AppMenuPreferences ==
Preferences…

== AppMenuPlasticWebsite ==
Unity DevOps Version Control Website…

== NoCommentSet ==
No comment has been set

== CannotGetComment ==
Cannot get the comment

== CopiedToClipboard ==
Copied!

== OpenOrCreateWorkspace ==
View workspaces

== ViewRepositories ==
View repositories

== InviteMembersToOrganization ==
Invite members to the organization

== InviteMembersToProject ==
Invite members to the project

== InviteOtherTeamMembers ==
Invite other team members

== InviteMembersTitle ==
Invite members

== InviteMembersToOrganizationNotAdminError ==
You cannot invite members to the organization because you are not the administrator.

== CalculatingViewLayout ==
Calculating view layout

== ConfirmRevertToChangesetTitle ==
Confirm Revert

== RevertToChangesetWarning ==
Are you sure you want to revert to this changeset?

== ChangesetMenuItemRevertToChangeset ==
Revert to changeset

== UpdateReportTitle ==
Update report

== MoveToChangelist ==
Move to changelist

== ChangelistNameEmpty ==
Changelist name must not be empty

== SignInToContinue ==
Sign in to continue

== SignInToOrganizationRequired ==
Sign into this organization is required to view its repositories.

== SignIntoServer ==
SIGN IN

== NotSignedIn ==
You're not signed in

== SessionEnded ==
Your session probably ended because there was not activity for this account.

== SignInAgain ==
SIGN IN AGAIN

== SomethingWentWrong ==
Something went wrong

== TryAgain ==
TRY AGAIN

== LoadingRepositoryTree ==
Loading repository tree...

== DetailsHeader ==
Details

== Download ==
Download

== DownloadUpdate ==
Download update

== CalculatingSize ==
Calculating directory size...

== OpenWorkspace ==
Open workspace

== OpenCloudDashboard ==
Open cloud dashboard

== Accounts ==
Accounts

== ManageAccounts ==
Manage accounts

== LocalServer ==
Local server

== DivergentMoveViewSource ==
View content of the moved item

== LoadedTwiceViewSource ==
View content of the item loaded twice

== CycleMoveConflictViewSource ==
View content of item moved on source

== CycleMoveConflictViewDestination ==
View content of item moved on destination

== EvilTwinsConflictViewSource ==
View content of item added on source

== EvilTwinsConflictViewDestination ==
View content of item added on destination

== MoveDeleteConflictViewSource ==
View content of item moved on source

== MoveDeleteConflictViewDestination ==
View content of item deleted on destination

== DeleteMoveConflictViewSource ==
View content of item deleted on source

== DeleteMoveConflictViewDestination ==
View content of item moved on destination

== MovedEvilConflictViewSource ==
View content of item moved on source

== MovedEvilConflictViewDestination ==
View content of item moved on destination

== AddMoveConflictViewSource ==
View content of item added on source

== AddMoveConflictViewDestination ==
View content of item moved on destination

== MoveAddConflictViewSource ==
View content of item moved on source

== MoveAddConflictViewDestination ==
View content of item added on destination

== ChangeDeleteConflictViewSourceAdded ==
View content of item added on source

== ChangeDeleteConflictViewSourceChanged ==
View content of item changed on source

== ChangeDeleteConflictViewDestination ==
View content of item deleted on destination

== DeleteChangeConflictViewSource ==
View content of item deleted on source

== DeleteChangeConflictViewDestinationAdded ==
View content of item added on destination

== DeleteChangeConflictViewDestinationChanged ==
View content of item changed on destination

== NoOrganizationTitle ==
No organization yet?

== ClickButtonBelowToCreateOrg ==
Click the button below to create a new organization from Unity Cloud

== OpenUnityDashboard ==
Open Unity Cloud

== CreateNewOrganization ==
Create a new organization

== CreateOrganizationNameLabel ==
Organization name

== CreateDatacenterLabel ==
Datacenter

== Organizations ==
Organizations

== NoRepositoryTitle ==
No repository yet?

== CreateNewRepository ==
Create a new repository

== NoWorkspacesTitle ==
No workspaces yet?

== CreateNewWorkspace ==
Create a new workspace

== CreateWorkspaceLocationLabel ==
Location:

== CreateWorkspaceCheckBoxLabel ==
Create a workspace for this repository

== CreateWorkspaceExplanation ==
A workspace is your own local copy of items downloaded from a repository. It contains source code, documents, or any other files where you want to track all changes.

== CreateRepositoryExplanation ==
A repository is stored on a server (local or remote), it contains the entire history of a particular directory tree, including all the changes of its files and directories, revision labels, merge links, and other metadata.

== EmailWatermark ==
Email

== PasswordWatermark ==
Password

== ConfirmPasswordWatermark ==
Confirm password

== OrContinueWith ==
Or continue with

== UnityID ==
Unity ID

== WelcomeToPlasticSCMUnity ==
Welcome to Unity Version Control - Part of the Unity family

== DontHaveOrganization ==
Don't have an UVCS organization?

== ForgotPassword ==
Forgot password?

== ConnectToOnPremiseServer ==
Connect to an on-premises server

== HowWillYouBeWorkingWithPlastic ==
How will you be working with Unity Version Control?

== HowWillYouBeWorkingWithPlasticExplanation ==
Regardless of the choice you make, you will be able to switch between the Gluon client and the full Unity Version Control client later using the top-right menu.

== PlasticProductDescription ==
I need all the features of source control including branching and merging.

== PlasticProductDetails ==
This option will launch the full Unity Version Control Client with all source control features.

== GluonProductDescription ==
I do not need all the features of source control. I only need to edit and check in files.

== GluonProductDetails ==
This option will launch the Gluon Client which does not include features like branching and merging.

== SwitchToGluon ==
Switch to Gluon

== SwitchToPlastic ==
Switch to Unity VCS

== DynamicWkspacesTitle ==
Configuration of Dynamic Workspaces

== DynamicWkspacesSummary ==
Dynamic workspaces (also known as virtual workspaces) allow you to work with large repositories efficiently. Instead of downloading everything at once, files are retrieved on demand, guided by smart heuristics to speed up access to multiple files. This means you can mount large working copies while only using disk space for the files you actually need.

== DynamicWkspacesCurrently ==
The PlasticFS agent

== DynamicWkspacesInYourComputer ==
to use dynamic workspaces

== DynamicWkspacesAgentIsReady ==
is ready

== DynamicWkspacesAgentIsRequired ==
is required

== DynamicWkspacesStartAgent ==
Start PlasticFS agent for the first time and set it to run on startup?

== DynamicWkspacesStartAgentRemarks ==
It's visible in the taskbar tray when running.

== DynamicWkspacesStartAgentButton ==
Launch

== CodeEditorTitle ==
Code editor settings

== CodeEditorFont ==
Editor font

== CodeEditorTabs ==
Tabs

== CodeEditorColumnGuides ==
Column guides

== CodeEditorNoColumnGuides ==
No guidelines

== CodeEditorConvertTabs ==
Convert tabs to spaces while editing

== CodeEditorViewWhitespaces ==
View whitespaces

== CodeEditorViewLineEndings ==
View line endings

== CodeEditorRestoreToDefault ==
Restore to default

== TurnOn ==
Turn on

== LearnMoreAboutDynamicWorkspaces ==
Learn more about Dynamic Workspaces

== UsernameWatermark ==
Username

== WrongServer ==
Wrong server?

== GoBack ==
Go back

== ConnectToCloudServer ==
Connect to a cloud server

== WrongAuthenticationMethod ==
Wrong authentication method?

== ConfirmDeleteAccountTitle ==
Confirm remove account

== DeleteUserAccountConfirmMessage ==
Are you sure you want to remove account {0}?

== RemoveAccountButton ==
Remove account

== EditAccountButton ==
Edit account

== AddNewPlasticScmAccount ==
Add a new Unity DevOps Version Control account

== BrowseRepositoryDialogExplanation ==
Select a path

== EmptyRepositoryHeader ==
This repository is empty

== WorkspaceNotOnDisk ==
<not on disk>

== OpeningRepository ==
Opening repository

== GoToLine ==
Go to line

== LineNumberRange ==
Line number ({0} - {1}):

== ShowNotification ==
Show Notification

== HideNotification ==
Hide Notification

== NoComment ==
<No comment>

== HistoryRevisionDetailsFormatString ==
{0} {1} on {2} by {3}

== HistoryHeaderDetailsStringFormat ==
by {0} on {1}

== ShelveDetailsFormatString ==
{0} by {1}

== ShelveDetailsWithRepositoryFormatString ==
{0} by {1} on {2}

== ExpandCollapseComment ==
Expand/collapse comment

== OnBranch ==
on branch

== HistoryDiffViewButton ==
Diff view

== HistoryAnnotateButton ==
Annotate

== ThisItemWasMoved ==
This item was moved

== MovedFromLabel ==
From:

== MovedToLabel ==
To:

== ThisItemWasDeleted ==
This item was deleted

== IssueTrackersEmptyState ==
A workspace is needed to configure an issue tracker extension.

== NoTaskControlBinded ==
Do not bind Unity VCS branches or changesets with any issue tracking system

== TaskControlBinded ==
Bind to this issue tracking system:

== TaskControlApplyBinding ==
Apply binding to:

== AllRepositories ==
Repositories

== SelectedIssueTracker ==
Selected issue tracker: {0}

== TestConnection ==
Test Connection

== BindIssuesToBranches ==
Bind issues to Unity VCS branches (recommended)

== BindIssuesToChangesets ==
Bind issues to Unity VCS changesets

== IssueTrackerConnectionFailed ==
Test connection failed. Please review the entered values. {0}

== IssueTrackerNotLoaded ==
Unable to load the issue tracker extension library.

== ChangesetColors ==
Changeset colors

== SelectUsersDialogDescription ==
Please select one or more users from the list below:

== ClickToSetAlias ==
Click to set alias

== ByUser ==
By user

== ByReplicationSource ==
By replication source

== UnsavedChanges ==
There are unsaved changes

== ViewChangesOrExit ==
Workspace '{0}' has unsaved changes. Would you like to view them, or exit without saving?

== ViewChanges ==
View changes

== ExitWithoutSaving ==
Exit without saving

== OperationInProgressOnWk ==
There is an operation in progress on workspace '{0}'.
Please wait for it to finish.

== ExitButton ==
Exit

== MergeLinkMissingChangeset ==
missing changeset (this link cannot be checked in)

== NoCheckinCommentTitle ==
Check-in comment is empty!

== NoCheckinCommentMessage ==
The comment field is empty. You'll create a changeset that has no check-in comment. Do you want to continue?

== NoShelveCommentTitle ==
Shelve comment is empty!

== NoShelveCommentMessage ==
Please add a comment to easily identify your shelves later

== DontAskMeAgainWithAction ==
Don't ask me again. You can change this setting later in {0}

== ShelveOptions ==
shelve options

== CommentOptions ==
comment options

== AddComment ==
Add comment

== SkipAndShelve ==
Skip & Shelve

== SkipAndCheckin ==
Skip & Check in

== AddCheckinComment ==
Please add a comment for your check-in

== AddShelveComment ==
Please add a comment for your shelve

== DontAskMeAgain ==
Don't ask me again

== ResetButton ==
Reset

== OnlyThis ==
Only this

== ModifiedWithDate ==
modified {0}

== SelectEntryToSeeDifferences ==
Select an entry from the history to see what changed

== ShowRevisionsThatContainsMethod ==
Show only revisions that contain the element

== ShowRevisionsWithChanges ==
Show only revisions with changes

== ShowFullFileDifferences ==
Show full file differences

== ShowElementDifferences ==
Show only element differences

== LoadMore ==
Load more

== LoadMoreCount ==
Load more ({0}/{1})

== SemanticHistoryTitle ==
Semantic history of {0} at file {1}

== ElementNotFound ==
The element was not found

== RevisionNotConnected ==
Cannot be processed, related revisions are not replicated

== ChangedMergeFromCs ==
Changed in merge source and destination (from cs:{0})

== NotChanged ==
Unchanged

== Added ==
Added

== Changed ==
Changed

== MergeFromCs ==
 in merge source (from cs:{0})

== MovedOutScope ==
Moved from {0} to {1}

== Renamed ==
Renamed from {0} to {1}

== MovedDownPosition ==
Moved down {0} positions

== MovedUpPosition ==
Moved up {0} positions

== And ==
 and {0}

== PendingConflicts ==
Conflicts to solve ({0})

== DiffConflictButton ==
diff

== ExplainMoveButton ==
explain move

== ViewConflictOnResultButton ==
view on result

== ViewMoveSrcOnResultButton ==
source on result

== ViewMoveDstOnResultButton ==
destination on result

== ChangePositionButton ==
change position

== TwoWayMergeButton ==
2-way

== KeepDstChangesButton ==
keep destination

== KeepSrcChangesButton ==
keep source

== CodeReviewFiltersAll ==
ALL code reviews

== CodeReviewFiltersPending ==
Pending reviews

== CodeReviewFiltersCreatedByMe ==
Created by me

== CodeReviewFiltersAssignedToMe ==
Assigned to me

== CodeReviewFiltersCustomQuery ==
Use a custom query

== AnalyzeRefactorGroups ==
We find refactors automatically for less than 50 files. You can launch it manually :)

== AnalyzeRefactorGroupsButton ==
Analyze refactors

== CantAnalyzeRefactors ==
Can't analyze refactors: {0}

== NoRefactorGroups ==
There are no refactors detected.

== DetectedRefactorGroups ==
Code moved between files detected!

== ShowButton ==
Show

== HideButton ==
Hide

== DownloadingData ==
Downloading data

== SemanticDifferencesCalculated ==
Semantic differences calculated

== ProcessingDifferences ==
Processing differences

== RefactorGroupNode ==
Refactor Group {0}

== AnalyzingRefactors ==
Analyzing refactors...

== WontDeleteOpenWorkspace ==
The workspace '{0}' is currently open and will not be deleted

== WontDeleteOpenWorkspaces ==
The following workspaces are currently open and will not be deleted:

== WontDeleteRepositoryWithActiveWorkspace ==
The repository '{0}' could not be deleted because it is being used by an open workspace

== WontDeleteRepositoriesWithActiveWorkspaces ==
The following repositories could not be deleted because they are being used by open workspace:

== LicenseExpiredLinkLabel ==
Your license has expired. Visit [[http://www.plasticscm.com|plasticscm.com]] to buy a license

== ExtendedTrialLicenseExpiresTodayLinkLabel ==
Your trial license expires today. Visit [[http://www.plasticscm.com|plasticscm.com]] to buy a license

== ExtendedTrialLicenseExpiresTomorrowLinkLabel ==
Your trial license expires tomorrow. Visit [[http://www.plasticscm.com|plasticscm.com]] to buy a license

== ExtendedTrialLicenseExpiresInDaysLinkLabel ==
Your trial license expires in {0} days. Visit [[http://www.plasticscm.com|plasticscm.com]] to buy a license

== TrialLicenseExpiresTodayLinkLabel ==
Your trial license expires today. Please [[https://www.plasticscm.com/account/register|sign up]] to get a free 30 days trial for 5 users. It just takes 30 seconds!

== TrialLicenseExpiresTomorrowLinkLabel ==
Your trial license expires tomorrow. Please [[https://www.plasticscm.com/account/register|sign up]] to get a free 30 days trial for 5 users. It just takes 30 seconds!

== TrialLicenseExpiresInDaysLinkLabel ==
Your trial license expires in {0} days. Please [[https://www.plasticscm.com/account/register|sign up]] to get a free 30 days trial for 5 users. It just takes 30 seconds!

== LicenseExpiresTodayLinkLabel ==
Your license expires today. Visit [[http://www.plasticscm.com|plasticscm.com]] to extend your license

== LicenseExpiresTomorrowLinkLabel ==
Your license expires tomorrow. Visit [[http://www.plasticscm.com|plasticscm.com]] to extend your license

== LicenseExpiresInDaysLinkLabel ==
Your license expires in {0} days. Visit [[http://www.plasticscm.com|plasticscm.com]] to extend your license

== CannotConnectToServerLicense ==
Cannot connect to the Unity VCS server. Retrying...

== LicenseFileFilter ==
Unity VCS license (*.lic)|lic

== TrialLicenseWarningNonAdminPrompt ==
The current user is not the server administrator. Once the extended trial license is retrieved you will be prompted to store it in a file at a custom location.

== TrialLicenseSigningUpLabel ==
Waiting for a valid login...

== DifferencesAdornerTooltip ==
Click to see the differences

== UnchangedNameGroup ==
Unchanged

== Usings ==
Usings

== BaseLabel ==
base

== SrcLabel ==
Source (their changes)

== DstLabel ==
Destination (your changes)

== BaseTooltip ==
the common ancestor, it is the parent version of the two files you are merging. It shows how the file was originally

== SrcTooltip ==
the changes made in the code you're merging from (theirs). Shows the differences between the base and the source contributor

== DstTooltip ==
the changes on your working copy (yours). Shows the differences between the base and the destination contributor

== ChangeEditorFont ==
Change editor font...

== FontSelection ==
Font selection

== SelectFontExplanation ==
Use the combo box below to select your preferred font. Check the sample text to see how it looks.

== SelectAFont ==
Select a font

== RestoreDefaultFont ==
Restore default font

== BrExTooltipDetailsForCs ==
by {0} on {1}

== BrExTooltipDetails ==
Created by {0} on {1}

== LoadingTooltipDetails ==
Loading tooltip details...

== ErrorLoadingAnnotations ==
Error calculating annotations: {0}

== ErrorLoadingTooltipDetails ==
Error loading tooltip details

== PlasticZoomInShortcut ==
ctrl++

== PlasticZoomInNumPadShortcut ==
ctrl+Add

== PlasticZoomOutShortcut ==
ctrl+-

== PlasticZoomResetShortcut ==
ctrl+D0

== PlasticZoomResetNumPadShortcut ==
ctrl+NumPad0

== PlasticZoomInShortcutMacOS ==
cmd++

== PlasticZoomInNumPadShortcutMacOS ==
cmd+Add

== PlasticZoomOutShortcutMacOS ==
cmd+-

== PlasticZoomResetShortcutMacOS ==
cmd+D0

== PlasticZoomResetNumPadShortcutMacOS ==
cmd+NumPad0

== PlasticToggleShelvesViewShortcut ==
Shift+Ctrl+H

== PlasticToggleShelvesViewShortcutMacOS ==
cmd+Shift+H

== HideUnhideShortcutForMacOS ==
cmd+H

== HideUnhideShortcut ==
ctrl+H

== ZoomReset ==
Reset Zoom

== Zoom ==
Zoom

== ZoomLevelLabel ==
{0:0}%

== UnityVCSIsEnabled ==
Unity VCS is enabled

== UnityVCSIsDisabled ==
Unity VCS is disabled

== DisableButton ==
Disable

== EnableButton ==
Enable

== DeletedRepositoryHeader ==
This repository is deleted

== ShowDeletedRepositoriesToggle ==
Show deleted repositories

== ShowDeletedRepositoriesToggleExplanation ==
Deleted repositories are kept for 7 days on the cloud. During this period deleted repositories can be restored, after this period the repository is automatically removed.

== BrExTooltipTaskDetails ==
Assigned to {0}

== BrExTooltipBranchDetails ==
Branch created by {0}

== NewVersionWindowTitle ==
New version available for update!

== ViewAllReleaseNotes ==
View all release notes

== ConfirmCancelDownloadTitle ==
Cancel download

== ConfirmCancelDownloadMessage ==
Are you sure you want to cancel the download operation?

== MainSidebarCheckForUpdatesItem ==
Check for updates

== NoNewVersionDescription ==
No new updates found

== NoNewVersionTitle ==
You are up to date!

== CheckingNewUpdates ==
Checking for new updates...

== NothingCheckedForMerge ==
No changes are checked. Please check at least one change to apply.

== UnityVersionControl ==
Unity Version Control

== UnityVersionControlSupport ==
Unity Version Control support

== UnityDevOpsVersionControl ==
Unity DevOps Version Control

== FindInFilesButton ==
Find in files

== SearchWatermark ==
Type your search here

== AllFilesOption ==
All files

== CurrentFileOption ==
Current file

== VisibleFilesOption ==
Visible files

== BothRevisionsOption ==
Source and destination

== SourceRevisionOption ==
Source

== DestinationRevisionOption ==
Destination

== MatchCaseLabel ==
Match case

== FindFilesInLabel ==
Look in

== FindMatchesInLabel ==
Find matches in

== FileExtensionLabel ==
File extension

== FileExtensionExample ==
Example: .cs, .c, .html

== FindInFilesStatusSingleResult ==
1 result found in 1 file for "{0}"

== FindInFilesStatusSingleFile ==
{0} results found in 1 file for "{1}"

== FindInFilesStatus ==
{0} results found in {1} files for "{2}"

== FindInFilesProgress ==
Searching... ({0}/{1}) {2}

== FindInFilesCanceled ==
Search canceled

== AllResultsSegment ==
All results

== SourceSegment ==
Source

== DestinationSegment ==
Destination

== Executable ==
Executable

== UnityDashboard ==
Unity Dashboard

== WebAdmin ==
Web Admin

== LoadingOrganizations ==
Loading organizations...

== More ==
More

== SigningIn ==
Signing in...

== ShowEmbeddedShelveDiffTooltip ==
Always open shelves in current window

== EmptyLabelsMessage ==
There are no labels created yet

== EmptyAttributesMessage ==
There are no attributes created yet

== EmptyCodeReviewsMessage ==
There are no code reviews created yet

== EmptyCodeReviewsMatchQueryMessage ==
There are no code reviews that match your query

== EmptyWkExplorerMessage ==
The workspace is empty

== EmptyPendingChangesMessage ==
There are no pending changes found

== EmptyIncomingChangesMessage ==
There are no incoming changes found

== EmptySyncViewMessage ==
There are no cloud syncs created yet

== LockInfoDetailsFormatString ==
{0} {1} on {2} by {3} with destination {4}

== LockMenuItemReleaseLock ==
Release

== LockMenuItemRemoveLock ==
Remove

== ConfirmReleaseLocksTitle ==
Confirm release

== ReleaseLocksMessageSingular ==
Releasing locks will allow other users to keep working on these files and retrieve locks. Would you like to release 1 lock?

== ReleaseLocksMessagePlural ==
Releasing locks will allow other users to keep working on these files and retrieve locks. Would you like to release {0} locks?

== ReleaseLocksButton ==
Release

== ReleaseLocksButtonTooltip ==
Release locks will allow other users to keep working on these files and retrieve locks

== ConfirmRemoveLocksTitle ==
Confirm remove

== RemoveLocksMessageSingular ==
Removing locks will make these files editable by anyone with access to them on the repository, increasing the risk of future merge conflicts. Would you like to remove 1 lock?

== RemoveLocksMessagePlural ==
Removing locks will make these files editable by anyone with access to them on the repository, increasing the risk of future merge conflicts. Would you like to remove {0} locks?

== RemoveLocksButton ==
Remove

== RemoveLocksButtonTooltip ==
Remove locks will make these files editable by anyone with access to them on the repository, increasing the risk of future merge conflicts

== LearnMoreAboutReleaseRemoveLocks ==
Learn more about release and remove locks

== ConfigureLockRules ==
Configure rules

== DiscoverNewSmartLocks ==
New feature: Smart Locks. Artists can now lock files safely among branches.

== PurgedRevision ==
Purged revision

== PurgedRevisionMessage ==
The content can't be displayed, this revision has been permanently deleted.

== MarkReview ==
Mark review

== MarkReviewAs ==
Mark review as...

== MarkAs ==
Mark as {0}

== ReviewStatusComment ==
Comment (optional)

== GettingReviewers ==
Getting reviewers ...

== GettingReviewComments ==
Getting review comments ...

== Conversation ==
Conversation

== ReviewChanges ==
Review changes

== Reviewers ==
Reviewers

== Author ==
Author

== AddedDescriptionTimeAgo ==
added a description {0}

== EditDescription ==
Edit description

== AddDescription ==
Add description

== AddADescription ==
Add a description

== AddADescriptionExplanation ==
Give an overview, or provide instructions about this review

== AddDescriptionPlaceHolder ==
Type your description here

== StartAConversationPlaceHolder ==
Start a conversation...

== MarkedReviewAs ==
marked review as

== SelftRequestedReview ==
self-requested a review

== RequestedReviewFrom ==
requested review from

== HideOneReply ==
Hide 1 reply

== HideReplies ==
Hide {0} replies

== ShowOneReply ==
Show 1 reply

== ShowReplies ==
Show {0} replies

== ResolvedIn ==
Resolved in

== QuestionMarkedAsResolved ==
Question marked as resolved

== Discarded ==
Discarded

== AskedAQuestion ==
asked a question

== Commented ==
commented

== RequestedChanges ==
requested changes

== AddCommentPlaceHolder ==
Add your comment here...

== Today ==
Today

== Yesterday ==
Yesterday

== LastWeek ==
Last week

== MoreThanAWeekAgo ==
More than a week ago

== DiffHeaderDetailsStringFormat ==
by {0} on {1}

== MovedFrom ==
 (moved from {0})

== BrExFiltersLabel ==
Filters:

== DateFilterButton ==
Date

== SavedFilters ==
Saved filters

== SinceSpecifiedDate ==
Since specified date

== DateRange ==
Date range

== SaveCurrentFilter ==
Save current filter

== NothingHereYet ==
Nothing here yet

== UserFilterButton ==
User

== FilterByMeButton ==
Me

== FilterByMeButtonTooltip ==
Shows branches created or contains changesets by the current user

== UnassignedButton ==
Unassigned

== UnassignedButtonTooltip ==
Shows reviews that don't have any reviewers assigned

== BranchFilterButton ==
Branch

== FilterWorkingBranchButton ==
Working branch

== FilterWorkingBranchButtonTooltip ==
Displays the branch that are currently being worked on

== OtherFilterButton ==
Other

== StatusFilterButton ==
Status

== ReviewersStatusFilterButton ==
Reviewer's status

== RelevantChangesetsText ==
View relevant changes only

== RelevantChangesetsExplanation ==
Show only the changes defining diagram structure: branch start/end, merges, labels, parents, and working changeset

== RelevantChangesetsShortText ==
Only relevant

== PendingToMergeBranchesText ==
Exclude merged branches

== PendingToMergeBranchesExplanation ==
Omit branches already merged into any parent

== PendingToMergeBranchesShortText ==
Not merged

== RelatedBranchesText ==
Show related branches

== RelatedBranchesExplanation ==
Extend view to include parent, children, and merge-connected branches for comprehensive view

== RelatedBranchesShortText ==
Related

== BranchesEmptyState ==
Oops! It looks like there are no branches matching your filters

== HiddenBranchesEmptyState ==
Oops! It looks like there are no hidden branches matching your filters

== HiddenBranchesInRepositoryEmptyState ==
Oops! It looks like there are no hidden branches for this repository

== HistoryEmptyState ==
Oops! It looks like there are no revisions matching your filters

== ChangesetsEmptyState ==
Oops! It looks like there are no changesets matching your filters

== LabelsEmptyState ==
Oops! It looks like there are no labels matching your filters

== AttributesEmptyState ==
Oops! It looks like there are no attributes matching your filters

== CodeReviewsEmptyState ==
Oops! It looks like there are no code reviews matching your filters

== ShelvesEmptyState ==
Oops! It looks like there are no shelves matching your filters

== DiffsEmptyState ==
Oops! It looks like there are no diffs matching your filters

== ResetFilters ==
Reset filters

== ReRequestReviewButton ==
Re-request review

== ListViewButton ==
List view

== TreeViewButton ==
Tree view

== ExpandAllMenuItem ==
Expand all nodes

== CollapseAllMenuItem ==
Collapse all nodes

== NewBranchButton ==
New branch

== CreateNewBranchButton ==
Create new branch

== NoBranchMatchingFilter ==
There are no branches that are matching the filter

== CreateANewBranchInstead ==
You can create a new branch instead

== MainBranchSection ==
Main branch

== RecentBranchesSection ==
Recent branches

== OtherBranchesSection ==
Other branches

== ReviewersColumn ==
Reviewers

== CommentsColumn ==
Comments

== CommentsCellTooltip ==
{0} comments and replies

== UnresolvedIssuesCellTooltip ==
{0} unresolved issues (change requests and questions)

== CreateWorkspacePanelTitle ==
Create your cloud workspace on {0} organization

== CreateWorkspacePanelDescription ==
Do you want to connect this workspace to an existing repository?

== WorkspaceWithUvcsConnectionSelectorName ==
Connect workspace to a repository

== WorkspaceWithUvcsConnectionSelectorDescription ==
I want to load source files from a Unity UVCS repository to this workspace.

== WorkspaceWithoutConnectionSelectorName ==
Don't connect workspace to a repository

== WorkspaceWithoutConnectionSelectorDescription ==
I want to import files from a local drive.

== WorkspaceNameEntry ==
Workspace name:

== NoRepositoriesToConnect ==
There are no repositories to connect the workspace to

== RepositoryAndBranchOnlyAvailableForConnectedWks ==
The repository and the branch are only available for connected workspaces

== CollaboratorsEmptyState ==
There are no collaborators yet

== Collaborators ==
Collaborators

== LoadingMainBranchProgress ==
Loading main branch...

== ConnectingWorkspaceToRepository ==
Connecting workspace to the UVCS repository...

== SharingWorkspace ==
Sharing drive...

== ExecuteQueryClearsFiltersWarningTitle ==
Clear filters warning

== ExecuteQueryClearsFiltersWarningMessage ==
Executing this query will clear the display filters you have configured for this view. After the query is executed, you can re-apply them to filter the results.

== ExecuteQueryClearsFiltersWarningPositiveButton ==
Execute query

== ClearQueryWarningTitle ==
Clear query warning

== ApplyDateFilterClearsQueryWarningMessage ==
You have a custom query set up for this view. Applying a date filter will clear it.

== ApplyDateFilterClearsQueryWarningMessagePositiveButton ==
Apply filter

== ChangingShowHiddenBranchesClearsQueryWarningMessage ==
You have a custom query set up for this view. Toggling between hidden/unhidden branches will clear it.

== ResumeSyncWhenOpenWorkspaceLinkTitle ==
Open cloud drive workspace

== ResumeSyncWhenOpenWorkspaceLinkMessage ==
You are trying to open the '{0}' workspace on your disk but the syncing is paused, so the workspace could be out-of-date. Do you want to resume the syncing now?

== RepositoriesAndWorkspaces ==
Repositories and workspaces

== AllWorkspaces ==
All workspaces

== WorkspaceMenuItemAddExistingWorkspace ==
Add existing workspace...

== NoValidWorkspaceTitle ==
No valid workspace

== NoValidWorkspaceMessage ==
The selected folder is not a UVCS workspace. Please select a valid workspace folder.

== DropFolderToOpenWorkspace ==
Drop here to open. Hold Shift to open in a new window.

== DropFolderToCreateWorkspace ==
Drop here to create a new workspace.

== DeleteBranchTitle ==
Confirm branch deletion

== DeleteBranchExplanation ==
You are about to delete {0} and all of its changesets. Are you sure you want to continue?

== DeleteBranchesExplanation ==
You are about to delete the following branches and all their changesets:

== DeleteBranchesConfirmation ==
This action cannot be undone. Are you sure you want to continue?

== ConfirmationCheckBox ==
Yes, I am sure!

== ContinueAnyway ==
Continue anyway

== ViewSyncIssuesMenuItem ==
View sync issues

== SyncIssuesDialogTitle ==
Sync issues

== SyncIssuesEmptyStateMessage ==
No issues found

== SyncIssuesEmptyStateExplanation ==
Issues related to syncing your files will be shown here

== SyncIssuesDownloadingChanges ==
Issues downloading changes

== SyncIssuesUploadingChanges ==
Issues uploading changes

== SyncIssuesCountSingular ==
(1 issues)

== SyncIssuesCountPlural ==
({0} issues)

== CopySyncIssues ==
Copy sync issues

== LastSynced ==
synced {0}

== IssuesFound ==
Issues found

== WorkspacesUnableToSyncSingular ==
Your workspace is unable to sync

== WorkspacesUnableToSyncPlural ==
{0} workspaces are unable to sync

== NoDirectorySelectedEmptyState ==
Select a directory in the tree to see its contents

== EmptyDirectoryEmptyState ==
The selected directory is empty

== CopyingFilesIndeterminateProgress ==
Copying files...

== CopyingFilesProgress ==
Copying file {0} of {1}: {2}

== AskUserForExistingFileTitle ==
File already exists

== AskUserForExistingFileMessage ==
The file {0} already exists. How would you like to handle it?

== ReplaceButton ==
Replace

== KeepBothButton ==
Keep both

== SkipButton ==
Skip

== ApplyToAllFollowingFiles ==
Apply to all following files

== CopyFileErrorsTitle == 
Copy files result

== CopyFileErrorsMessage == 
The following files couldn't be copied to the cloud workspace '{0}'.
Please check the error details below.

== CreateCloudWorkspaceTitle ==
Create a new cloud drive

== CreateCloudWorkspaceExplanation ==
Select {0} for your cloud drive where you can store all your creations, and collaborate with people.

A cloud drive contains the entire history, including all the changes of its files and directories.

== CreateCloudWorkspaceExplanationSelectForCloudOrg ==
the organization

== CreateCloudWorkspaceExplanationSelectForUnityOrg ==
the organization and the project

== OrganizationLabel ==
Organization:

== GettingOrganizations ==
Getting organizations...

== GettingProjects ==
Getting projects...

== CreatingCloudWorkspace ==
Creating drive {0}...

== CloudWorkspaceNameEntry ==
Drive name:

== CloudWorkspaceNameEmpty ==
Drive name must not be empty

== CreateCloudWorkspaceButton ==
Create drive

== ShareDriveTitle ==
Share drive to:

== ShareDriveExplanation ==
Grant your team members access to this drive for instant asset sharing and seamless collaboration.

== CollaboratorsListEmptyState ==
This organization has no users

== ManageUsersDashboardLink ==
Manage users in Dashboard

== CannotLoadBranches ==
Cannot load branches

== CannotLoadBranchesExplanation ==
We couldn't load the branches from the UVCS server. Please check your connection and try again.

== ShelvedChanges ==
Shelved changes

== ShelvedChangesExplanation ==
There are pending shelved changes. Click 'View' to manage them or use the dropdown for more options.

== ViewShelvedChangesButtonExplanation ==
View and apply shelved changes

== DiscardShelvedChanges ==
Discard shelved changes

== EnableSwitchAndShelveTitle ==
Enable the new Switch & Shelve experience!

== EnableSwitchAndShelveMessage ==
Switching in workspaces with pending changes just got better! Our new Shelve option gives you more control over your changes when switching branches, labels, or changesets.

== EnableSwitchAndShelveLeaveChangesTitle ==
Leave changes on Source:

== EnableSwitchAndShelveLeaveChangesDescription ==
You can save your changes on a shelve to keep them available for future use.

== EnableSwitchAndShelveBringChangesTitle ==
Bring changes to Target:

== EnableSwitchAndShelveBringChangesDescription ==
Move your changes seamlessly to the new branch or changeset.

== EnableSwitchAndShelveQuestionStart ==
Do you want to enable the Shelve option now?

== EnableSwitchAndShelveQuestionEnd ==
You can change the behaviour at any time in the preferences.

== EnableSwitchAndShelveYesEnableIt ==
YES, ENABLE IT!

== EnableSwitchAndShelveYesEnableItLowerCase ==
Yes, enable it!

== EnableSwitchAndShelveNotNow ==
Not now

== HideBranchConfirmationTitle ==
Hide branch

== HideBranchConfirmationMessageSingular ==
The branch will be hidden. Hidden branches can still be viewed and unhidden from the Branches view.

Do you want to continue?

== HideBranchConfirmationMessagePlural ==
The branches will be hidden. Hidden branches can still be viewed and unhidden from the Branches view.

Do you want to continue?

== DeleteBranchErrorHideBranchConfirmationTitle ==
Cannot delete branch

== DeleteBranchErrorHideBranchConfirmationMessage ==
Could not delete

== DeleteBranchErrorHideBranchConfirmationExplanation ==
You can {0} instead. Hidden branches can still be viewed and unhidden from the Branches view.

Do you want to continue?

== DeleteBranchErrorHideBranchConfirmationHideBranchSingular ==
hide it

== DeleteBranchErrorHideBranchConfirmationHideBranchPlural ==
hide them

== DiscardShelveConfirmationTitle ==
Discard shelveset

== DiscardShelveConfirmationMessage ==
The shelveset will be discarded and all of its contents will be removed.
Do you want to continue?

== OverwriteExistingShelvesWarning ==
Your current shelveset will be overwritten by creating a new one

== OverwriteShelveDialogTitle ==
Overwrite shelveset

== OverwriteShelveDialogMessage ==
Are you sure you want to overwrite the existing shelveset with your current changes?

== OverwriteShelveDialogOverwriteButton ==
Overwrite

== ShowViewButtonTooltip ==
Show {0} ({1})

== CopyFilePathMenuItem ==
Copy path

== CopyRelativeFilePathMenuItem ==
Copy relative path

== CopyLinkMenuItem ==
Copy link

== CopyLinkOnCurrentBranchMenuItem ==
Copy link (current branch)

== CopyLinkOnCurrentChangesetMenuItem ==
Copy link (current changeset)

== CloudOrganizationsSection ==
Cloud organizations

== UnityOrganizationsSection ==
Unity organizations

== AllProjects ==
All projects

== ManageOrganizationMenuItem ==
Manage organization

== ManageProjectMenuItem ==
Manage project

== ViewProjectsInUnityCloud ==
View projects in Unity Cloud

== ServerEntry ==
Server:

== MismatchingRepositoryProjectMessage ==
This Unity project is connected to [{0}], but the Unity Version Control repository belongs to [{1}].
This means that your repository will appear under [{2}] at Unity Cloud, and the storage for this repository is being charged to the organization [{3}].
If this is not correct, please re-connect this project to [{4}], or move the repository to the project [{5}].

== EmailNotifications ==
Email notifications

== SubscribeButton ==
Subscribe

== UnsubscribeButton ==
Unsubscribe

== ManageEmailNotificationsLinkLabelMessage ==
Email notifications are disabled for this repository. Manage {0} or {1} settings to opt in to notifications.

== OpenRepositorySettingsLinkLabelMessage ==
repository settings

== OpenOrganizationSettingsLinkLabelMessage ==
organization settings

== GenericSubscriptionReason ==
You're receiving notifications.

== UserIsUnsubscribedSubscriptionReason ==
Subscribe to receive email notifications for this Code Review.

== UserCommentedInReviewSubscriptionReason ==
You're receiving notifications because you commented.

== UserMentionedInReviewSubscriptionReason ==
You're receiving notifications because you were mentioned.

== UserIsReviewerSubscriptionReason ==
You're receiving notifications because you're a reviewer.

== UserIsOwnerSubscriptionReason ==
You're receiving notifications because you're the owner of the Code Review.

== UserSubscribedManuallySubscriptionReason ==
You're receiving notifications because you subscribed manually.

== ShowHiddenBranchesTooltip ==
Show hidden branches

== DontShowHiddenBranchesTooltip ==
Don't show hidden branches

== HiddenBranchTooltip ==
This branch is hidden

== PlasticConversationShortcutWindows ==
Ctrl+D1

== PlasticConversationShortcutMacOS ==
Cmd+D1

== PlasticConversationShortcutLinux ==
Ctrl+D1

== PlasticReviewChangesShortcutWindows ==
Ctrl+D2

== PlasticReviewChangesShortcutMacOS ==
Cmd+D2

== PlasticReviewChangesShortcutLinux ==
Ctrl+D2

== OpeningWorkspace ==
Opening workspace...

== LocksTutorialLabel ==
Learn how to configure locks in this {0}.

== LocksTutorialButton ==
Unity Learn tutorial

== ShowLabelsButton ==
Show labels

== UnityLabelShortcutForWindows ==
%l

== UnityLabelShortcutForMacOS ==
#%l

== UseUnityVersionControlToManageYourProject ==
Use Unity Version Control to manage your project

== UseUnityVersionControl ==
Use Unity Version Control...

== HideVersionControlToolbar ==
Hide Version Control Toolbar

== LearnMoreAboutUnityVersionControl ==
Learn more about Unity Version Control

== CheckinPendingChanges ==
Check-in Pending Changes...

== UseUnityVersionControl ==
Use Unity Version Control...

== HideVersionControlToolbar ==
Hide Version Control Toolbar

== LearnMoreAboutUnityVersionControl ==
Learn more about Unity Version Control

== CheckinPendingChanges ==
Check-in Pending Changes...

== ViewIncomingChanges ==
View incoming changes

== ViewIncomingChanges ==
View incoming changes

== UnitySwitchShortcutForWindows ==
%#w

== UnitySwitchShortcutForMacOS ==
#%w

== ShowUnityVersionControlToolbarButton ==
Show Unity Version Control button in toolbar

== PendingChangesInfo ==
{0} pending changes in your workspace
