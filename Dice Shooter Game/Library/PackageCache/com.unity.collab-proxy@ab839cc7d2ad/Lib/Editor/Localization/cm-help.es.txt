== CMD_DESCRIPTION_ACCESS_TOKEN ==
Permite al usuario administrar Tókenes Personales de Acceso.

== CMD_USAGE_ACCESS_TOKEN ==
Sintaxis:

    cm accesstoken <command> [options]

Comandos:

    - create
    - list
    - reveal
    - revoke
    - admin

    Para obtener más información de cada uno de los comandos use lo siguiente:
    cm accesstoken <command> --usage
    cm accesstoken <command> --help

== CMD_HELP_ACCESS_TOKEN ==
Ejemplos:

    cm accesstoken create "Para ser usado por el servidor de compilación" 4w
    cm accesstoken list
    cm accesstoken reveal 19c57d0f-c525-4767-8670-82f7ecc2ccdb
    cm accesstoken revoke 19c57d0f-c525-4767-8670-82f7ecc2ccdb
    cm accesstoken admin allowlist show
    cm accesstoken admin listall
    cm accesstoken admin revoke 19c57d0f-c525-4767-8670-82f7ecc2ccdb

== CMD_DESCRIPTION_ACCESS_TOKEN_ADMIN ==
Permite al propietario del servidor de repositorio administrar algunos aspectos
de los Tókenes Personales de Acceso.

== CMD_USAGE_ACCESS_TOKEN_ADMIN ==
Sintaxis:

    cm accesstoken admin <command> [options]

Comandos:

    - allowlist
    - listall
    - revoke

    Para obtener más información sobre cada comando, ejecuta:
    cm accesstoken admin <command> --usage
    cm accesstoken admin <command> --help

== CMD_HELP_ACCESS_TOKEN_ADMIN ==
Ejemplos:

    cm accesstoken admin allowlist add --user=<EMAIL>
    cm accesstoken admin allowlist remove --user=<EMAIL>
    cm accesstoken admin allowlist show
    cm accesstoken admin listall
    cm accesstoken admin revoke 19c57d0f-c525-4767-8670-82f7ecc2ccdb

== CMD_DESCRIPTION_ACCESS_TOKEN_ADMIN_ALLOWLIST ==
Permite al propietario del servidor de repositorio administrar qué usuarios y
grupos pueden utilizar Tókenes Personales de Acceso.

== CMD_USAGE_ACCESS_TOKEN_ADMIN_ALLOWLIST ==
Sintaxis:

    cm accesstoken admin allowlist <command> [options]

Comandos:

    - add
    - remove
    - show

    Para obtener más información sobre cada comando, ejecuta:
    cm accesstoken admin allowlist <command> --usage
    cm accesstoken admin allowlist <command> --help

== CMD_HELP_ACCESS_TOKEN_ADMIN_ALLOWLIST ==
Notas:

    Los Tókenes Personales de Acceso solo pueden ser utilizados por los usuarios
    y grupos incluidos en la allowlist.

    Para permitir acceso a la funcionalidad a todos los usuarios presentes
    y futuros al mismo tiempo, puede usar el usuario especial 'all':

    cm accesstoken admin allowlist add --user=all

    No podrá añadir usuarios y grupos concretos a la allowlist mientras el
    usuario especial 'all' esté presente.
    No podrá añadir el usuario especial 'all' a la allowlist mientras haya
    usuarios y grupos concretos presentes.

    Un usuario puede tener acceso a la funcionalidad por pertenecer a un grupo,
    incluso si no está incluido explícitamente en la allowlist. Recuerde esto
    al decidir qué usuarios podrán crear y administrar Tókenes Personales de
    Acceso.

Ejemplos:

    cm accesstoken admin allowlist add --users=<EMAIL>
    cm accesstoken admin allowlist remove --users=<EMAIL>
    cm accesstoken admin allowlist show

== CMD_DESCRIPTION_ACCESS_TOKEN_ADMIN_ALLOWLIST_ADD ==
Permite al propietario del servidor de repositorio añadir usuarios y grupos
a la allowlist de los Tókenes Personales de Acceso.

== CMD_USAGE_ACCESS_TOKEN_ADMIN_ALLOWLIST_ADD ==
Sintaxis:

    cm accesstoken admin allowlist add
        (--users=<usuarios>) (--groups=<grupos>) [<repserverspec>]

    repserverspec       Especificación del servidor de repositorios.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de servidor de repositorios.)
    --users             Lista separada por comas con los nombres de los usuarios
                        que se añadirán a la allowlist.
    --groups            Lista separada por comas con los nombres de los grupos
                        que se añadirán a la allowlist.

== CMD_HELP_ACCESS_TOKEN_ADMIN_ALLOWLIST_ADD ==
Notas:

    Los Tókenes Personales de Acceso solo pueden ser utilizados por los usuarios
    y grupos incluidos en la allowlist.

    Para permitir acceso a la funcionalidad a todos los usuarios presentes
    y futuros al mismo tiempo, puede usar el usuario especial 'all':

    cm accesstoken admin allowlist add --user=all

    No podrá añadir usuarios y grupos concretos a la allowlist mientras el
    usuario especial 'all' esté presente.
    No podrá añadir el usuario especial 'all' a la allowlist mientras haya
    usuarios y grupos concretos presentes.

    Un usuario puede tener acceso a la funcionalidad por pertenecer a un grupo,
    incluso si no está incluido explícitamente en la allowlist. Recuerde esto
    al decidir qué usuarios podrán crear y administrar Tókenes Personales de
    Acceso.

Ejemplos:

    cm accesstoken admin allowlist add
      --users=<EMAIL>,<EMAIL>
    (Añade a <NAME_EMAIL> y <EMAIL> a la
     allowlist en el servidor de repositorio por defecto.)

    cm accesstoken admin allowlist add
      --users=<EMAIL> --groups=CICD,Automations orgname@cloud
    (Añade <NAME_EMAIL> y a los grupos CICD y Automations
     a la allowlist en la organización orgname@cloud.)

== CMD_DESCRIPTION_ACCESS_TOKEN_ADMIN_ALLOWLIST_REMOVE ==
Permite al propietario del servidor de repositorio eliminar usuarios y grupos
de la allowlist de los Tókenes Personales de Acceso.

== CMD_USAGE_ACCESS_TOKEN_ADMIN_ALLOWLIST_REMOVE ==
Sintaxis:

    cm accesstoken admin allowlist remove
        (--users=<usuarios>) (--groups=<grupos>) [<repserverspec>]

    repserverspec       Especificación del servidor de repositorios.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de servidor de repositorios.)
    --users             Lista separada por comas con los nombres de los usuarios
                        que se eliminarán de la allowlist.
    --groups            Lista separada por comas con los nombres de los grupos
                        que se eliminarán de la allowlist.

== CMD_HELP_ACCESS_TOKEN_ADMIN_ALLOWLIST_REMOVE ==
Notas:

    Intentar eliminar un usuario o grupo que no esté en la allowlist
    producirá un error. En este caso, la allowlist no será modificada.

Ejemplos:

    cm accesstoken admin allowlist remove
      --users=<EMAIL>,<EMAIL>
    (Elimina a <NAME_EMAIL> y <EMAIL> de la
     allowlist en el servidor de repositorio por defecto.)

    cm accesstoken admin allowlist remove
     --users=<EMAIL> --groups=CICD,Automations orgname@cloud
    (Elimina <NAME_EMAIL> y a los grupos CID y Automations
      de la allowlist en la organización orgname@cloud.)

== CMD_DESCRIPTION_ACCESS_TOKEN_ADMIN_ALLOWLIST_SHOW ==
Permite al propietario del servidor de repositorio mostrar los usuarios y
grupos presentes en la allowlist de los Tókenes Personales de Acceso.

== CMD_USAGE_ACCESS_TOKEN_ADMIN_ALLOWLIST_SHOW ==
Sintaxis:

    cm accesstoken admin allowlist show [<repserverspec>]

    repserverspec       Especificación del servidor de repositorios.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de servidor de repositorios.)

== CMD_HELP_ACCESS_TOKEN_ADMIN_ALLOWLIST_SHOW ==
Ejemplos:

    cm accesstoken admin allowlist show
    (Muestra la allowlist del servidor de repositorios por defecto.)

    cm accesstoken admin allowlist show orgname@cloud
    (Muestra la allowlist de la organización orgname@cloud.)

== CMD_DESCRIPTION_ACCESS_TOKEN_ADMIN_LISTALL ==
Permite al propietario del servidor de repositorio listar todos los
Tókenes Personales de Acceso, de todos los usuarios.

== CMD_USAGE_ACCESS_TOKEN_ADMIN_LISTALL ==
Sintaxis:

    cm accesstoken admin listall [<repserverspec>]
        [--format=<str_format>] [--dateformat=<str_date_format>]

    repserverspec       Especificación del servidor de repositorios.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de servidor de repositorios.)
    --format            Muestra el mensaje de salida en el formato indicado.
                        Consulte las Notas para más información.
    --dateformat        Configura el formato de salida para la impresión de fechas.

== CMD_HELP_ACCESS_TOKEN_ADMIN_LISTALL ==
Notas:

    Los Tókenes Personales de Acceso revocados no son devueltos en el resultado.
    Sin embargo, los tókenes caducados sí.

    Parámetros de formato de salida (opción --format):

        Este comando acepta una cadena de formato para mostrar la salida.

        Los parámetros de salida de este comando son los siguientes:
        {id}             | {0}         Identificador del token.
        {description}    | {1}         Descripción del token.
        {owner}          | {2}         Propietario del token.
        {creationdate}   | {3}         Fecha de creación del token.
        {expirationdate} | {4}         Fecha de expiración del token.
        {lastusedate}    | {5}         Fecha de último uso del token.
        {tab}                          Inserta un tabulador.
        {newline}                      Inserta una nueva línea.

    Si el valor de opción '--format' no se especifica, la salida se imprimirá
    en formato tabla.

Ejemplos:

    cm accesstoken admin listall
    (Lista todos los tókenes del servidor de repositorios por defecto.)

    cm accesstoken admin listall skull:8087
    (Lista todos los tókenes del servidor de repositorios skull:8087.)

    cm accesstoken admin listall --format="{id}"
    (Lista todos los tókenes del servidor de repositorios por defecto
     y muestra únicamente su ID.)

== CMD_DESCRIPTION_ACCESS_TOKEN_ADMIN_REVOKE ==
Permite al propietario del servidor de repositorio revocar un
Token Personal de Acceso, independientemente de quién sea el propietario.

== CMD_USAGE_ACCESS_TOKEN_ADMIN_REVOKE ==
Sintaxis:

    cm accesstoken admin revoke <id> [<repserverspec>]

    id                  El ID del token a ser revocado.
    repserverspec       Especificación del servidor de repositorios.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de servidor de repositorios.)

== CMD_HELP_ACCESS_TOKEN_ADMIN_REVOKE ==
Notas:

    Tratar de revocar un Token Personal de Acceso que no exista resultará en un
    error.

    Para revocar un token es necesario usar su ID completo.

    Revocar un token no significa que los tókenes de Unity VCS ya revelados
    dejen de funcionar inmediatamente, si no que el token no será renovado
    una vez expire.
    (Use 'cm accesstoken reveal --help' para más información.)

Ejemplos:

    cm accesstoken admin revoke de0be8a4-2a7a-44c6-9c4c-82c8222dbc6f
    (Revoca el token con el ID especificado del servidor de repositorios
     por defecto.)

    cm ^accesstoken admin ^revoke de0be8a4-2a7a-44c6-9c4c-82c8222dbc6f skull:8087
    (Revoca el token con el ID especificado del servidor de repositorios
     skull:8087.)

== CMD_DESCRIPTION_ACCESS_TOKEN_CREATE== 
Crea un nuevo Token Personal de Acceso.

== CMD_USAGE_ACCESS_TOKEN_CREATE == 
Sintaxis:

    cm accesstoken create <description> <lifetime> [<repserverspec>]
        [--format=<str_format>] [--dateformat=<str_date_format>]

    description         Descripción que ayuda a identificar el token.
    lifetime            Tiempo de vida del token, en formato "estilo Go" o
                        "sufijo Unix". Consulte las Notas para más información.
    repserverspec       Especificación del servidor de repositorios.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de servidor de repositorios.)
    --format            Muestra el mensaje de salida en el formato indicado.
                        Consulte las Notas para más información.
    --dateformat        Configura el formato de salida para la impresión de fechas.

== CMD_HELP_ACCESS_TOKEN_CREATE ==
Notas:

    El tiempo de vida del token se especifica con una cadena de texto,
    consistente en una secuencia de parejas <número><unidad>[<n><u>...],
    donde las siguientes unidades son válidas:
        w                   Semanas
        d                   Días
        h                   Horas
        m                   Minutos
        s                   Segundos

    Los siguientes ejemplos son cadenas válidas:
        1w                  1 semana
        5d                  5 días
        12h                 12 horas
        30m                 30 minutos
        35s                 35 segundos
        1h30m               1 hora y 30 minutos

    Parámetros de formato de salida (opción --format):

    Este comando acepta una cadena de formato para mostrar la salida.

    Los parámetros de salida de este comando son los siguientes:
        {id}             | {0}         Identificador del token.
        {description}    | {1}         Descripción del token.
        {owner}          | {2}         Propietario del token.
        {creationdate}   | {3}         Fecha de creación del token.
        {expirationdate} | {4}         Fecha de expiración del token.
        {lastusedate}    | {5}         Fecha de último uso del token.
        {tab}                          Inserta un tabulador.
        {newline}                      Inserta una nueva línea.

    Si el valor de opción '--format' no se especifica, la salida se imprimirá
    en formato tabla.

Ejemplos:

    cm accesstoken create "Token del servidor de compilación" 4w
    (Crea un nuevo token con la descripción dada y un tiempo de vida
     de 4 semanas contra el servidor de repositorios por defecto.)

    cm accesstoken create "Token del servidor de compilación" 365d skull:8087
    (Crea un nuevo token con la descripción dada y un tiempo de vida de 365 días
     en el servidor de repositorios skull:8087.)

    cm accesstoken create "Token del servidor de compilación" 12h --format="{id}"
    (Crea un nuevo token con la descripción dada y un tiempo de vida de 12 horas
     contra el servidor de repositorios por defecto, y muestra únicamente su ID.)

== CMD_DESCRIPTION_ACCESS_TOKEN_LIST ==
Lista Tókenes Personales de Acceso.

== CMD_USAGE_ACCESS_TOKEN_LIST ==
Sintaxis:

    cm accesstoken list [<repserverspec>]
        [--format=<str_format>] [--dateformat=<str_date_format>]

    repserverspec       Especificación del servidor de repositorios.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de servidor de repositorios.)
    --format            Muestra el mensaje de salida en el formato indicado.
                        Consulte las Notas para más información.
    --dateformat        Configura el formato de salida para la impresión de fechas.

== CMD_HELP_ACCESS_TOKEN_LIST ==
Notas:

    Solo puede listar sus propios Tókenes Personales de Acceso.
    Los tókenes revocados no son devueltos en el resultado. Sin embargo,
    los tókenes caducados sí.

    Parámetros de formato de salida (opción --format):

        Este comando acepta una cadena de formato para mostrar la salida.

        Los parámetros de salida de este comando son los siguientes:
        {id}             | {0}         Identificador del token.
        {description}    | {1}         Descripción del token.
        {owner}          | {2}         Propietario del token.
        {creationdate}   | {3}         Fecha de creación del token.
        {expirationdate} | {4}         Fecha de expiración del token.
        {lastusedate}    | {5}         Fecha de último uso del token.
        {tab}                          Inserta un tabulador.
        {newline}                      Inserta una nueva línea.

    Si el valor de opción '--format' no se especifica, la salida se imprimirá
    en formato tabla.

Ejemplos:

    cm accesstoken list
    (Lista los tókenes en el servidor de repositorios por defecto.)

    cm accesstoken list skull:8087
    (Lista los tókenes en el servidor de repositorios skull:8087.)

    cm accesstoken list --format="{id}"
    (Lista los tókenes en el servidor de repositorios por defecto y muestra
    únicamente su ID.)

== CMD_DESCRIPTION_ACCESS_TOKEN_REVOKE ==
Revoca un Token Personal de Acceso existente.

== CMD_USAGE_ACCESS_TOKEN_REVOKE ==
Sintaxis:

    cm accesstoken revoke <id> [<repserverspec>]

    id                  El ID del token a ser revocado.
    repserverspec       Especificación del servidor de repositorios.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de servidor de repositorios.)

== CMD_HELP_ACCESS_TOKEN_REVOKE ==
Notas:

    Solo puede revocar sus propios Tókenes Personales de Acceso.
    Tratar de revocar un token que no exista o que no le pertenezca resultará
    en un error.
    (Use 'cm accesstoken list' para más información.)

    Para revocar un token es necesario usar su ID completo.

    Revocar un token no significa que los tókenes de Unity VCS ya revelados
    dejen de funcionar inmediatamente, si no que el token no será renovado
    una vez expire.
    (Use 'cm accesstoken reveal --help' para más información.)

Ejemplos:

    cm accesstoken revoke de0be8a4-2a7a-44c6-9c4c-82c8222dbc6f
    (Revoca el token con el ID especificado del servidor de repositorios
     por defecto.)

    cm ^accesstoken ^revoke de0be8a4-2a7a-44c6-9c4c-82c8222dbc6f skull:8087
    (Revoca el token con el ID especificado del servidor de repositorios
     skull:8087.)

== CMD_DESCRIPTION_ACCESS_TOKEN_REVEAL ==
Muestra un Token Personal de Acceso para que pueda ser utilizado como credencial.

== CMD_USAGE_ACCESS_TOKEN_REVEAL ==
Sintaxis:

    cm accesstoken reveal <id> [<repserverspec>]

    id                  El ID del token a ser revelado.
    repserverspec       Especificación del servidor de repositorios.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de servidor de repositorios.)

== CMD_HELP_ACCESS_TOKEN_REVEAL ==
Notas:

    Solo puede revelar sus propios Tókenes Personales de Acceso.
    Tratar de revelar un token que no exista o que no le pertenezca resultará
    en un error.
    (Use 'cm accesstoken list' para más información.)

    Para revelar un token es necesario usar su ID completo.

    El token revelado puede ser utilizado en automatizaciones. Puede utilizarlo
    en posteriores comandos del 'cm' (tanto en la misma máquina como en una
    diferente) utilizando los siguientes argumentos (reemplácelos adecuadamente):

        $ cm repo list \
            --username=Pedro \
            --workingmode=OIDCWorkingMode \
            --token="el token revelado" \
            --server=skull:8087

    El token revelado será válido desde el mismo momento en el que se muestra,
    y se renovará automáticamente mientras que el Token Persona de Acceso
    relacionado no haya expirado ni haya sido revocado.

Ejemplos:

    cm accesstoken reveal d2f43753-f612-4e51-b9b4-3c2883d7cf95
    (Revela el token con el ID proporcionado en el servidor de repositorios
     por defecto.)

    cm accesstoken reveal d2f43753-f612-4e51-b9b4-3c2883d7cf95 skull:8087
    (Revela el token con el ID proporcionado en el servidor de repositorios
     skull:8087.)

== CMD_DESCRIPTION_ACL ==
Configura permisos para un objeto.

== CMD_USAGE_ACL ==
Sintaxis:

    cm acl (--user=<usr_name> | --group=<group_name>)
           (-allowed|-denied|-overrideallowed|-overridedenied=+|-<permission>[,...])[,...]
           <objectspec>

    --user              Nombre de usuario.
    --group             Nombre de grupo.
    -allowed            Habilita los permisos indicados. Use una coma para
                        separar permisos. (Use 'cm showpermissions' para mostrar
                        los permisos disponibles.)
    -denied             Deniega los permisos indicados. Use una coma para
                        separar permisos. (Use 'cm showpermissions' para mostrar
                        los permisos disponibles.)
    -overrideallowed    Anula los permisos habilitados. Use una coma para separar
                        permisos. (Use 'cm showpermissions' para mostrar
                        los permisos disponibles.)
    -overridedenied     Anula los permisos denegados. Use una coma para separar
                        permisos. (Use 'cm showpermissions' para mostrar
                        los permisos disponibles.)
    objectspec          Objeto sobre el que configurar los permisos.
                        Este objeto puede ser uno de los siguientes:
                        servidor de repositorios, repositorio, rama, etiqueta
                        o atributo.
                        (Use 'cm help objectspec' para más información sobre
                        las especificaciones de objetos.)

Uso especial para rutas seguras:
    cm acl [(--user=<usr_name> | --group=<group_name>)
            (-allowed|-denied|-overrideallowed|-overridedenied=+|-<permission>[,...])[,...]]
            [--delete] [--branches=[+ | -]<branch>[,...]] 
            <spec>

    --delete            Elimina una ruta segura.
                        Consulte las Notas para más información.
    --branches          Configura los permisos de una ruta segura a un grupo de
                        ramas. Use una coma para separar las ramas.
                        Opcionalmente, cada rama puede ir precedida de un signo
                        + o - para especificar si la rama debe añadirse o
                        eliminarse de la lista cuando se está editando.
                        Consulte las Notas para más información.
    spec                La ruta segura donde configurar los permisos.

== CMD_HELP_ACL ==
La configuración de permisos requiere entender cómo funciona la seguridad en
Unity VCS. Lea la Guía de Seguridad para aprender cómo funcionan estos 
permisos:
https://www.plasticscm.com/download/help/securityguide

Notas:

    Este comando configura permisos para un usuario o un grupo en los objetos,
    repositorios, ramas, etiquetas o rutas de servidor especificados.

    Especificaciones de objeto:
        (Use 'cm help objectspec' para más información sobre las especificaciones
        de objetos.)
        El comando acl usa un tipo especial de especificación: rutas seguras.

        - Especificación de rutas seguras:
            path:server_path[#tag]
            Ejemplos: path:/src/foo.c
                      path:/doc/pdf
                      path:/doc/pdf#documents

    Acciones de permisos:
        Use -allowed y -denied para especificar qué permisos aplicar.
        Use los argumentos -overrideallowed y -overridedenied para especificar
        qué permisos se anulan.

        Cada acción requiere una lista de permisos separados por comas.

    Nombres de permisos:
        Cada nombre de permiso debe ir precedido del símbolo + o -.
        El símbolo + aplica el permiso y el símbolo - lo elimina.
        Para ver los permisos de un objeto use el comando 'cm showacl'.

    Permisos anulados:
        Anular un permiso usando --overrideallowed y --overridedenied permite
        evitar la herencia de permisos.
        Esto es útil para evitar los permisos aplicados a nivel de repositorio o
        de servidor.
        Por ejemplo:
            cm acl --user=vio -allowed=+ci -overrideallowed=+ci br:qa@test
            (Permite a la usuaria 'vio' hacer checkin en la rama 'qa' del 
            repositorio 'test' incluso si ella tiene los permisos denegados a 
            nivel de repositorio.)

    Permisos de rutas de servidor (rutas seguras):
        - Se pueden especificar permisos para una ruta de servidor dada.
        - Estos permisos se comprueban durante la operación de checkin.
        - Estos permisos también se pueden comprobar durante la operación de
          update y se pueden usar para evitar que ciertos directorios y ficheros
          se descarguen al espacio de trabajo.
        - Por cada ítem que se proteja (checkin), el servidor intenta concordar
          la ruta del ítem con una ruta segura. Si se encuentra, entonces la
          operación de checkin comprueba si el ítem tiene permisos para ser 
          protegido (checked in).

        Los permisos que se pueden definir para una ruta segura son:
            ci, change, add, move, rm, read

        Si la comprobación de permisos no es satisfactoria para ninguno de los 
        ítems involucrados, entonces la operación de checkin se revierte.

        Para aplicar permisos de rutas seguras a un grupo de ramas, use la 
        opción --branches.
        Por ejemplo:
          cm acl --user=jo -denied=+ci path:/src#rule0 --branches=main,main/rel0

        Para editar el ACL asociado a una ruta segura, use el símbolo "#".
        Por ejemplo:
          cm acl --user=jo -denied=+rm path:/src#rule0
          (Sin "#", la lista de ramas tendría que especificarse de nuevo).

        La lista de ramas de una ruta segura se puede editar.
        Por ejemplo:
          cm acl path:/src#rule0 --branches=-main,+main/rel1
          (Elimina 'main' de la lista y añade 'main/rel1'.)

        Para eliminar una ruta segura, use el argumento --delete.
        Por ejemplo:
          cm acl --user=jo --delete path:/src#rule0

    Herencia:
        Herencia es una opción que proviene de los inicios de Plastic SCM 3.0.
        Es una opción avanzada pero también obsoleta.
        Permite a un objeto heredar los permisos de otro objeto anulando las
        relaciones de herencia por defecto.

        Use la opción -cut para cortar la cadena de herencia.
        Usd la opción -cutncpy para cortar y copiar los permisos heredados.
        (Esto está inspirado en los permisos de sistema de Windows donde se
        puede cortar herencia pero mantener los permisos actuales.)

        La opción -inherit permiter heredar de una especificación de objeto.
        Por ejemplo: '-inherit=object_spec'

Ejemplos:

    cm acl --user=danipen -denied=+ci rep:core
    (Deniega el permiso de checkin al usuario 'danipen' en el repositorio 'core'.)

    cm acl --group=developers -allowed=+view,-read -denied=+chgperm br:main
    (Otorga el permiso 'view', elimina el permiso 'read' y deniega el permiso
    'chgperm' al grupo 'developers' en la rama 'main'.)

Ejemplos con rutas de servidor:

    cm acl --group=devs -denied=+ci path:/server#rel --branches=main,main/2.0
    (Deniega el permiso de checkin al grupo 'devs' para cualquier ruta que
    concuerde con '/server' en las ramas 'main' y 'main/2.0'. La etiqueta '#rel'
    se crea para poder referirse a ella más adelante.)

    cm acl path:/server#rel --branches=-/main,+/main/Rel2.1
    (Actualiza la ruta segura '/server' cuya etiqueta es 'rel', borrando la 
    rama 'main' y añadiendo la rama 'main/Rel2.1' al grupo de ramas al que la 
    ruta segura aplica. Teniendo en cuenta el ejemplo anterior, ahora la lista
    de ramas contendrá 'main/Rel2.1' y 'main/2.0'.)

    cm acl --user=vsanchezm -allowed=-read -overrideallowed=+read path:/doc
    (Eliminar el permiso de lectura 'read' a 'vsanchezm' anulándolo en la ruta
    '/doc'.)

== CMD_DESCRIPTION_ACTIVATEUSER ==
Activa un usuario con licencia.

== CMD_USAGE_ACTIVATEUSER ==
Sintaxis:

    cm activateuser | au <user-name>[ ...] [--server=<rep-server-spec>]

    user-name   Nombre o nombres de usuario a activar. Use comillas dobles (" ")
                para especificar nombres de usuario que contengan espacios. Use
                un espacio en blanco para separar nombres de usuarios.

Opciones:
    --server=<rep-server-spec>  Activa al usuario en el servidor indicado.
                                Si no se especifica ningún servidor, el comando
                                se ejecuta en el servidor por defecto indicado
                                en el fichero client.conf.
                                (Use 'cm help objectspec' para más información
                                sobre las especificaciones de servidores de
                                repositorios.)

== CMD_HELP_ACTIVATEUSER ==
Notas:

    Para activar un usuario debe haber sido desactivado previamente.
    Por defecto, un usuario se activa por primera vez cuando lleva a cabo una
    operación de escritura en Unity VCS. El usuario se activa automáticamente
    si no se ha excedido el número máximo de usuarios.

    (Use 'cm help deactivateuser' para más información sobre desactivar usuarios
    en Unity VCS.)

Examples:

    cm activateuser john
    cm activateuser david "mary collins"
    cm au peter --server=localhost:8087

== CMD_DESCRIPTION_ADD ==
Añade un ítem al control de versiones.

== CMD_USAGE_ADD ==
Sintaxis:

    cm add [-R | -r | --recursive] [--silent] [--ignorefailed]
           [--skipcontentcheck] [--coparent] [--filetypes=<file>] [--noinfo]
           [--format=<str-format>] [--errorformat=<str-format>] 
           <item-path>[ ...]

    item-path   Ítem o ítems a añadir. Use comillas dobles (" ") para especificar
                rutas que contengan espacios. Use un espacio en blanco para
                separar rutas de ítems.
                Use * para añadir todo el contenido del directorio actual.

Opciones:

    -R|-r|--recursive   Añade los ítems recursivamente.
    --silent            No muestra ninguna salida.
    --ignorefailed      Si un ítem no se puede añadir, la operación de add
                        continuará sin él. Importante: si un directorio no se 
                        puede añadir, su contenido tampoco se añadirá.
    --skipcontentcheck  Cuando la extensión no es suficiente para determinar si
                        el fichero es de texto o binario, se asumirá que es
                        binario en lugar de usar el contenido para detectar el 
                        tipo. Este funcionamiento se asume para aumentar el
                        rendimiento en grandes operaciones de checkin.
    --coparent          Hace checkout al padre del ítem que se va a añadir.
    --filetypes         Fichero con los tipos de ficheros a usar. Consulte el
                        siguiente enlace para más información:
                        http://blog.plasticscm.com/2008/03/custom-file-types.html
    --noinfo            No muestra información de progreso.
    --format            Muestra el mensaje de salida con el formato indicado.
                        Consulte los ejemplos para más información.
    --errorformat       Muestra el mensaje de error (si existe) con el formato
                        indicado. Consulte los ejemplos para más información.

== CMD_HELP_ADD ==
Notas:

    Requisitos para añadir ítems:
    - El directorio padre del ítem a añadir debe haber sido añadido previamente.

Lectura desde stdin:

    El comando 'add' puede leer rutas desde stdin. Para ello, pase como
    argumento un guión "-".
    Por ejemplo: cm add -

    Las rutas se leerán hasta que se introduzca una línea en blanco.
    Este tipo de lectura permite usar una tubería (pipe) para especificar qué
    ficheros se van a añadir.
    Por ejemplo:
      dir /S /B *.c | cm add -
      (En Windows, añade todos los ficheros del workspace con extensión .c.)

Ejemplos:

    cm add file1.txt file2.txt
    (Añade los ítems file1.txt y file2.txt.)

    cm add c:\workspace\file.txt
    (Añade el ítem file.txt en la ruta c:\workspace.)

    cm add -R c:\workspace\src
    (Añade recursivamente el contenido de la ruta src.)

    cm add -R *
    (Añade recursivamente todo el contenido del directorio actual.)

    cm add -R * --filetypes=filetypes.conf
    (Añade recursivamente todo el contenido del directorio actual usando el
    fichero 'filetypes.conf' para asignar un tipo a cada fichero basándose en su
    extensión en lugar de en el contenido.)

    cm add --coparent c:\workspace\dir\file.txt
    (Añade el fichero 'file.txt' y hace checkout a 'dir'.)

    cm add -R * --format="ADD {0}" --errorformat="ERR {0}"
    (Añade recursivamente todo el contenido del directorio actual y muestra por
    pantalla 'ADD <item>' para cada archivo añadido y 'ERR <item>' para cada
    archivo que no se pudo añadir.)

== CMD_USAGE_ADDIGNOREPATTERN ==
Uso:

      cm addignorepattern <pattern>[ ...] 
                          [--workspace=<wkpath> | --allworkspaces] [--remove]

== CMD_DESCRIPTION_ADMIN ==
Ejecuta comandos de administración en el servidor.

== CMD_USAGE_ADMIN ==
Sintaxis:

    cm admin <command> [options]

Comandos:

    readonly

    Para obtener más información de cada uno de los comandos use lo siguiente:
    cm admin <command> --usage
    cm admin <command> --help

== CMD_HELP_ADMIN ==
Notas:
    Sólo el administrador del servidor puede ejecutar comandos de administración.

Ejemplos:

    cm admin readonly enter
    cm admin readonly status

== CMD_DESCRIPTION_ADMIN_READONLY ==
Habilita/deshabilita el modo solo lectura del servidor.

== CMD_USAGE_ADMIN_READONLY ==
Sintaxis:

    cm admin readonly (enter | leave | status) [<server>]

Acciones:

    enter   Habilita el modo solo lectura en el servidor. Las operaciones de
            escritura se rechazan.
    leave   Deshabilita el modo solo lectura en el servidor.
    status  Muestra el estado del modo solo lectura en el servidor.

Opciones:
    server  Ejecuta el comando en el servidor indicado (servidor:puerto). (Use 
            'cm help objectspec' para más información sobre las especificaciones
            de servidor.)
            Si no se especifica ningún servidor, el comando actúa en el servidor
            del espacio de trabajo actual.
            Si la ruta actual no corresponde a un espacio de trabajo, el comando
            actúa sobre el servidor por defecto indicado en el fichero client.conf.

== CMD_HELP_ADMIN_READONLY ==
Notas:
    Solo el administrador del servidor puede configurarlo como solo lectura.

Ejemplos:

    cm admin readonly enter diana:8086
    cm admin readonly leave

== CMD_DESCRIPTION_ANNOTATE ==
Muestra el changeset donde se modificó por última vez y por quién cada línea de
un fichero.

== CMD_USAGE_ANNOTATE ==
Sintaxis:

    cm annotate | blame <spec>[ ...]
        [--format=<str_format>]
        [--comparisonmethod=(ignoreeol | ignorewhitespaces 
            | ignoreeolandwhitespaces | recognizeall)]
        [--dateformat=<str_date_format>]
        [--encoding=<name>]
        [--stats]
        [--repository=<repspec>]

    spec        Especificación del fichero que se va a anotar.
                (Use 'cm help objectspec' para obtener más información sobre
                especificaciones.)
                Use comillas dobles (" ") para especificar rutas que contengan
                espacios.

Opciones:

    --format            Muestra el mensaje de salida en el formato indicado.
                        Consulte las Notas para más información.
    --comparisonmethod  Configura el método de comparación indicado.
                        Consulte las Notas para más información.
    --dateformat        Configura el formato de salida para la impresión de fechas.
    --encoding          Especifica el encoding que se usará en la salida, por
                        ejemplo, utf-8. Consulte la documentación de MSDN en
                        http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                        para obtener la tabla de codificaciones soportadas y su
                        formato (al final de la página en la columna "Name").
    --stats             Muestra estadísticas.
    --repository        Especifica el repositorio a usar para calcular las
                        anotaciones. Por defecto, se usa el repositorio del
                        workspace cuya revisión está cargada. (Use
                        'cm help objectspec' para más información sobre las
                        especificaciones de repositorio.)

== CMD_HELP_ANNOTATE ==
Notas:

     Los ítems de tipo binario no pueden ser anotados.

    Métodos de comparación (opción --comparisonmethod):
        ignoreeol                 Ignora diferencias en final de línea.
        ignorewhitespaces         Ignora diferencias en espacios.
        ignoreeolandwhitespaces   Ignora diferencias en final de línea y espacios.
        recognizeall              Detecta diferencias en final de línea y espacios.

    Parámetros de formato de salida (opción --format):
        Este comando acepta una cadena de formato para mostrar la salida.
        Los parámetros de salida de este comando son los siguientes:
        {owner}        Usuario que hizo el último cambió en la línea.
        {rev}          Especificación de la revisión de la línea.
        {content}      Contenido de la línea.
        {date}         Fecha de checkin de la línea.
        {comment}      Comentario de la revisión de la línea.
        {changeset}    Changeset de la revisión de la línea.
        {line}         Número de línea del fichero.
        {id}           Identificador del ítem.
        {parentid}     Identificador del padre del ítem.
        {rep}          Repositorio del ítem.
        {branch}       Rama de la revisión de la línea.
        {ismergerev}   Si la revisión de la línea se creó en un merge.

    --dateformat:
        Para especificar el formato en el que se escribirán las fechas en pantalla.
        Consulte los formatos soportados:
        https://docs.microsoft.com/en-us/dotnet/standard/base-types/custom-date-and-time-format-strings

    --repository:
        Para devolver los datos de un repositorio remoto. Esto es útil cuando se
        trabaja con escenarios distribuidos.

Ejemplos:

    cm blame c:\workspace\src --comparisonmethod=ignoreeolandwhitespaces --encoding=utf-8
    cm annotate c:\workspace\file.txt --comparisonmethod=ignoreeol

    cm annotate c:\workspace\file.txt --format="{owner} {date, 10} {content}"
    (Muestra el usuario que hizo el último cambio, un espacio, la fecha alineada
    a la derecha, un espacio y el contenido de la línea.)

    cm blame c:\workspace\file.txt --format="{owner, -7} {comment} {date}" \
        --dateformat=yyyyMMdd
    (Muestra en 7 espacios (y alineado a la izquierda) el usuario que hizo el
    último cambio, un espacio, el comentario seguido de otro espacio y la fecha
    formateada (por ejemplo, 20170329).)

    cm annotate c:\workspace\file.txt --repository=centralRep@myserver:8084

    cm blame serverpath:/src/client/checkin/Checkin.cs#cs:73666
    (Muestra las anotaciones del fichero (usando una especificacion de ruta de
    servidor) comenzando por el changeset 73666.)

== CMD_DESCRIPTION_APPLYLOCAL ==
Busca cambios locales (movidos localmente, borrados localmente y modificados
localmente) y los aplica para que Unity VCS pueda monitorizarlos.

== CMD_USAGE_APPLYLOCAL ==
Sintaxis:

    cm applylocal | al [--dependencies] [<item_path>[ ...]]
                    [--machinereadable [--startlineseparator=<sep>]
                      [--endlineseparator=<sep>] [--fieldseparator=<sep>]]

Opciones:

    --dependencies        Añade las dependencias de cambios locales a los ítems
                          a aplicar.
    item_path             Ítems a aplicar. Use comillas dobles (" ")
                          para especificar rutas que contengan espacios. Use un
                          espacio en blanco para separar rutas de ítems.
    --machinereadable     Muestra el resultado en un formato fácil de parsear.
    --startlineseparator  Usado con '--machinereadable', indica cómo deben
                          empezar las líneas del resultado.
    --endlineseparator    Usado con '--machinereadable', indica cómo deben
                          terminar las líneas del resultado.
    --fieldseparator      Usado con '--machinereadable', indica cómo deben
                          separarse los campos de cada línea resultante.

== CMD_HELP_APPLYLOCAL ==
Notas:

    - Si no se especifican las opciones --dependencies y <item_path>, la operación
      involucra a todos los cambios locales en el espacio de trabajo.

    - Siempre se aplican recursivamente desde la ruta indicada.

Ejemplos:

    cm applylocal foo.c bar.c

    cm applylocal .
    (Aplica todos los cambios locales del directorio actual.)

    cm applylocal
    (Aplica todos los cambios locales del espacio de trabajo.)

    cm applylocal --machinereadable
    (Aplica todos los cambios locales del espacio de trabajo y muestra por
    pantalla el resultado en un formato simple y fácil de parsear.)

    cm applylocal --machinereadable --startlineseparator=">" \
      --endlineseparator="<" --fieldseparator=","
    (Aplica todos los cambios locales del espacio de trabajo y muestra por
    pantalla el resultado en un formato simple y fácil de parsear, comenzando y
    terminando cada línea con los separadores indicados y separando los campos
    de cada línea con el separador también indicado.)

== CMD_DESCRIPTION_ARCHIVE ==
Almacena datos en almacenamiento externo.

== CMD_USAGE_ARCHIVE ==
Sintaxis:

    cm archive | arch <revspec>[ ...] [-c=<str_comment>]
                        [--file=<base_file>]
    (Extrae datos del repositorio y los almacena en un almacenamiento externo.)

    cm archive | arch <revspec>[ ...] --restore
    (Restaura al repositorio datos previamente almacenados.)

    revspec     Una o más especificaciones de revisión.(Use 'cm help objectspec'
                para más información sobre especificaciones de revisiones.)
    --restore   Restaura desde los ficheros generados durante el almacenamiento
                los datos previamente almacenados. El almacenamiento externo y
                el fichero externaldata.conf tienen que estar disponibles en el
                momento de la restauración de la revisión. Consulte las Notas
                para más información.

Opciones:

    -c              Aplica un comentario en los ficheros de almacenamiento que
                    se van a crear.
    --file          Prefijo y ruta (opcional) de los nuevos ficheros de
                    almacenamiento de datos.

== CMD_HELP_ARCHIVE ==
Notas:

    Este comando extrae datos del repositorio y los almacena en un almacenamiento
    externo, ahorrando así espacio en la base de datos.
    El comando también restaura (--restore) las revisiones que previamente
    fueron archivadas para volverlas a introducir en el repositorio.

    Use 'cm help objectspec' para saber más sobre especificaciones de revisiones.

    El usuario que ejecute este comando debe ser administrador del servidor
    de Unity VCS (propietario del servidor de repositorios) para que se le
    permita completar la operación.

    Cada segmento de datos de las revisiones especificadas se almacenará en
    un fichero diferente cuyo nombre comenzará por el texto definido en el
    argumento --file. Este argumento puede ser un path completo incluyendo un
    prefijo o únicamente el prefijo.

    Una vez archivados los datos de las revisiones, se podrá acceder a ellos
    de dos maneras:

    1. Desde el cliente: El cliente de Unity VCS detectará si los datos fueron
       fueron archivados y pedirá al usuario que indique la ubicación de los
       ficheros.
       El usuario configurará las ubicaciones de almacenamiento externo en un
       fichero llamado externaldata.conf. El fichero se creará en las ubicaciones
       estándar donde se encuentran los ficheros de configuración y siguiendo las
       mismas reglas que se aplican para el fichero client.conf. El fichero
       externaldata.conf contendrá las rutas en los que se encuentren los datos
       archivados.

    2. Desde el servidor: Con este método, los usuarios no tienen que saber si
       los datos han sido archivados o no ya que las peticiones se resuelven de
       forma transparente por el servidor. Para ello, el administrador creará un
       fichero llamado externaldata.conf en el directorio del servidor e
       introducirá las rutas donde se archivaron los datos.

    Para restaurar (restore) los datos de una revisión (o conjunto de
    revisiones), el cliente tiene que poder acceder a los ficheros donde los
    datos se almacenaron. Por tanto, no es posible restaurar datos que estén
    siendo resueltos por el servidor (método 2) porque el cliente no podrá
    identificarlos como archivados.
    Para restaurar usando el método 2, el administrador tiene que editar el
    fichero externaldata.conf en el servidor para eliminar el acceso a los
    ficheros archivados para que puedan ser restaurados.

    Ejemplo para archivar:
    1) Archivar una revisión:
       cm archive Assets/RoofTextures/Textures/Wooden_Roof_05.png --file=/Users/<USER>/archive/battle
    2) Es posible comprobar la revisión archivada en la ruta especificada:
       ls -al /Users/<USER>/archive/battle*
       -rw-r--r--  1 <USER>  <GROUP>  2220039 Nov  9 10:52 /Users/<USER>/archive/battle-100280-167
    
    Ejemplo para restaurar:
    1) Añadir la ruta de almacenamiento al fichero externaldata.conf:
       vi /Users/<USER>/.plastic4/externaldata.conf
       /Users/<USER>/archive
    2) Restaurar la revisión:
       cm archive Assets/RoofTextures/Textures/Wooden_Roof_05.png --restore

    Configure la variable de entorno PLASTICEDITOR para especificar un editor
    para escribir el comentario. Si la variable de entorno PLASTICEDITOR está
    configurada pero no se indica ningún comentario, el editor se abrirá para
    que pueda escribir el comentario.

Lectura desde stdin:

    El comando 'archive' puede leer rutas desde stdin. Para ello, pase como
    argumento un guión "-".
    Por ejemplo: cm archive -

    Las rutas se leerán hasta que se introduzca una línea en blanco.
    Este tipo de lectura permite usar una tubería (pipe) para especificar qué
    ficheros se van a almacenar (archive).
    Por ejemplo:
      dir /S /B *.c | cm archive --all -
      (En Windows, almacena (archive) todos los ficheros del workspace con
      extensión .c.)

Ejemplos:

    cm archive bigfile.zip#br:/main
    (Almacena la última revisión de 'bigfile.zip' en la rama 'main'.)

    cm archive bigfile.zip#br:/main --restore
    (Restaura la revisión almacenada.)

    cm archive rev:myfile.pdf#cs:2 -c="big pdf file" --file=c:\arch_files\arch
    (Archiva la revisión del changeset 2 de myfile.pdf en la carpeta
    'c:\archived_files'. El nombre del fichero de almacenamiento comenzará con
    'arch' (por ejemplo, arch_11_56).)

    cm find "revs where size > 26214400" --format="{item}#{branch}" \
      --nototal | cm archive -c="volume00" --file="volume00" -
    (Archiva todos los ficheros de tamaño mayor de 25Mb en ficheros de
    almacenamiento cuyo nombre empiece por 'volume00'.)


== CMD_DESCRIPTION_ATTRIBUTE ==
Permite al usuario administrar atributos.

== CMD_USAGE_ATTRIBUTE ==
Sintaxis:

    cm attribute | att <command> [options]

Comandos:

    create | mk
    delete | rm
    set
    unset
    rename
    edit

    Para obtener más información de cada uno de los comandos use lo siguiente:
    cm attribute <command> --usage
    cm attribute <command> --help

== CMD_HELP_ATTRIBUTE ==
Ejemplos:

    cm attribute create status
    cm attribute set att:status br:/main/SCM105 open
    cm attribute unset att:status br:/main/SCM105
    cm attribute delete att:status
    cm attribute rename att:status "buildStatus"
    cm attribute edit att:status "Status of the task in the CI pipeline"

== CMD_DESCRIPTION_CHANGELIST ==
Agrupa los cambios pendientes en listas de cambios (changelists).

== CMD_USAGE_CHANGELIST ==
Sintaxis:

    a) Manejo de los objetos 'changelist':

       cm changelist | clist [--symlink]
       (Muestra todas las listas de cambios.)

       cm changelist | clist create <clist_name>
          [<clist_desc>] [--persistent | --notpersistent] [--symlink]
       (Crea una lista de cambios.)

       cm changelist | clist delete <clist_name> [--symlink]
       (Borra la lista de cambios indicada. Si dicha lista contiene cambios
       pendientes, estos se moverán a lista de cambios 'default'.)

       cm changelist | clist edit <clist_name> [<action_name> <action_value>]
                             [--persistent | --notpersistent] [--symlink]
       (Edita la lista de cambios indicada.)

    b) Manejo del contenido de un 'changelist':

       cm changelist | clist <clist_name> (add | rm) <path_name>[ ...]
                             [--symlink]
       (Edita la lista de cambios indicada y añade (add) o quita (rm) los cambios
       que concuerden con la ruta especificada. Use un espacio en blanco para
       separar rutas. Use comillas dobles (" ") para especificar rutas que
       contengan espacios. El estado de las rutas debe ser 'Added' o
       'Checked-out'.)

Opciones:

    clist_name          El nombre de la lista de cambios (changelist). Se puede 
                        usar la ruta a un archivo que contenga el nombre en su
                        lugar. Más información en --namefile.
    clist_desc          La descripción de la lista de cambios. Se puede usar la 
                        ruta a un archivo que contenga el nombre en su lugar. 
                        Más información en --descriptionfile.
    action_name         Seleccione 'rename' o 'description' para modificar un
                        changelist.
    action_value        Aplica el nuevo cambio o la nueva descripción al editar
                        la lista de cambios.
    --persistent        La lista de cambios permanecerá en el espacio de trabajo
                        aun cuando los cambios en ella se hayan protegido 
                        (checked-in) o deshecho.
    --notpersistent     (Por defecto.) La lista de cambios no permanecerá en el
                        espacio de trabajo cuando los cambios en ella se protejan
                        (checked-in) o se deshagan.
    --symlink           Aplica la operación al symlink pero no al destino
                        (target).
    --namefile          Una ruta válida al archivo que contiene el nombre de la 
                        lista de cambios. El archivo debe existir en esa ruta, debe 
                        contener algo de texto y no puede contener saltos de línea.
    --newnamefile       Una ruta válida al archivo que contiene el nuevo nombre de 
                        la lista de cambios cuando se edita para renombrar. El 
                        archivo debe existir en esa ruta, debe de contener algo de 
                        texto y no puede contener saltos de línea.
    --descriptionfile   Una ruta válida al archivo que contiene la descripción deseada 
                        para la lista de cambios. El archivo debe existir en esa ruta.

== CMD_HELP_CHANGELIST ==
Notas:

    El comando 'changelist' gestiona tanto las listas de cambios pendientes del
    espacio de trabajo como los cambios dentro de la lista.

Ejemplos:

    cm changelist
    (Muestra las listas de cambios del espacio de trabajo actual.)

    cm changelist create config_changes "dotConf files" --persistent
    (Crea una nueva lista de cambios llamada 'config_changes' con descripción
    'dotConf files' que permanecerá en el espacio de trabajo después de que sus
    cambios se hayan protegido (checked-in) o deshecho.)

    cm changelist create --namefile="name.txt" --descriptionfile="desc.txt"
    (Crea una nueva lista de cambios cuyo nombre y descripción son tomados de 
    archivos.)

    cm changelist edit config_changes rename config_files --notpersistent
    (Edita la lista de cambios 'config_changes' renombrándola a 'config_files'
    y haciéndola no persistente.)
        
    cm changelist edit config_changes --notpersistent
    (Edita la lista de cambios 'config_changes' haciéndola no persistente.)

    cm changelist delete config_files
    (Elimina la lista de cambios 'config_files' del espacio de trabajo actual.)

    cm changelist delete --namefile="name.txt"
    (Elimina la lista de cambios identificada por el nombre contenido en 'name.txt' 
    del espacio de trabajo actual.)

    cm changelist config_files add foo.conf
    (Añade el fichero 'foo.conf' a la lista de cambios 'config_files'.)

    cm changelist config_files rm foo.conf readme.txt
    (Elimina los ficheros 'foo.conf' y 'readme.txt' de la lista de cambios
    'config_files' y los mueve la lista de cambios por defecto del sistema.)

    cm changelist edit --namefile="name.txt" description --descriptionfile="desc.txt"
    (Edita la lista de cambios identificada por el nombre contenido en 'name.txt', cambiando 
    su descripción por el contenido del archivo 'desc.txt'.)

    cm changelist edit --namefile="name.txt" rename --newnamefile="newname.txt"
    (Edita la lista de cambios identificada por el nombre contenido en 'name.txt' file, 
    renombrándola por el contenido del archivo 'newname.txt'.)

== CMD_DESCRIPTION_CHANGESET ==
Ejecuta operaciones avanzadas sobre changesets.

== CMD_USAGE_CHANGESET ==
Sintaxis:

    cm changeset <command> [options]

Comandos:

    move        | mv
    delete      | rm
    editcomment | edit

    Para obtener más información de cada uno de los comandos use lo siguiente:
    cm changeset <command> --usage
    cm changeset <command> --help

== CMD_HELP_CHANGESET ==
Ejemplos:

    cm changeset move cs:15@myrepo br:/main/scm005@myrepo
    cm changeset delete cs:2b55f8aa-0b29-410f-b99c-60e573a309ca@devData

== CMD_DESCRIPTION_CHANGESET_EDITCOMMENT ==
Modifica el comentario de un changeset.

== CMD_USAGE_CHANGESET_EDITCOMMENT ==
Sintaxis:

    cm changeset editcomment | edit <csetspec> <new_comment>

Opciones:

    csetspec            El changeset cuyo comentario se va a editar. (Use
                        'cm help objectspec' para más información sobre
                        especificaciones de changesets.)
    new_comment         El nuevo comentario que se aplicará al changeset indicado.

== CMD_HELP_CHANGESET_EDITCOMMENT ==
Notas:

    - La especificación del changeset debe ser válida.

Ejemplos:

    cm changeset editcomment cs:15@myrepo "I forgot to add the checkin details"
    cm changeset edit cs:cb11ecdb-1aa9-4f11-8698-dcab14e5885a \
         "This comment text will replace the previous one."
    cm changeset edit "89095131-895d-4173-9440-ff9ef9b2538d@project@cloud" \
         "Changing my comment"

== CMD_DESCRIPTION_CHANGESET_MOVE ==
Mueve un changeset y todos sus descendientes a otra rama.

== CMD_USAGE_CHANGESET_MOVE ==
Sintaxis:

    cm changeset move | mv <csetspec> <branchspec>

Opciones:

    csetspec            El primer changeset que debe moverse a una rama
                        diferente. Todos los changeset descendientes en la
                        misma rama se moverán también.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de changesets.)
    branchspec          La rama destino a la que mover los changesets
                        seleccionados. Debe estar vacía o no existir. En caso
                        de no existir, el comando la creará.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de ramas.)

== CMD_HELP_CHANGESET_MOVE ==
Notas:

    - La especificación del changeset debe ser válida.
    - La rama de destino debe estar vacía o no existir.
    - Si la rama de destino no existe, el comando la creará.
    - Los enlaces de merge no se verán afectados dado que las ramas no están
      involucradas en ellos.

Ejemplos:

    cm changeset move cs:15@myrepo br:/main/scm005@myrepo
    cm changeset move cs:cb11ecdb-1aa9-4f11-8698-dcab14e5885a br:/hotfix/TL-352

== CMD_DESCRIPTION_CHANGESET_DELETE ==
Elimina un changeset del repositorio.

== CMD_USAGE_CHANGESET_DELETE ==
Sintaxis:

    cm changeset delete | rm <csetspec>

Opciones:

    csetspec            El changeset a eliminar. Debe cumplir con una serie de
                        condiciones. Consulte las Notas para más información.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de changesets.)

== CMD_HELP_CHANGESET_DELETE ==
Notas:

    - El changeset debe ser el último en su rama.
    - El changeset no puede ser el padre de otro changeset.
    - El changeset no debe ser origen de un merge ni formar parte de un intervalo
      de merge.
    - El changeset no tiene que tener ninguna etiqueta.
    - El changeset no puede ser el changeset raíz (cs:0).

Ejemplos:

    cm changeset rm cs:4525@myrepo@myserver
    cm changeset delete cs:cb11ecdb-1aa9-4f11-8698-dcab14e5885a

== CMD_DESCRIPTION_CHANGEUSERPASSWORD ==
Cambia el password del usuario (UP).

== CMD_USAGE_CHANGEUSERPASSWORD ==
Sintaxis:

    cm changepassword | passwd

== CMD_HELP_CHANGEUSERPASSWORD ==
Notas:

    Este comando está disponible solo si la configuración de seguridad es UP
    (user/password).
    Lea la Guía del Administrador para más información:
    https://www.plasticscm.com/download/help/adminguide

    Las contraseñas antigua y nueva son obligatorias.

Ejemplo:

    cm passwd

== CMD_DESCRIPTION_CHECKCONNECTION ==
Comprueba la conexión con el servidor.

== CMD_USAGE_CHECKCONNECTION ==
Sintaxis:

    cm checkconnection | cc [<repserverspec>]

Options:

    repserverspec   Repositories server.
                    (Use 'cm ^help ^objectspec' to learn more about repserver
                    specs.)

Examples:

    cm ^checkconnection myorg@cloud

== CMD_HELP_CHECKCONNECTION ==
Notas:

    - Este comando devuelve un mensaje indicando si existe una conexión válida
      con el servidor especificado. Si no se indica un especificación de
      servidor de repositorios, la comprobación se hará sobre el servidor
      indicado en el fichero client.conf.
    - El comando comprueba la compatibilidad de versiones con el servidor.
    - El comando también comprueba si el usuario configurado es válido o no.

== CMD_DESCRIPTION_CHECKDB ==
Comprueba la integridad de los repositorios.

== CMD_USAGE_CHECKDB ==
Sintaxis:

    cm checkdatabase | chkdb [<repserverspec> | <repspec>]

Options:

    repserverspec       Servidor de repositorios.
    repspec             Repositorio.

    (Use 'cm help objectspec' para más información sobre especificaciones de
    servidor de repositorios y repositorio..)

== CMD_HELP_CHECKDB ==
Notas:

    - Si no se indica ninguna especificación (de servidor de repositorios o de
      repositorio), la comprobación se hará sobre el servidor indicado en el
      fichero client.conf.

Ejemplos:

      cm checkdatabase repserver:localhost:8084
      cm chkdb rep:default@localhost:8084

== CMD_DESCRIPTION_CHECKIN ==
Guarda (protege) los cambios en el repositorio.

== CMD_USAGE_CHECKIN ==
Sintaxis:

    cm checkin | ci [<item_path>[ ...]]
        [-c=<comentario> | -commentsfile=<fichero_comentario>]
        [--all|-a] [--applychanged] [--private] [--update] [--symlink]
        [--noshowchangeset]
        [--machinereadable [--startlineseparator=<sep>]
          [--endlineseparator=<sep>] [--fieldseparator=<sep>]]

Opciones:

    item_path             Ítems a guardar (checkin). Use comillas dobles (" ")
                          para especificar rutas que contengan espacios. Use un
                          espacio en blanco para separar rutas de ítems.
                          Use . para hacer checkin al directorio actual.
    -c                    Asigna el comentario indicado al changeset que se crea
                          en el checkin.
    -commentsfile         Asigna el comentario especificado en el fichero al
                          changeset que se crea en el checkin.
    --all | -a            Incluye en el checkin los ítems de las rutas indicadas
                          que han sido cambiados, movidos y borrados localmente.
    --applychanged        Incluye en el checkin los ítems cambiados y los
                          desprotegidos (checked-out).
    --private             Incluye en el checkin los ítems privados.
    --update              Procesa automáticamente un update-merge si fuera
                          necesario.
    --symlink             Incluye en el checkin al symlink pero no al destino
                          (target).
    --noshowchangeset     No muestra el changeset resultante.
    --machinereadable     Muestra el resultado en un formato fácil de parsear.
    --startlineseparator  Usado con '--machinereadable', indica cómo deben
                          empezar las líneas del resultado.
    --endlineseparator    Usado con '--machinereadable', indica cómo deben
                          terminar las líneas del resultado.
    --fieldseparator      Usado con '--machinereadable', indica cómo deben
                          separarse los campos de cada línea resultante.

== CMD_HELP_CHECKIN ==
Notas:

    - Si no se especifica ningún <item_path>, el checkin afecta a todos los
      cambios pendientes en el workspace.
    - El checkin siempre se aplica recursivamente desde la ruta especificada.
    - Para proteger (checkin) un ítem:
      - El ítem tiene que estar controlado (checked-in).
      - Si el ítem es privado (no está controlado), es necesario indicar el
        argumento --private para poder protegerlo (checkin).
      - El ítem tiene que estar desprotegido (checked-out).
      - Si el ítem ha cambiado pero no está desprotegido (no checked-out), no es
        necesario indicar el argumento --applychanged a no ser que <item_path>
        sea un directorio o contenga wildcards ('*').

    El contenido de la revisión a proteger (checkin) debería ser diferente de la
    revisión anterior.

    Configure la variable de entorno PLASTICEDITOR para especificar un editor
    para escribir el comentario. Si la variable de entorno PLASTICEDITOR está
    configurada pero no se indica ningún comentario, el editor se abrirá para
    que pueda escribir el comentario.

Lectura desde stdin:

    El comando 'checkin' puede leer rutas desde stdin. Para ello, pase como
    argumento un guión "-".
    Por ejemplo: cm checkin -

    Las rutas se leerán hasta que se introduzca una línea en blanco.
    Este tipo de lectura permite usar una tubería (pipe) para especificar qué
    ficheros se van a proteger (checkin).
    Por ejemplo:
      dir /S /B *.c | cm checkin --all -
      (En Windows, protege (checkin) todos los ficheros del workspace con
      extensión .c.)

Ejemplos:

    cm checkin file1.txt file2.txt
    (Protege (checkin) los ficheros desprotegidos (checked-out) 'file1.txt' y
    'file2.txt'.)

    cm checkin . -commentsfile=micomentario.txt
    (Protege (checkin) el directorio actual y aplica el comentario incluido en
    el fichero 'mycomment.txt'.)

    cm checkin link --symlink
    (Protege (checkin) el fichero symlink y no el destino (target).)

    cm ci file1.txt -c="my comment"
    (Protege (checkin) el fichero 'file1.txt' y aplica el comentario.)

    cm status --short --compact --changelist=pending_to_review | cm checkin -
    (Obtiene los ítems de la lista de cambios (changelist) 'pending_to_review' y
    las redirecciona como entrada al comando checkin para protegerlos.)

    cm ci . --machinereadable
    (Protege (checkin) el directorio actual y muestra por pantalla el resultado
    en un formato simple y fácil de parsear.)

    cm ci . --machinereadable --startlineseparator=">" --endlineseparator="<" --fieldseparator=","
    (Protege (checkin) el directorio actual y muestra por pantalla el resultado
    en un formato simple y fácil de parsear, comenzando y terminando cada línea
    con los separadores indicados y separando los campos de cada línea con el
    separador también indicado.)

== CMD_DESCRIPTION_CHECKOUT ==
Marca los ficheros para ser modificados (desprotege).

== CMD_USAGE_CHECKOUT ==
Sintaxis:

    cm checkout | co [<item_path>[ ...]] [-R | -r | --recursive]
                     [--format=<str_format>]
                     [--errorformat=<str_format>] [--resultformat=<str_format>]
                     [--silent] [--symlink] [--ignorefailed]
                     [--machinereadable [--startlineseparator=<sep>]
                       [--endlineseparator=<sep>] [--fieldseparator=<sep>]]

Opciones:

    item_path             Ítems a desproteger (checkout). Use comillas dobles (" ")
                          para especificar rutas que contengan espacios. Use un
                          espacio en blanco para separar rutas de ítems.
                          Use . para hacer checkout al directorio actual.
    -R                    Desprotege (checkout) los ficheros recursivamente.
    --format              Muestra el progreso en la salida con el formato
                          indicado. Consulte los ejemplos para más información.
    --resultformat        Muestra el resultado en la salida con el formato
                          indicado. Consulte los ejemplos para más información.
    --silent              No muestra nada en la salida.
    --symlink             Incluye en el checkout al symlink pero no al destino
                          (target).
    --ignorefailed        Si no se puede hacer un checkout exclusivo (bloqueo)
                          de un ítem, el checkout continuará sin él.
    --machinereadable     Muestra el resultado en un formato fácil de parsear.
    --startlineseparator  Usado con '--machinereadable', indica cómo deben
                          empezar las líneas del resultado.
    --endlineseparator    Usado con '--machinereadable', indica cómo deben
                          terminar las líneas del resultado.
    --fieldseparator      Usado con '--machinereadable', indica cómo deben
                          separarse los campos de cada línea resultante.

== CMD_HELP_CHECKOUT ==
Notas:

    Para desproteger (checkout) un ítem:
    - El ítem tiene que estar controlado (bajo control de versiones)
    - El ítem debe estar protegido (checked-in).

    Si los bloqueos están configurados en el servidor (existe el fichero
    lock.conf), cada vez que se produza un checkout en un ítem, Unity VCS
    comprueba si se cumple alguna de las reglas de bloqueo. Si se cumple, el
    ítem se bloqueará (checkout exclusivo) y nadie podrá hacer otro checkout.
    Para conocer los bloqueos que existen en cualquier momento en el servidor
    usa el comando 'cm lock list'.
    Lea la Guía del Administrador para más información:
    https://www.plasticscm.com/download/help/adminguide

    El texto en el argumento --format reemplaza a la variable '{0}' del ítem que
    se va a desproteger (checkout). Consulte los ejemplos para más información.

Lectura desde stdin:

    El comando 'checkout' puede leer rutas desde stdin. Para ello, pase como
    argumento un guión "-".
    Por ejemplo: cm checkout -

    Las rutas se leerán hasta que se introduzca una línea en blanco.
    Este tipo de lectura permite usar una tubería (pipe) para especificar qué
    ficheros se van a desproteger (checkout).
    Por ejemplo:
      dir /S /B *.c | cm checkout --all -
      (En Windows, desprotege (checkout) todos los ficheros del workspace con
      extensión .c.)

Ejemplos:

    cm checkout file1.txt file2.txt
    (Desprotege (checkout) los ficheros 'file1.txt' y 'file2.txt'.)

    cm co *.txt
    (Desprotege (checkout) todos los ficheros con extensión .txt.)

    cm checkout .
    (Desprotege (checkout) el directorio actual.)

    cm checkout -R c:\workspace\src
    (Desprotege (checkout) recursivamente el contenido de la carpte 'src'.)

    cm co file.txt --format="Checking out item {0}" 
        --resultformat="Item {0} checked out"
    (Desprotege (checkout) el fichero 'file.txt' y usa un formato determinado
    para mostrar el progreso de la operación y el resultado.)

    cm checkout link --symlink
    (Desprotege (checkout) el fichero symlink y no el destino (target).)

    cm checkout . -R --ignorefailed
    (Desprotege (checkou) recursivamente el directorio actual ignorando aquellos
    ficheros que no se pueden desproteger.)

    cm co . --machinereadable --startlineseparator=">"
    (Desprotege (checkout) el directorio actual y muestra por pantalla el
    resultado en un formato simple y fácil de parsear, comenzando cada línea con
    el separador indicado.)

== CMD_DESCRIPTION_CHECKSELECTORSYNTAX ==
Comprueba la sintaxis de un selector.

== CMD_USAGE_CHECKSELECTORSYNTAX ==
Sintaxis:

    cm checkselectorsyntax | css --file=<selector_file>
    (Comprueba la sintaxis del fichero del selector.)

    cat <selector_file> | cm checkselectorsyntax | css -
    (Unix. Comprueba el fichero del selector desde la entrada estándar.)

    type <selector_file> | cm checkselectorsyntax | css -
    (Windows. Comprueba el fichero del selector desde la entrada estándar.)


    --file     Fichero desde el que leer el selector.

== CMD_HELP_CHECKSELECTORSYNTAX ==
Notas:

    Este comando lee el selector tanto de un fichero o de la entrada estándar, y
    comprueba su sintaxis. Si esta comprobación falla, los motivos de este fallo
    se mostrarán por la salida estándar.

Ejemplos:

    cm checkselectorsyntax --file=myselector.txt
    (Comprueba la sintaxis del fichero 'myselector.txt'.)

    cat myselector.txt | cm checkselectorsyntax
    (Comprueba la sintaxis del fichero 'myselector.txt' desde la entrada estándar.)

== CMD_DESCRIPTION_CHANGEREVISIONTYPE ==
Cambia el tipo de revisión (binario o texto) de un ítem.

== CMD_USAGE_CHANGEREVISIONTYPE ==
Sintaxis:

    cm changerevisiontype | chgrevtype | crt <item_path>[ ...] --type=(bin | txt)

    item_path           Ítems a los que cambiar el tipo de revisión. Use comillas
                        dobles (" ") para especificar rutas que contengan
                        espacios. Use un espacio en blanco para separar rutas de
                        ítems.
    --type              Tipo de revisión: 'bin' (binario) o 'txt' (texto).

== CMD_HELP_CHANGEREVISIONTYPE ==
Notas:

    Este comando solo se puede aplicar a ficheros y no a directorios.
    El tipo especificado debe ser uno de los soportados por el sistema: 'bin'
    (binario) o 'txt' (texto).

Ejemplos:

    cm changerevisiontype c:\workspace\file.txt --type=txt
    (Cambia el tipo de revisión del fichero 'file.txt' a texto.)

    cm chgrevtype comp.zip "image file.jpg" --type=bin
    (Cambia el tipo de revisión de los ficheros 'comp.zip' e 'image file.jpg' a
    binario.)

    cm crt *.* --type=txt
    (Cambia el tipo de revisión de todos los ficheros a texto.)

== CMD_DESCRIPTION_TRIGGER_EDIT ==
Edita un trigger.

== CMD_USAGE_TRIGGER_EDIT ==
Sintaxis:

    cm trigger | tr edit <subtype_type> <position_number>
                         [--position=<new_position>]
                         [--name=<new_name>] [--script=<script_path>]
                         [--filter=<str_filter>] [--server=<repserverspec>]

    subtype_type        Ejecución y operación del trigger.
                        (Use 'cm showtriggertypes' para ver la lista de tipos de
                        triggers.)
    position_number     Posición que ocupa el trigger que se va a modificar.

Opciones:

    --position          Nueva posición del trigger a modificar.
                        Esta posición no debe estar en uso por otro trigger del
                        mismo tipo.
    --name              Nuevo nombre del trigger a modificar.
    --script            Nueva ruta de ejecución del script del trigger a modificar.
                        Si el script comienza con "webtrigger ", el trigger se
                        considerará como trigger de tipo web. Consulte las Notas
                        para más información.
    --filter            Comprueba solo los ítems que cumplen con el filtro
                        indicado.
    --server            Modifica el trigger del servidor indicado.
                        Si no se especifica ningún trigger, entonces el comando
                        se ejecuta en el servidor configurado en el cliente.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de servidor.)

== CMD_HELP_TRIGGER_EDIT ==
Notas:

    Web triggers: Un trigger web se crea escribiendo "webtrigger <target-uri>"
    como comando del trigger. En este caso, el trigger ejecutará una consulta
    POST contra el URI especificado en la que el cuerpo de la petición
    contiene un diccionario JSON con las variables de entorno del trigger y una
    clave INPUT apuntando a un vector de cadenas de texto .

Ejemplos:
    cm trigger edit after-setselector 6 --name="Backup2 manager" --script="/new/path/al/script"
    cm tr edit before-mklabel 7 --position=4 --server=myserver:8084
    cm trigger edit after-add 2 --script="webtrigger http://myserver.org/api"

== CMD_DESCRIPTION_CODEREVIEW ==
Crea, edita o elimina revisiones de código.

== CMD_USAGE_CODEREVIEW ==
Sintaxis:

    cm codereview <spec> <title> [--status=<status_name>]
                [--assignee=<user_name>] [--format=<str_format>]
                [--repository=<rep_spec>]
    (Crea una revisión de código.)

    cm codereview -e <id> [--status=<status_name>] [--assignee=<user_name>]
                [--repository=<rep_spec>]
    (Edita una revisión de código.)

    cm codereview -d <id> [ ...] [--repository=<rep_spec>]
    (Borra una o más revisiones de código.)


    spec        Puede ser una especificación de changeset, una especificación de 
                shelve o una especificación de rama. Creará la nueva revisión de 
                código de dicha especificación. (Use 'cm help objectspec' para más 
                información sobre especificaciones de changeset o rama.)
    title       Cadena de texto que se usará como título de la revisión de
                código.
    id          Número de identificación de la revisión de código. También se
                puede usar un GUID.

Opciones:

    -e              Edita los parámetros de una revisión de código.
    -d              Elimina una o más revisiones de código. Use un espacio en
                    blanco para separar los identificadores de las revisiones de
                    código.
    --status        Aplica el nuevo estado a la revisión de código. Consulte las
                    Notas para más información.
    --assignee      Aplica el nuevo usuario asignado a la revisión de código.
    --format        Muestra el mensaje de salida con el formato indicado.
                    Consulte las Notas para más información.
    --repository    Indica el repositorio que se utilizará por defecto. (Use
                    'cm help objectspec' para más información sobre
                    especificaciones de repositorio.)

== CMD_HELP_CODEREVIEW ==
Notas:

    Este comando permite a los usuarios gestionar revisiones de código: crear,
    editar y eliminar revisiones de código de changesets o ramas.

    Para crear una nueva revisión de código se requiere una especificación de
    changeset o rama además de un título. También se puede indicar el estado
    inicial y el usuario asignado como revisor de los cambios.
    Como resultado se devolverá el identificador ID (o GUID).

    Para editar o eliminar una revisión de código existente debe indicar el ID
    (o GUID) de la misma. No se mostrarán mensajes si no ha habido errores.

    El parámetro 'status' debe tener uno de los siguientes valores:
    'Under review' (valor por defecto), 'Reviewed' o 'Rework required'.

    El parámetro 'repository' sirve para especificar el repositorio por defecto.
    Esto es útil cuando el usuario quiere gestionar revisiones de código de un
    servidor diferente del asociado al espacio de trabajo actual o cuando no
    existe un espacio de trabajo.

    Parámetros de formato de salida (opción --format):
        Este comando acepta una cadena de formato para mostrar la salida.
        Los parámetros de salida de este comando son los siguientes:
        {0}             id
        {1}             guid

    Tenga en cuenta que el parámetro --format solo tiene efecto al crear una
    nueva revisión de código.

Ejemplos:

    cm codereview cs:1856@myrepo@myserver:8084 "My code review" --assignee=dummy
    cm codereview br:/main/task001@myrepo@myserver:8084 "My code review" \
    --status="Rework required" --assignee=newbie --format="{id} -> {guid}"

    cm codereview 1367 -e --assignee=new_assignee
    cm codereview -e 27658884-5dcc-49b7-b0ef-a5760ae740a3 --status=Reviewed

    cm codereview -d 1367 --repository=myremoterepo@myremoteserver:18084
    cm codereview 27658884-5dcc-49b7-b0ef-a5760ae740a3 -d

== CMD_DESCRIPTION_CRYPT ==
Encripta una contraseña.

== CMD_USAGE_CRYPT ==
Sintaxis:

    cm crypt <mypassword>

    mypassword      Contraseña que se va a encriptar.

== CMD_HELP_CRYPT ==
Notas:

    Este comando encripta la contraseña que se pasa como argumento.
    Está diseñado para encriptar las contraseñas de los ficheros de 
    configuración y aumentar así su seguridad.

Ejemplos:

    cm crypt dbconfpassword -> ENCRYPTED: encrypteddbconfpassword
    (Encripta la contraseña en el fichero de configuración de la base de datos:
    'db.conf'.)

== CMD_DESCRIPTION_DEACTIVATEUSER ==
Desactiva un usuario con licencia.

== CMD_USAGE_DEACTIVATEUSER ==
Sintaxis:

    cm deactivateuser | du <usr_name>[ ...] [--server=<name:port>]
                           [--nosolveuser]

    usr_name            Nombre del usuario a desactivar. Use un espacio en
                        blanco para separar nombres de usuario.
                        Si SID, entonces debe incluir el argumento '--nosolveuser'.

Options:

    --server            Desactiva el usuario en el servidor indicado. Si no se
                        indica ningún servidor, el comando se ejecuta en el
                        servidor configurado en el cliente.
    --nosolveuser       Si se indica esta opción, el comando no comprobará si el
                        nombre de usuario existe en el sistema de autentificación.
                        El nombre de usuario debe ser un usuario SID.

== CMD_HELP_DEACTIVATEUSER ==
Notas:

    Este comando desactiva a un usuario. De este modo no podrá usar Unity VCS.

    (Use 'cm activateuser' para más información sobre cómo activar usuarios de
    Unity VCS.)

    Este comando comprueba si el usuario existe en el sistema de autentificación
    (por ejemplo, ActiveDirectory, LDAP, User/Password...).
    Para forzar la desactivación de un usuario que ya no existe en el sistema de
    autentificación, use la opción '--nosolveuser' option.

Ejemplos:

    cm deactivateuser john
    cm du peter "mary collins"
    cm deactivateuser john --server=myserver:8084
    cm deactivateuser S-1-5-21-3631250224-3045023395-1892523819-1107 --nosolveuser

== CMD_DESCRIPTION_DIFF ==
Muestra las diferencias entre ficheros, changesets o ramas.

== CMD_USAGE_DIFF ==
Sintaxis:

    cm diff <csetspec> | <lbspec> | <shspec> [<csetspec> | <lbspec> | <shspec>]
            [<path>]
            [--added] [--changed] [--moved] [--deleted]
            [--repositorypaths] [--download=<download_path>]
            [--encoding=<name>] 
            [--comparisonmethod=(ignoreeol | ignorewhitespaces | 
                ignoreeolandwhitespaces | recognizeall)] 
            [--clean]
            [--integration]
            [--format=<str_format>] [--dateformat=<str_format>]

        Muestra las diferencias entre un changeset o un shelveset 'origen' y un
        changeset o un shelveset 'destino'. Los changesets se pueden especificar
        usando una especificación de changeset o de etiqueta.
        Si se indican dos especificaciones, la primera será el 'origen' del diff
        y la segunda será el 'destino'.
        Si se indica solo una especificación, el 'origen' será el changeset
        padre del 'destino' indicado.
        Si se especifica una ruta, la ventana de Diff se abrirá para mostrar las
        diferencias entre las dos revisiones del fichero indicado.

    cm diff <revspec1> <revspec2>

        Muestra las diferencias entre un par de revisiones. Las diferencias se
        muestran en la ventana de Diff. La primera revisión indicada aparecerá
        a la izquierda.

    cm diff <brspec> [--added] [--changed] [--moved] [--deleted]
            [--repositorypaths] [--download=<download_path>]
            [--encoding=<name>] 
            [--comparisonmethod=(ignoreeol | ignorewhitespaces | 
                ignoreeolandwhitespaces | recognizeall)] 
            [--clean]
            [--integration]
            [--format=<str_format>] [--dateformat=<str_format>]
            [--fullpaths | --fp]

        Muestra las diferencias de rama.

    (Use 'cm help objectspec' para más información sobre especificaciones.)

Opciones:

    --added             Imprime las diferencias de ítems añadidos al repositorio.
    --changed           Imprime las diferencias de ítems que han cambiado.
    --moved             Imprime las diferencias de ítems movidos o renombrados.
    --deleted           Imprime las diferencias de ítems borrados.

                        Si no se indica ni '--added' ni '--changed' ni '--moved'
                        ni '--deleted', se imprimen todas las diferencias.
                            'A' significa ítems añadidos ('added'.)'
                            'C' significa ítems cambiados ('changed'.)
                            'D' significa ítems borrados ('deleted'.)
                            'M' significa ítems movidos ('moved'). El ítem a la
                              izquierda es el original, y el de la derecha es el
                              destino.

    --repositorypaths   Imprime rutas de repositorio en lugar de rutas de
                        workspace. (Esta opción anula la opción '--fullpaths'.)
    --download          Guarda el contenido de las diferencias en la ruta de
                        salida indicada.
    --encoding          Especifica el encoding que se usará en la salida, por
                        ejemplo, utf-8. Consulte la documentación de MSDN en
                        http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                        para obtener la tabla de codificaciones soportadas y
                        su formato (al final de la página en la columna "Name").
    --comparisonmethod  Configura el método de comparación indicado.
                        Consulte las Notas para más información.
    --clean             Omite las diferencias que provienen de un merge mostrando
                        solo las diferencias creadas en los checkins.
    --integration       Muestra los cambios de la rama que están pendientes de 
                        integrar en su rama padre. Tiene en cuenta los rebases 
                        y los merges a la rama padre ya realizados.
    --format            Muestra el mensaje de salida en el formato indicado.
                        Consulte las Notas para más información.
    --dateformat        Configura el formato de salida para la impresión de fechas.
    --fullpaths, --fp   Fuerza la impresión de rutas de workspaces absolutas para
                        ficheros y directorios siempre que sea posible.

== CMD_HELP_DIFF ==
Notas:

    Métodos de comparación (opción --comparisonmethod):
        ignoreeol                 Ignora diferencias en final de línea.
        ignorewhitespaces         Ignora diferencias en espacios.
        ignoreeolandwhitespaces   Ignora diferencias en final de línea y espacios.
        recognizeall              Detecta diferencias en final de línea y espacios.

    Parámetros de formato de salida (opción --format):
        Este comando acepta una cadena de formato para mostrar la salida.
        Los parámetros de salida de este comando son los siguientes:
        {path}              Ruta del ítem.
        {date}              Fecha/hora del cambio.
        {owner}             Autor del cambio.
        {revid}             Identificador de la revisión considerada como
                            destino en las diferencias.
        {parentrevid}       Identificador del padre de la revisión considerada
                            como destino en las diferencias.
        {baserevid}         Identificador de la revisión considerada como origen
                            en las diferencias.
        {srccmpath}         Ruta de servidor antes de mover un ítem (para la
                            operación de movido).
        {dstcmpath}         Ruta de servidor después de mover un ítem (para la
                            operación de movido).
        {type}              Tipo de ítem:
            D   directorio,
            B   fichero binario,
            F   fichero de texto,
            S   symlink,
            X   Xlink.
        {repository}        Repositorio del ítem.
        {status}            Estado del ítem:
            A   añadido,
            D   borrado,
            M   movido,
            C   cambiado.
        {fsprotection}      Muestra los permisos del ítem (Linux/Mac chmod).
        {srcfsprotection}   Muestra los permisos de la revisión padre del ítem.
        {newline}           Inserta una nueva línea.

Notas sobre 'revid':
    Para ítems añadidos, 'baserevid' y 'parentrevid' es -1 puesto que no existe
    revisión previa en estos casos.
    Para ítems borrados, 'revid' es el identificador de la revisión origen, y
    'baserevid' es -1 puesto que no existe revisión destino.
    Para Xlinks, 'baserevid' y 'parentrevid' son siempre -1.

Ejemplos:

  Comparar ramas:

    cm diff br:/main/task001
    cm diff br:/main/task001 \doc\readme.txt
    cm diff br:/main/task001 --integration

  Comparar changesets:

    cm diff 19
    cm diff 19 25
    cm diff cs:19 cs:25 --format="{path} {parentrevid}"
    cm diff cs:19 cs:23 --format="{date} {path}" --dateformat="yy/dd/MM HH:mm:ss"
    cm diff cs:19 cs:23 --changed
    cm diff cs:19 cs:23 --repositorypaths
    cm diff cs:19 cs:23 --download="D:\temp"
    cm diff cs:19 cs:23 --clean
    cm diff cs:19 cs:23 \doc\readme.txt

  Comparar etiquetas:

    cm diff lb:EtiquetaPrimeraRelease lb:EtiquetaSegundaRelease
    cm diff lb:tag_193.2 cs:34214
    cm diff cs:31492 lb:tag_193.2

  Comparar shelves:

    cm diff sh:2
    cm diff sh:2 sh:4

  Comparar revisiones:

    cm diff rev:readme.txt#cs:19 rev:readme.txt#cs:20
    cm diff serverpath:/doc/readme.txt#cs:19@myrepo \
        serverpath:/doc/readme.txt#br:/main@myrepo@localhost:8084
    cm diff rev:foo.c#cs:1 rev:foo.c#cs:2 --comparisonmethod=ignoreeol

== CMD_DESCRIPTION_DIFFMETRICS ==
Muestra métricas de diferencias entre dos revisiones.

== CMD_USAGE_DIFFMETRICS ==
Sintaxis:

    cm diffmetrics | dm <revspec1> <revspec2> [--format=<str_format>]
                        [--encoding=<name>]
                        [--comparisonmethod=(ignoreeol | ignorewhitespaces |
                            ignoreeolandwhitespaces | recognizeall)]

    revspec           Revisiones a comparar.
                      (Use 'cm help objectspec' para más información sobre
                      especificaciones de revisiones.)

Options:

    --format            Muestra el mensaje de salida en el formato indicado.
                        Consulte las Notas para más información.
    --encoding          Especifica el encoding que se usará en la salida, por
                        ejemplo, utf-8. Consulte la documentación de MSDN en
                        http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                        para obtener la tabla de codificaciones soportadas y
                        su formato (al final de la página en la columna "Name").
    --comparisonmethod  Configura el método de comparación indicado.
                        Consulte las Notas para más información.

== CMD_HELP_DIFFMETRICS ==
Notas:

    Las métricas que se obtienen son: número de líneas cambiadas, añadidas y
    borradas.

    Parámetros de formato de salida (opción --format):
        Este comando acepta una cadena de formato para mostrar la salida.
        Los parámetros de salida de este comando son los siguientes:
        {0}             Número de líneas cambiadas.
        {1}             Número de líneas añadidas.
        {2}             Número de líneas borradas.

    Métodos de comparación (opción --comparisonmethod):
        ignoreeol                 Ignora diferencias en final de línea.
        ignorewhitespaces         Ignora diferencias en espacios.
        ignoreeolandwhitespaces   Ignora diferencias en final de línea y espacios.
        recognizeall              Detecta diferencias en final de línea y espacios.

Ejemplos:

    cm diffmetrics file.txt#cs:2 file.txt#br:/main/scm0211 \
    --format="There are {0} changed, {1} added and {2} deleted lines."
    (Muestra las métricas de diferencias en el formato indicado.)

    cm dm file.txt#cs:2 file.txt#cs:3 --encoding=utf-8 --comparisonmethod=ignorewhitespaces

== CMD_DESCRIPTION_FASTEXPORT ==
Exporta los datos de un repositorio de Unity VCS a un formato fast-export.

== CMD_USAGE_FASTEXPORT ==
Sintaxis:

    cm fast-export | fe <repspec> <fast-export-file>
                        [--import-marks=<marks_file>]
                        [--export-marks=<marks_file>]
                        [--branchseparator=<chr_separator>]
                        [--nodata] [--from=<changesetid>] [--to=<changesetid>]
Opciones:

    repspec             Repositorio cuyos datos serán exportados.
                        (Use 'cm help objectspec' para más información sobre 
                        especificaciones de repositorio.)
    fast-export-file    Fichero con los datos del repositorio en formato
                        fast-export de Git.
    --import-marks      Fichero de marcas usado para la importación incremental.
                        Este fichero ha sido previamente exportado con el
                        comando --export-marks. Los changesets descritos en este
                        fichero no se importarán porque ya lo fueron en una
                        importación previa.
    --export-marks      Fichero donde se guardan los changesets importados.
                        Este fichero se usará en una operación fast-import para
                        indicar los changesets que han sido importados.
    --branchseparator   Unity VCS usa "/" como separador por defecto en la 
                        jerarquía de ramas. Esta opción permite usar un carácter
                        como separador de jerarquía. Por ejemplo, la rama 
                        main-task-sub será mapeada en Unity VCS como
                        /main/task/sub.
    --nodata            Exporta el repositorio pero sin incluir los datos. Esta
                        opción es útil para comprobar que la exportación se
                        llevará a cabo sin problemas.
    --from              Exporta desde el changeset especificado.
    --to                Exporta hasta el changeset especificado.

== CMD_HELP_FASTEXPORT ==
Notas:

    - Para importar un repositorio de Unity VCS a Git, use un comando como el siguiente:
      cat repo.fe.00 | git fast-import --export-marks=marks.git  --import-marks=marks.git

    - La exportación incremental se realiza usando un fichero de marcas que
      contenga los changesets previamente importados (ficheros --import-marks y
      --export-marks).
      Esto significa que solamente se exportarán los changesets que no fueron
      exportados con un fast-export previamente.

Ejemplos:

    cm fast-export repo@localhost:8087 repo.fe.00 --import-marks=marks.cm --export-marks=marks.cm
    (Exporta el repositorio 'repo' en el servidor local al fichero 'repo.fe.00'
    en formato fast-export de Git y crea el fichero de marcas para poder
    realizar una futura exportación incremental.)

    cm fast-export repo@localhost:8087 repo.fe.00 --from=20
    (Exporta el repositorio 'repo' en el servidor local al fichero 'repo.fe.00'
    en formato Git fast-export desde el changeset '20'.)

== CMD_DESCRIPTION_FASTIMPORT ==
Importa datos de fast-export de Git a un repositorio.

== CMD_USAGE_FASTIMPORT ==
Sintaxis:

    cm fast-import | fi <repspec> <fast-export-file>
                        [--import-marks=<marks_file>]
                        [--export-marks=<marks_file>]
                        [--stats] [--branchseparator=<chr_separator>]
                        [--nodata] [--ignoremissingchangesets] [--mastertomain]

Opciones:

    repspec                     Repositorio en el cual se importarán los datos.
                                Si no existe en el momento de la operación, se
                                crea automáticamente. (Use 'cm help objectspec'
                                para más información sobre especificaciones de
                                repositorio.)
    fast-export-file            Fichero con los datos en formato Git
                                fast-export.
    --import-marks              El fichero de marcas utilizado para
                                importaciones incrementales. Dicho fichero se
                                ha exportado previamente con --export-marks.
                                Los changesets descritos en este fichero no se
                                importarán porque ya existían de una importación
                                previa.
    --export-marks              El fichero en el cual se guardarán los changesets
                                importados. Dicho fichero se utilizará en una
                                operación posterior de fast-import para indicar
                                los changesets que ya han sido importados.
    --stats                     Muestra estadísticas del proceso de importación.
    --branchseparator           Unity VCS usa "/" como separador por defecto
                                en la jerarquía de ramas. Esta opción permite
                                usar un carácter como separador en esa
                                jerarquía. Por ejemplo, la rama main-task-sub
                                será mapeada en Unity VCS como /main/task/sub.
    --nodata                    Importa Git fast-export pero sin incluir datos.
                                Esta opción es útil para comprobar que la
                                importación exportación se llevará a cabo sin
                                problemas.
    --ignoremissingchangesets   Los changesets que no pueden ser importados se
                                descartan y la operación de fast-import continúa
                                sin ellos.
    --mastertomain              Realiza la importación usando "main" en lugar
                                de "master".

== CMD_HELP_FASTIMPORT ==
Notas:

    - Para exportar un repositorio de Git, use un comando como el siguiente:
      git fast-export --all -M --signed-tags=strip --tag-of-filtered-object=drop> ..\git-fast-export.dat
      La opción -M es importante para detectar ítems movidos.

    - El repositorio indicado se crea en caso de que no exista.

    - La importación incremental se realiza usando los ficheros de marcas que
      contengan los changesets previamente importados (ficheros --import-marks y
      --export-marks).
      Esto significa que solamente se importarán los changesets que no fueron
      importados con un fast-import previamente.

Ejemplos:

    cm fast-import mynewrepo@atenea:8084  repo.fast-export
    (Importa el contenido exportado en el fichero 'repo.fast-export' al
    repositorio 'mynewrepo' del servidor 'atenea:8084'.)

    cm fast-import repo@server:8084  repo.fast-export --export-marks=rep.marks
    (Importa el contenido exportado en el fichero 'repo.fast-export' al
    repositorio 'repo' del servidor 'atenea:8084' y crea un archivo de
    marcas para importaciones incrementales posteriores.)

    cm fast-import repo@server:8084  repo.fast-export --importmarks=repo.marks \
    --export-marks=repo.marks
    (Importa el contenido del fichero 'repo.fast-export'. Solamente se importarán
    los changesets que no están en el fichero de marcas. Se utiliza el mismo
    fichero de marcas para guardar la lista de changesets de nuevo para la
    siguiente importación incremental.)

== CMD_DESCRIPTION_FILEINFO ==
Obtiene información detallada de ítems del espacio de trabajo.

== CMD_USAGE_FILEINFO ==
Sintaxis:

    cm fileinfo <item_path>[ ...] [--fields=<field_value>[,...]]
                [[--xml | -x [=<output_file>]] | [--format=<str_format>]]
                [--symlink] [--encoding=<name>]

    item_path       Ítems a mostrar. Use comillas dobles (" ") para especificar
                    rutas que contengan espacios. Use un espacio en blanco para
                    separar rutas de ítems.

Opciones:

    --fields        Cadena de valores separados por coma. Indica qué campos Se
                    mostrarán por cada ítem. Consulte las Notas para más
                    información.
    --xml | -x      Imprime el resultado en formato XML a la salida estándar.
                    También es posible especificar un fichero de salida.
                    Esta opción no puede combinarse con --format.
    --format        Muestra el mensaje de salida en un formato específico.
                    Consulte las Notas para más información. No puede
                    combinarse con --xml.
    --symlink       Aplica la operación al symlink pero no al destino.
    --encoding      Especifica el encoding que se usará en la salida, por
                    ejemplo, utf-8. Consulte la documentación de MSDN en
                    http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                    para obtener la tabla de codificaciones soportadas y
                    su formato (al final de la página en la columna "Name").

== CMD_HELP_FILEINFO ==
Notas:

    Este comando muestra una lista detallada de atributos para cada ítem
    indicado. Cada atributo se muestra en una nueva línea.

    Se puede modificar la lista de atributos para mostrar únicamente los
    atributos que el usuario necesite. Para ello, utilice el argumento
    --fields=<field_list> que acepta una cadena de nombres de atributo
    separados por comas. De este modo, solo se mostrarán aquellos argumentos
    cuyo nombre esté presente en la lista.

    Revision head changeset:

    Esta opción está desactivada por defecto. Tenga en cuenta que la
    recuperación de este atributo es notablemente más lenta que la del resto de
    atributos, por lo que recomendamos agrupar tantos ficheros por ejecución como
    sea posible. Esto mejora los tiempos de ejecución al evitar numerosas
    ejecuciones de 'cm fileinfo' separadas.
    Además, esta característica no se encuentra disponible actualmente para
    directorios controlados.

    A continuación se encuentra la lista completa de nombres de atributo. Los
    nombres marcados con asterisco ('*') no se mostrarán:
        ClientPath              Ruta local del ítem en disco.
        RelativePath            Ruta relativa del ítem en el espacio de trabajo.
        ServerPath              Ruta del repositorio para el ítem.
        Size                    Tamaño del ítem.
        Hash                    Suma 'hash' del ítem.
        Owner                   Nombre del usuario propietario del ítem.
        RevisionHeadChangeset   (*) Changeset de la revisión cargada en el
                                changeset head de la rama.
                                (Ver nota más arriba.)
        RevisionChangeset       Changeset de la revisión cargada en el espacio
                                de trabajo.
        RepSpec                 Especificación del repositorio para el ítem.
                                (Use 'cm help objectspec' para más información
                                sobre especificaciones de repositorio.)
        Status                  Estado del ítem en el espacio de trabajo:
                                añadido, desprotegido (checked-out), eliminado...
        Type                    Tipo de la revisión (texto, binario, directorio,
                                symlink o desconocido).
        Changelist              Lista de cambios (si existe) a la que pertenece
                                el ítem.
        IsLocked                (*) Si el ítem está bloqueado por un checkout
                                exclusivo o no.
        LockedBy                (*) Usuario que bloqueó el ítem mediante
                                checkout exclusivo.
        LockedWhere             (*) Localización del ítem bloqueado mediante
                                checkout exclusivo.
        IsUnderXlink            Si el ítem está localizado en un Xlink o no.
        UnderXlinkTarget        Destino del Xlink (si existe) donde el ítem se
                                encuentra.
        UnderXlinkPath          Ruta de servidor del ítem en el repositorio del
                                Xlink (si existe).
        UnderXlinkWritable      Si el Xlink al que pertenece el ítem es de
                                escritura o no.
        UnderXlinkRelative      Si el Xlink al que pertenece el ítem es
                                relativo o no.
        IsXlink                 Si el ítem es un Xlink o no.
        XlinkTarget             Repositorio destino al que el ítem apunta, si es
                                un Xlink.
        XlinkName               Nombre del ítem Xlink.
        XlinkWritable           Si el ítem Xlink es un Xlink de escritura o no.
        XlinkRelative           Si el ítem Xlink es un Xlink relativo o no.

    Parámetros de formato de salida (opción --format):
        Este comando acepta una cadena de formato para mostrar su salida.
        Los parámetros de salida de este comando son los siguientes:
        {ClientPath}
        {RelativePath}
        {ServerPath}
        {Size}
        {Hash}
        {Owner}
        {RevisionHeadChangeset}
        {RevisionChangeset}
        {Status}
        {Type}
        {Changelist}
        {IsLocked}
        {LockedBy}
        {LockedWhere}
        {IsUnderXlink}
        {UnderXlinkTarget}
        {UnderXlinkPath}
        {UnderXlinkWritable}
        {UnderXlinkRelative}
        {IsXlink}
        {XlinkTarget}
        {XlinkName}
        {XlinkWritable}
        {XlinkRelative}
        {RepSpec}

    Tenga en cuenta que las opciones --format y --xml son mutuamente excluyentes,
    por lo que no se pueden utilizar simultáneamente.

Ejemplos:

    cm fileinfo file1.txt file2.txt dir/
    cm fileinfo "New Project.csproj" --xml
    cm fileinfo assets.art --fields=ServerPath,Size,IsLocked,LockedBy
    cm fileinfo proj_specs.docx --fields=ServerPath,RevisionChangeset --xml
    cm fileinfo samples.ogg --format="{ServerPath}[{Owner}] -> {Size}"

== CMD_DESCRIPTION_FIND ==
Obtiene una serie de objetos en base a unos criterios de búsqueda.

== CMD_USAGE_FIND ==
Sintaxis:

    cm find <object_type> 
            [where <str_conditions>]
            [on repository '<repspec>' | on repositories '<repspec1>','<repspec2>'[,...]]
            [order by <sort_field> ['asc' | 'desc']]
            [[limit <maxresults>] [offset <offset>]]
            [--format=<str_format>] [--dateformat=<date_format>]
            [--nototal] [--file=<dump_file>] [--xml] 
            [--encoding=<name>]

    object_type         Objecto a buscar.
                        Algunos de estos objetos permiten la cláusula 'order by'.
                        Use 'cm help showfindobjects' para más información sobre
                        estos objetos, aquellos que permiten ordenación y por qué campos.
                        Lea la Guía de 'cm find' para más información:
                        https://www.plasticscm.com/download/help/cmfind

Opciones:

    str_conditions      Condiciones de búsqueda sobre los atributos del objeto.
    repspec             Nombre o especificación del repositorio (o repositorios)
                        donde buscar.
                        Para 'on repositories', use una coma para separar las
                        especificaciones de repositorio.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de repositorio.)
    sort_field          Nombre del campo elegido para la ordenación. Tenga en cuenta
                        que esos campos son limitados. Use 'cm help showfindobjects' para
                        consultar qué objetos se permiten ordenar y por qué campos.
    maxresults          Número máximo de resultados devueltos por la búsqueda.
    offset              Número de resultados que se omiten antes de empezar a devolver
                        los resultados de la búsqueda.
    --format            Muestra el mensaje de salida en el formato indicado.
                        Lea la Guía de 'cm find' para ver todos los atributos
                        de objeto que se pueden usar para el formato de salida:
                        https://www.plasticscm.com/download/help/cmfind
    --dateformat        Configura el formato de salida para la impresión de fechas.
    --nototal           No muestra el número total de registros.
    --file              Fichero al que volcar los resultados.
    --xml               Imprime el resultado en formato XML a la salida estándar.
    --encoding          Especifica el encoding que se usará en la salida, por
                        ejemplo, utf-8. Consulte la documentación de MSDN en
                        http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                        para obtener la tabla de codificaciones soportadas y
                        su formato (al final de la página en la columna "Name").

== CMD_HELP_FIND ==
Notas:

    Si no se especifica ningún repositorio, la búsqueda se realiza sobre el
    repositorio configurado en el espacio de trabajo.

    Cuando se ejecutan búsquedas desde la línea de comandos usando operadores de
    comparación (>, <, >=, <=), el intérprete de comandos (o shell) considera a
    estos operadores como redirecciones de E/S. Por lo que es necesario escribir
    la búsqueda entre comillas dobles.

    El comando 'cm find' acepta una cadena de formato para mostrar la salida.
    Cada parámetro de salida se identifica con un nombre, y el usuario se
    puede referir a él escribiéndolo entre llaves '{' y '}'.
    Los parámetros de salida se corresponden normalmente con atributos del objeto.

    Estas son cadenas de formato válidas:
        --format={id}{date}{name}
        --format="{item}#{branch} con fecha {date}"

    Consideraciones sobre XML y encoding:

    Cuando se especifica la opción --xml, se muestra el resultado como texto XML
    en la salida estándar. Por defecto se utiliza la codificación estándar del 
    sistema operativo, por lo que es posible que los caracteres no ANSI no se
    visualicen correctamente en la consola. Pero si se redirige la salida a un
    fichero, el contenido se visualizará correctamente. 
    Cuando se especifican simultáneamente las opciones --xml y --file, se 
    utiliza por defecto la codificación UTF-8.

Ejemplos:

    cm find revision
    cm find revision "where changeset = 23 and owner = 'user'"
    cm find branch "on repository 'rep1'"
    cm find label "on repositories 'rep1', 'rep:default@localhost:8084'"
    cm find branch "where parent = 'br:/main' on repository 'rep1'"
    cm find revision "where item = 'item:.'" --format="{item}#{branch}"
    cm find revision "where item = 'item:.'" --xml --file=c:\queryresults\revs.xml
    cm find label "where owner='me' limit 10 offset 20"
    cm find branches "where owner='me' order by branchname desc limit 10"

== CMD_DESCRIPTION_FINDCHANGED ==
Obtiene una lista de los ficheros cambiados. Este comando está obsoleto y solo
se mantiene por retrocompatibilidad. Use 'cm status'.

== CMD_USAGE_FINDCHANGED ==
Sintaxis:

    cm findchanged | fc [-R | -r | --recursive] [--checkcontent]
                        [--onlychanged] [<path>]

Opciones:

    -R                  Busca recursivamente en los directorios.
    --checkcontent      Compara los ficheros por contenido.
    --onlychanged       Encuentra solo los ficheros cambiados; los ficheros
                        desprotegidos no se devolverán en la búsqueda.
    path                (Por defecto, el directorio actual.)
                        Ruta inicial de búsqueda de los ficheros cambiados.

== CMD_HELP_FINDCHANGED ==
Notas:

    Si no se especifica la opción '--checkcontent', Unity VCS busca cambiados
    basándose en el registro de tiempo (timestamp) de los ficheros.
    Si se especifica la opción '--checkcontent', entonces lo que se compara es
    el contenido de los ficheros o las carpetas y no el timestamp.

    Este comando es útil para detectar ficheros que han cambiado durante una
    desconexión del servidor de Unity VCS. La salida de este comando permite
    usar una tubería (pipe) con el comando 'checkout' y así poder comprobar
    los cambios más tarde (ver ejemplos).

Ejemplos:

    cm findchanged .
    (Busca ficheros cambiados en el directorio actual.)

    cm findchanged -R . | cm checkout -
    (Desprotege los elementos cambiados.)

== CMD_DESCRIPTION_FINDCHECKEDOUT ==
Obtiene una lista de ficheros desprotegidos. Este comando está obsoleto y se
mantiene por retrocompatibilidad. Use 'cm status'.

== CMD_USAGE_FINDCHECKEDOUT ==
Sintaxis:

    cm findcheckouts | fco [--format=<str_format>] [--basepath]

Opciones:

    --format            Muestra el mensaje de salida con el formato indicado.
                        Consulte las Notas para más información.
    --basepath          Ruta por la que comenzar a buscar desprotegidos. Si no
                        se especifica ninguna ruta, se usará la ruta actual.

== CMD_HELP_FINDCHECKEDOUT ==
Notas:

    Este comando es útil para, en un único paso, proteger o deshacer las
    desprotecciones de todos los ficheros desprotegidos, y redirigir la salida
    estándar a otro comando. Vea los ejemplos.

    Parámetros de formato de salida (opción --format):
        Este comando acepta una cadena de formato para mostrar la salida.
        Los parámetros de salida de este comando son los siguientes:
        {0}             Fecha.
        {1}             Propietario.
        {2}             Espacio de trabajo.
        {3}             Nombre de la máquina cliente.
        {4}             Ruta del fichero.
        {5}             Información de rama y repositorio.

Ejemplos:

    cm findcheckouts --format="File {4} changed on branch {5}"
    (Busca los ficheros desprotegidos y formatea la salida con la ruta del
    fichero y la información de la rama y repositorio.)

    cm findcheckouts --format={4} | cm checkin -
    (Protege todos los ficheros desprotegidos.)

    cm findcheckouts --format={4} | cm undocheckout -
    (Deshace la desprotección de todos los ficheros desprotegidos.)

== CMD_DESCRIPTION_FINDPRIVATE ==
Obtiene una lista de ítems privados. Este comando está obsoleto y se mantiene
por retrocompatibilidad. Use 'cm status'.

== CMD_USAGE_FINDPRIVATE ==
Sintaxis:

    cm findprivate | fp [-R | -r | --recursive] [--exclusions] [<path>]

Opciones:

    -R                  Busca recursivamente en los directorios.
    --exclusions        Permite interrumpir la búsqueda dentro de las rutas
                        ignoradas (definidas en el fichero ignore.conf).
    path                (Por defecto, directorio actual.)
                        Ruta por la que comenzar a buscar los ficheros privados.

== CMD_HELP_FINDPRIVATE ==
Notas:

    Si no se especifica ninguna ruta, Unity VCS comenzará a buscar por el
    directorio actual.

    Este comando es útil para añadir los elementos privados de la ruta
    redireccionando la salida estándar al comando 'add'.
    Vea los ejemplos.

Ejemplos:

    cm findprivate .

    cm findprivate -R | cm add -
    (Busca recursivamente items privados y los añade al repositorio.)

== CMD_DESCRIPTION_GETCONFIG ==
Obtiene información de configuración.

== CMD_USAGE_GETCONFIG ==
Sintaxis:

    cm getconfig [setfileasreadonly] [location] [extensionworkingmode]
                 [extensionprefix] [defaultrepserver] [organization]

    setfileasreadonly       Muestra si los ficheros protegidos se dejan en modo
                            solo lectura o no.
    location                Muestra la ruta del fichero de configuración del
                            cliente.
    extensionworkingmode    Muestra el modo de trabajo de la extensión.
    extensionprefix         Muestra el prefijo de la extensión configurada.
    defaultrepserver        Muestra el servidor de repositorios configurado por
                            defecto.
    organization            Muestra la información de la organización del workspace
                            o predeterminada tal y como figura en la configuración.

== CMD_HELP_GETCONFIG ==
Ejemplos:

    cm getconfig setfileasreadonly

== CMD_DESCRIPTION_GETCONFIG_GETORGANIZATION ==
Obtiene información de una organización Cloud de la configuración.
Si no se proporciona ninguna, se usará implicitamente la organización
del workspace o la predeterminada.

== CMD_USAGE_GETCONFIG_GETORGANIZATION ==
Sintaxis:

    cm getconfig getorganization [organization] [--format=<str_format>]

    organization    Una especificación de servidor explícita incluyendo la organización.
    --format        Muestra el mensaje de salida en el formato indicado.
                    Consulte las Notas para más información.

== CMD_HELP_GETCONFIG_GETORGANIZATION ==
Notas:

    Parámetros de formato de salida (opción --format):
    Este comando acepta una cadena de formato para mostrar la salida.

    Los parámetros de salida de este comando son los siguientes:
    {name}       Nombre de la organización.
    {type}       Puede ser 'cloud' o 'unity'.
    {unityid}    El ID de la organización de Unity (si lo tuviese, de lo contrario sería -1).
    {region}     La región de la organización.

Ejemplos:

    cm getconfig organization --format={unityid}
    cm getconfig organization myorg@cloud

== CMD_DESCRIPTION_GETFILE ==
Descarga el contenido de una revisión especificada.

== CMD_USAGE_GETFILE ==
Sintaxis:

    cm getfile | cat <revspec>[[;<output_file>] | [--file=<output_file>]]
                     [--debug] [--symlink] [--raw]

    revspec     Especificación de la revisión. (Use 'cm help objectspec' para
                más información sobre las especificaciones de revisiones.)

Opciones:

    --file          Fichero donde guardar la salida. Por defecto, se imprime en
                    la salida estándar. Esta opción sólo se debe usar cuando
                    se proporciona una única revisión. Si se desea añadir más de
                    una revisión, esta opción no debería usarse. En su lugar,
                    se debería emplear la versión de pares de 'revspec;output_file',
                    añadiendo tantos pares como se desee separados por espacios
                    en blanco.
    --debug         Cuando se especifica la revisión de un directorio, el
                    comando muestra el id de la revisión y la protección del
                    sistema del fichero.
    --symlink       Aplica la operación al symlink pero no al destino (target).
    --raw           Muestra los datos del fichero sin procesar (raw data).

== CMD_HELP_GETFILE ==
Ejemplos:

    cm cat myfile.txt#br:/main
    (Obtiene la última revisión del fichero 'myfile.txt' en la rama 'br:/main'.)

    cm getfile myfile.txt#cs:3 --file=tmp.txt
    (Obtiene el changeset 3 del fichero 'myfile.txt' y escribe su contenido en
    el fichero 'tmp.txt'.)

    cm cat serverpath:/src/foo.c#br:/main/task003@myrepo
    (Obtiene el contenido del fichero '/src/foo.c' del último changeset de la
    rama '/main/task003' en el repositorio 'myrepo'.)

    cm cat revid:1230@rep:myrep@repserver:myserver:8084
    (Obtiene la revisión con id igual a 1230.)

    cm getfile rev:info\ --debug
    (Obtiene todas las revisiones del directorio 'info'.)

    cm getfile "revid:25@rep:default@repserver:localhost:8084;file_revid25.txt" 
        "revid:16@rep:default@repserver:localhost:8084;file_revid_16.txt"
    (Obtiene dos revisiones y las almacena  en diferentes archivos. Para listar
    revid es posible usar el comando 'cm find revision'. Considera que para
    especificar colecciones de 'revspec;output_file' podría ser necesario
    entrecomillar cada par individualmente y después separar cada uno por
    espacios en blanco).

== CMD_DESCRIPTION_GETREVISION ==
Carga una revisión en el espacio de trabajo.

== CMD_USAGE_GETREVISION ==
Este comando modifica la revisión cargada en el espacio de trabajo, con lo que
puede afectar a futuros merges.
Este es un comando avanzado heredado de antiguas versiones; úselo con precaución.

Sintaxis:

    cm getrevision <revspec>

    revspec     Especificación de la revisión. (Use 'cm help objectspec' para
                más información sobre las especificaciones de revisiones.)


== CMD_HELP_GETREVISION ==
Ejemplos:

    cm getrevision file.txt#cs:3
    (Carga la revisión del changeset 3 del fichero 'file.txt'.)

== CMD_DESCRIPTION_GETSTATUS ==
Obtiene el estado de un ítem.

== CMD_USAGE_GETSTATUS ==
Este comando está pensado para la automatización de otros comandos. Con lo que,
es posible, que no resulte muy intuitivo.

Sintaxis:

    cm getstatus | gs <item_path>[ ...] [--format=<str_format>] [--stats]
                      [-R | -r | --recursive]

    item_path       Ítem o ítems de los que obtener el estado. Use comillas
                    dobles (" ") para especificar rutas que contengan espacios.
                    Use un espacio en blanco para separar rutas de ítems.

Opciones:

    --format        Muestra el mensaje de salida en el formato indicado.
                    Consulte las Notas para más información.
    --stats         Muestra estadísticas sobre el proceso de obtención del estado.
    -R              Muestra recursivamente el estado en las rutas indicadas.

== CMD_HELP_GETSTATUS ==
Notas:

    Parámetros de formato de salida (opción --format):
        Este comando acepta una cadena de formato para mostrar la salida.
        Los parámetros de salida de este comando son los siguientes:
        {0}             Ruta del ítem.
        {1}             Estado del ítem:
            0   privado,
            1   protegido,
            2   desprotegido.

Lectura desde stdin:

    El comando 'getstatus' puede leer rutas desde stdin. Para ello, pase como
    argumento un guión "-".
    Por ejemplo: cm getstatus -

    Las rutas se leerán hasta que se introduzca una línea en blanco.
    Este tipo de lectura permite usar una tubería (pipe) para especificar de qué
    ficheros se van a obtener su estado.
    Por ejemplo:
      dir /S /B *.c | cm getstatus --format="Path {0} Status {1}" -
      (En Windows, obtiene el estado de todos los ficheros del workspace con
      extensión .c.)

Ejemplos:

    cm getstatus file1.txt file2.txt
    (Obtiene el estado de los ficheros.)

    cm gs info\ -R --format="The item {0} has the status {1}"
    (Obtiene el estado del directorio y de todos sus ficheros y muestra la
    salida formateada.)

== CMD_DESCRIPTION_GETTASKBRANCHES ==
Obtiene las ramas asociadas con una tarea.

== CMD_USAGE_GETTASKBRANCHES ==
Este comando está pensado para la automatización de otros comandos. Con lo que,
es posible, que no resulte muy intuitivo.

Sintaxis:

    cm gettaskbranches | gtb <task_name> [--format=<str_format>] 
                             [--dateformat=<date_format>]

    task_name           Identificador de la tarea.

Opciones:

    --format        Muestra el mensaje de salida en el formato indicado.
                    Consulte las Notas para más información.
    --dateformat    Configura el formato de salida para la impresión de fechas.

== CMD_HELP_GETTASKBRANCHES ==
Notas:

    Parámetros de formato de salida (opción --format):
        Este comando acepta una cadena de formato para mostrar la salida.
        Los parámetros de salida de este comando son los siguientes:
        {tab}           Inserta un tabulador.
        {newline}       Inserta una nueva línea.
        {name}          Nombre de la rama.
        {owner}         Propietario de la rama.
        {date}          Fecha de creación de la rama.
        {parent}        Rama padre.
        {comment}       Comentario de la rama.
        {repname}       Repositorio donde se encuentra la rama.
        {repserver}     Nombre del servidor.

Ejemplos:

    cm gettaskbranches 4311
    cm gtb 4311 --format="br:{name}"
    cm gtb 4311 --format="br:{name} {date}" --dateformat="yyyy/MM/dd HH:mm:ss"

== CMD_DESCRIPTION_GETWORKSPACEINFO ==
Muestra información del selector del espacio de trabajo.

== CMD_USAGE_GETWORKSPACEINFO ==
Sintaxis:

    cm wi [<wk_path>]

    wk_path         Ruta en disco del workspace a consultar.

== CMD_HELP_GETWORKSPACEINFO ==
Notas:
    El comando 'wi' muestra la configuración actual del espacio de trabajo
    (repositorio, rama o etiqueta.)

Ejemplos:
    cm wi c:\mywk

== CMD_DESCRIPTION_GETWORKSPACEFROMPATH ==
Obtiene información del espacio de trabajo a partir de una ruta.

== CMD_USAGE_GETWORKSPACEFROMPATH ==
Este comando está pensado para la automatización de otros comandos. Con lo que,
es posible, que no resulte muy intuitivo.

Sintaxis:

    cm getworkspacefrompath | gwp <item_path> [--format=<str_format>] [--extended]

    item_path       Fichero o directorio en disco.

Opciones:
    --format        Muestra el mensaje de salida en el formato indicado.
                    Consulte las Notas para más información.
    --extended      Muestra más información sobre el espacio de trabajo: type y 
                    dynamic además del resto de campos. Consulte las Notas para 
                    más información.

== CMD_HELP_GETWORKSPACEFROMPATH ==
Notas:

    Este comando muestra información del espacio de trabajo de la ruta indicada.

    Parámetros de formato de salida (opción --format):
        Este comando acepta una cadena de formato para mostrar la salida.
        Los parámetros de salida de este comando son los siguientes:
        {0} | {wkname}          Nombre del espacio de trabajo.
        {1} | {wkpath}          Ruta del espacio de trabajo.
        {2} | {machine}         Nombre de la máquina del cliente.
        {3} | {owner}           Propietario del espacio de trabajo.
        {4} | {guid}            GUID del espacio de trabajo.
        {5} | {type}            Tipo de espacio de trabajo: partial (gluon) o regular.
        {6} | {dynamic}         Un espacio de trabajo puede ser dynamic (plasticfs)
                                o static.

        {tab}                   Inserta un tabulador.
        {newline}               Inserta una nueva línea.

Ejemplos:

    cm gwp c:\myworkspace\code\file1.cpp --format="Espacio de trabajo: {wkname}"
    cm gwp . --format="Nombre: {wkname} | Tipo: {type}, {dynamic}"

== CMD_DESCRIPTION_HELP ==
Muestra la ayuda de un comando de Unity VCS.

== CMD_USAGE_HELP ==
Sintaxis:

    cm help <command>

== CMD_HELP_HELP ==

== CMD_DESCRIPTION_IOSTATS ==
Muestra estadísticas relativas al hardware.

== CMD_USAGE_IOSTATS ==
Sintaxis:

    cm iostats [<repserverspec>] [<list_of_tests>[ ...]]
               [--nettotalmb=<value_mb>] [--networkiterations=<value_iter>]
               [--diskdatasize=<value_size>] [--disktestpath=<value_path>]
               [--systemdisplaytime=<value_time>]
               [--systemdisplaytimeinterval=<value_interval>]

Opciones:

    repserverspec                 Servidor de Unity VCS disponible para poder
                                  realizar pruebas de red como, por ejemplo,
                                  "serverUploadTest" o "serverDownloadTest".
                                  Si no se indica ningún servidor, el comando
                                  intentará comunicarse con el servidor
                                  configurado por defecto.
                                  (Use 'cm help objectspec' para más información
                                  sobre las especificaciones de servidores.)
    list_of_tests                 Tests disponibles. Use un espacio en blanco
                                  para separar los diferentes tests.
                                  Consulte las Notas para más información.
    --nettotalmb                  Cantidad de datos de usuario (en MegaBytes)
                                  transmitidos en un test de red, por ejemplo,
                                  "serverDownloadTest" y "serverUploadTest".
                                  Debe ser un valor comprendido entre "4" y "512"
                                  (por defecto, 16).
    --networkiterations           Número de iteraciones de los tests de red
                                  "serverDownloadTest" y "serverUploadTest".
                                  Debe ser un valor comprendido entre "1" y "100"
                                  (por defecto, 1).
    --diskdatasize                Cantidad de datos (en MegaBytes) que se 
                                  escribirán y leerán en el test de disco
                                  "diskTest".
                                  Debe ser un valor comprendido entre "100" y 
                                  "4096" (por defecto, 512).
    --disktestpath                Ruta donde el test de disco "diskTest" escribe
                                  los ficheros de tests. Si no se indica ninguna
                                  ruta, se intentará usar el directorio temporal
                                  del sistema.
    --systemdisplaytime           Intervalo de tiempo (en segundos) en el que se
                                  muestra el uso de los recursos del sistema.
                                  Esta opción está disponible para los tests
                                  "systemNetworkUsage" y "systemDiskUsage".
                                  Debe ser un valor comprendido entre "1" y
                                  "3600" (por defecto, 5 segundos).
     --systemdisplaytimeinterval  Intervalo de tiempo (en segundos) entre las
                                  muestras de rendimiento del sistema. Esta
                                  opcion está disponible para los tests
                                  "systemNetworkUsage" y "systemDiskUsage".
                                  Debe ser un valor comprendido entre "1" and "60"
                                  (por defecto, 1 segundo).

== CMD_HELP_IOSTATS ==
Notas:

    Este comando requiere de un servidor disponible para usarse durante los tests
    de velocidad de red ("serverUploadTest" y "serverDownloadTest").

    La ruta de la opción '--diskTestPath' debe ser una ruta dentro de la unidad
    física de disco que se va a probar. Si no se especifica ninguna ruta, el
    comando intentará usar la ruta temporal por defecto del sistema.
    La unidad de disco de la ruta especificada debe tener suficiente espacio
    libre para ejecutar la prueba.

    Durante la ejecución del comando, el sistema puede sufrir una caída de
    rendimiento a causa de las pruebas realizadas.

    Test disponibles:
        --serveruploadtest      (Defecto). Mide la velocidad de subida de datos
                                desde el cliente de Unity VCS al servidor.
        --serverdownloadtest    (Defecto). Mide la velocidad de bajada de datos
                                desde el servidor de Unity VCS al cliente.
        --disktest              (Defecto). Mide la velocidad de lectura y
                                escritura de disco.
        --systemnetworkusage    Muestra el uso actual de los recursos de red del
                                sistema. Se muestran contadores de rendimiento
                                de interfaz de red (Network Interface)
                                proporcionados por Microsoft Windows.
                                Test disponible solo en Microsoft Windows.
        --systemdiskusage       Muestra el uso actual de los discos físicos del
                                sistema. Se muestran contadores de rendimiento
                                de interfaz de red (Network Interface)
                                proporcionados por Microsoft Windows.
                                Test disponible solo en Microsoft Windows.
Ejemplos:

    cm iostats MYSERVER:8087 --serveruploadtest --serverdownloadtest --nettotalmb=32

== CMD_DESCRIPTION_ISSUETRACKER ==
Obtiene, actualiza o busca el estado de una tarea en el sistema de incidencias
(issue tracker) especificado.

== CMD_USAGE_ISSUETRACKER ==
Sintaxis:

    cm issuetracker <name> status get <task_id> <parameter>[ ...]
    cm issuetracker <name> status update <task_id> <status> <parameter>[ ...]
    cm issuetracker <name> status find <status> <parameter>[ ...]
    cm issuetracker <name> connection check <parameter>[ ...]

    name            Nombre del sistema de incidencias.
                    Solo Jira está soportado por el momento.
    task_id         Número de la tarea que se quiere consultar o actualizar.
    status          Estado de tarea válido en el sistema especificado.

Parámetros de Jira (todos son obligatorios):

    --user=<user>       El usuario a autenticar.
    --password=<pwd>    La contraseña a autenticar.
    --host=<url>        La url del sistema de seguimiento de incidencias.
    --projectkey=<key>  La clave del proyecto de Jira.
    
== CMD_HELP_ISSUETRACKER ==
Ejemplos:

    cm issuetracker jira status get 11 --user=<EMAIL> --password=pwd \
      --host=https://user.atlassian.net --projectkey=PRJ
    (Obtiene el estado de la tarea 11 para el proyecto 'PRJ'.)
       
    cm issuetracker jira status update 11 "Done" --user=<EMAIL> \
      --password=pwd --host=https://user.atlassian.net --projectkey=PRJ
    (Actualiza el estado a 'Done' de la tarea 11 para el proyecto 'PRJ'.)
    
    cm issuetracker jira status find "Done" --user=<EMAIL> --password=pwd \
      --host=https://user.atlassian.net --projectkey=PRJ
    (Obtiene los ids de las tareas cuyo estado es "Done" en el proyecto 'PRJ'.)
    
    cm issuetracker jira connection check --user=<EMAIL> --password=pwd \
      --host=https://user.atlassian.net --projectkey=PRJ
    (Comprueba si los parámetros de configuración son válidos o no.)

== CMD_DESCRIPTION_LICENSEINFO ==
Muestra información de la licencia y de su uso.

== CMD_USAGE_LICENSEINFO ==
Sintaxis:

    cm licenseinfo | li [--server=<repserverspec>] [--inactive] [--active]
                        [--sort=(name|status)]

Opciones:

    --server:           Obtiene la información de la licencia del servidor
                        especificado. Si no se especifica ningún servidor, el
                        comando se ejecutará contra el servidor configurado en
                        el cliente.
                        (Use 'cm help objectspec' para más información sobre
                        las especificaciones de servidores.)
    --inactive          Muestra únicamente usuarios inactivos en la sección
                        "Uso de licencia".
    --active            Muestra únicamente usuarios activos en la sección de
                        "Uso de licencia".
    --sort              Ordena usuarios usando el campo especificado:
                        'name' o 'status'.

== CMD_HELP_LICENSEINFO ==
Notas:

   Se muestra información acerca de la fecha de expiración, usuarios activos y
   no activos, etc.

Ejemplos:

   cm licenseinfo
   cm licenseinfo --server=myserver:8084
   cm licenseinfo --sort=name

== CMD_DESCRIPTION_LINKTASK ==
Enlaza un changeset a una tarea de un sistema de control de tareas.

== CMD_USAGE_LINKTASK ==
Este comando está pensado para la automatización de otros comandos. Con lo que,
es posible, que no resulte muy intuitivo.

Sintaxis:

    cm linktask | lt <csetspec> <ext_prefix> <task_name>

    csetspec        Especificación del changeset para enlazarlo a una tarea.
                    (Use 'cm help objectspec' para más información sobre las
                    especificaciones de changesets.)
    ext_prefix      Prefijo del sistema de control de tareas configurado.
    task_name       Identificador de la tarea en el sistema de control de tareas.

== CMD_HELP_LINKTASK ==
Ejemplos:

    cm lt cs:8@rep:default@repserver:localhost:8084 jira PRJ-1

== CMD_DESCRIPTION_LOCK_LIST ==
Muestra los bloqueos de un servidor.

== CMD_USAGE_LOCK_LIST ==
Sintaxis:

    cm lock list | ls [<revspec> [ ...]] [--server=<server>] [--anystatus]
                      [--repository] [--workingbranch=<brname>] [--anystatus]
                      [--onlycurrentuser] [--onlycurrentworkspace]
                      [--ignorecase]
                      [--machinereadable [--startlineseparator=<sep>]
                        [--endlineseparator=<sep>] [--fieldseparator=<sep>]
                        [--smartlocks]]

    revspec             Si se especifican una o más revisiones, el comando
                        mostrará una línea de bloqueo por cada revisión solo si
                        el ítem asociado está bloqueado en el servidor.
                        Si no se especifica ninguna revisión, el comando mostrará
                        todos los ítems bloqueados en el servidor por defecto (o
                        en el indicado en la opción '--server').
                        Use un espacio en blanco para separar revisiones en caso
                        de especificar más de una.
                        (Use 'cm help objectspec' para más información sobre las
                        especificaciones de revisiones.)

Opciones:

    --server                Especificación del servidor de repositorios.
                            Esta opción anula el servidor por defecto que se
                            obtiene del espacio de trabajo actual o del fichero
                            client.conf.
                            (Use 'cm help objectspec' para más información sobre
                            las especificaciones de servidores.)
    --repository            Especificación del repositorio. Esta opción lista sólo
                            los bloqueos del repositorio especificado.
                            (Use 'cm help objectspec' para más información sobre
                            las especificaciones de repositorios.)
    --workingbranch         Nombre de la rama. Esta opción lista sólo los bloqueos
                            que aplican a la rama destino de la rama especificada.
    --anystatus             Muestra los locks sin filtrar por estado.
                            Por defecto, sólo se muestran los locks es estado Locked.
    --onlycurrentuser       Filtra el resultado mostrando solo los bloqueos
                            llevados a cabo por el usuario actual.
    --onlycurrentworkspace  Filtra el resultado mostrando solo los bloqueos
                            del espacio de trabajo actual (comparándolos por
                            nombre).
    --ignorecase            Ignora mayúsculas y minúsculas en las rutas donde
                            se usa una especificación de ruta de servidor. Por
                            ejemplo, con esta opción, el comando funcionará tanto
                            con "/src/foo.c" como con "/sRc/fOO.c".
    --dateformat            Configura el formato de salida para la impresión de fechas.
    --machinereadable       Muestra el resultado en un formato fácil de parsear.
    --smartlocks            Usado con '--^machinereadable', muestra todos los campos
                            de los smart locks. Sin él, sólo muestra los campos de
                            que mostraba anteriormente. Está opción es necesario para
                            manterner la compatibilidad con plugins o integraciones
                            antiguas que necesitan el formato anterior.
    --startlineseparator    Usado con '--machinereadable', indica cómo deben
                            empezar las líneas del resultado.
    --endlineseparator      Usado con '--machinereadable', indica cómo deben
                            terminar las líneas del resultado.
    --fieldseparator        Usado con '--machinereadable', indica cómo deben
                            separarse los campos de cada línea resultante.

== CMD_HELP_LOCK_LIST ==
Notas:

    El comando muestra una lista de los elementos actualmente bloqueados en el
    servidor por defecto. También acepta una lista de especificaciones de
    revisión; en este caso, solo se mostrarán los bloqueos asociados a los
    elementos especificados.
    Es posible usar también la opción '--server=<server>' para consultar los
    bloqueos en ese servidor en concreto.

    El comando muestra una línea por cada bloqueo en el servidor especificado:
        - Repositorio del elemento bloqueado.
        - Item id del elemento bloqueado.
        - GUID del elemento bloqueado (sólo se imprime con --machinereadable).
        - Fecha de cuando se realizó el bloqueo.
        - Rama de destino donde el bloqueo será liberado.
        - Revision id del elemento bloqueado en la rama de destino.
        - Rama donde se realizó el bloqueo.
        - Revision id del elemento que mantiene el bloqueo.
        - Estado del bloqueo (Locked o Retained)
        - Nombre del usuario que realizó el bloqueo.
        - Nombre del espacio de trabajo donde se realizó el bloqueo.
        - Ruta del elemento bloqueado (formato de ruta de servidor).
    
    Para el formato --machinereadable sin --smartlocks, los campos mostrados son:
        - GUID del elemento bloqueado.
        - Nombre del usuario que realizó el bloqueo.
        - Nombre del espacio de trabajo donde se realizó el bloqueo.
        - Ruta del elemento bloqueado (formato de ruta de servidor).
        
Ejemplos:

    cm lock list
    cm lock ls --server=myserver:8084
    cm lock ls --repository=repo@myserver:8084 --anystatus
    cm lock ls --repository=repo@myserver:8084 --workingbranch=/main/scm21345
    cm lock ls serverpath:/src/foo.c#cs:99@default@localhost:8084
    cm lock ls revid:3521@default itemid:2381@secondary --onlycurrentuser
    cm lock ls --onlycurrentuser --dateformat="yy/dd/MM HH:mm:ss"
    cm lock ls --onlycurrentuser --onlycurrentworkspace
    cm lock list --machinereadable --startlineseparator=">" \
      --endlineseparator="<" --fieldseparator=","
    cm lock list --machinereadable --smartlocks --startlineseparator=">" \
        --endlineseparator="<" --fieldseparator=","        

== CMD_DESCRIPTION_LISTUSERS ==
Lista usuarios y grupos.

== CMD_USAGE_LISTUSERS ==
Sintaxis:

    cm listusers | lu <repserverspec> [--onlyusers] [--onlygroups]
                      [--filter= <str_filter>]
    cm listusers | lu <repserverspec> --group=<group_name>

    repserverspec       Especificación del servidor de repositorios.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de servidores de repositorio.)

Opciones:

    --onlyusers     Muestra únicamente los usuarios.
    --onlygroups    Muestra únicamente los grupos.
    --filter        Muestra únicamente los usuarios o grupos que coinciden con
                    el filtro especificado.
    --group         Muestra únicamente los usuarios que pertenecen a un grupo
                    determinado. Esta opción no es compatible con onlyusers,
                    onlygroups, ni filter.

== CMD_HELP_LISTUSERS ==
Ejemplos:

    cm lu localhost:8084
    (Muestra todos los usuarios del servidor.)

    cm listusers localhost:8084 --onlyusers --filter=m
    (Muestra los usuarios del servidor cuyo nombre contiene 'm'.)

    cm listusers codice@cloud --group=Administrators
    (Muestra los usuarios del grupo Administrators de la organización 'codice@cloud'.)

== CMD_DESCRIPTION_LOCATION ==
Muestra la ruta del cliente 'cm'.

== CMD_USAGE_LOCATION ==
Sintaxis:

    cm location

== CMD_HELP_LOCATION ==

== CMD_DESCRIPTION_LOCK ==
Este comando permite al usuario gestionar bloqueos.

== CMD_USAGE_LOCK ==
Sintaxis:

    cm lock <command> [options]

Comandos:

    list | ls
    unlock

    Para obtener más información de cada uno de los comandos use lo siguiente:
    cm lock <command> --usage
    cm lock <command> --help

== CMD_HELP_LOCK ==
Ejemplos:

    cm lock
    (Si no se especifica un subcomando, se ejecuta por omisión el subcomando 'list'.)
    cm lock list --anystatus
    cm lock unlock itemid:56@myrep@localhost:8084
    cm lock create /main/task@myrep itemid:56@myrep

== CMD_DESCRIPTION_LOG ==
Obtiene información sobre revisiones en changesets.

== CMD_USAGE_LOG ==
Sintaxis:

    cm log [<csetspec> | <repspec>] [--from=<csetspec_from>] [--allbranches]
           [--ancestors] [--csformat=<str_format>] [--itemformat=<str_format>]
           [--dateformat=<str_date_format>]
           [--xml[=<output_file>]] [--encoding=<name>]
           [--repositorypaths | --fullpaths | --fp]

Opciones:

    csetspec            Obtiene todos los cambios hechos en el changeset
                        especificado.
                        (Use 'cm help objectspec' para más información sobre las
                        especificaciones de changesets.)
    repspec             Obtiene todos los cambios hechos en el repositorio
                        especificado.
                        (Use 'cm help objectspec' para más información sobre las
                        especificaciones de repositorios.)
    --from              Lista todos los cambios hechos en cada changeset desde
                        el changeset especificado en [csetspec_from] hasta el
                        changeset especificado en [csetspec].
                        El changeset [csetspec_from] no se incluye en la salida.
                        Esta opción se ignorará si lo que se proporciona es una
                        especificación de repositorio.
    --allbranches       Muestra información sobre los changesets creados en un
                        intervalo especificado para todas las ramas donde dichos
                        changesets se crearon.
    --ancestors         Muestra información sobre los changesets accesibles
                        a través del padre y de los enlaces de mezcla (enlaces
                        de merge) para el changeset especificado en [csetspec].
                        Si también se especifica el changeset [csetspec_from],
                        este se usará como límite inferior para todas las rutas.
                        Nota: Los cambios del changeset no se mostrarán cuando
                        se use esta opción.
    --csformat          Muestra información del changeset en el formato
                        especificado. Consulte las Notas para más información.
                        No puede combinarse con --xml.
    --itemformat        Muestra información del ítem en el formato especificado.
                        Consulte las Notas para más información.
                        No puede combinarse con --xml.
    --dateformat        Configura el formato de salida para la impresión de fechas.
    --xml               Imprime el resultado en formato XML a la salida estándar.
                        También es posible especificar un fichero de salida. No
                        puede combinarse con --csformat o --itemformat.
    --encoding          Usado con la opción --xml, especifica el encoding que se
                        usará en la salida XML, por ejemplo, utf-8.
                        Consulte la documentación de MSDN en
                        http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                        para obtener la tabla de codificaciones soportadas y su
                        formato (al final de la página en la columna "Name").
    --fullpaths, --fp   Imprime las rutas completas de espacio de trabajo para
                        ficheros y directorios cuando sea posible.
    --repositorypaths   Imprime las rutas de repositorio (rutas de servidor) en
                        lugar de las rutas de espacio de trabajo. (Esta opción
                        anula la opción '--fullpaths'.)

== CMD_HELP_LOG ==
Notas:

    - Si no se especifica ningún changeset [csetspec] ni ninguna otra opción,
      el comando muestra información de cada changeset creado en el último mes
      en cada rama.
    - Si solo se especifica la opción '--from', el comando muestra información
      de cada changeset desde el especificado en '--from' hasta el último
      changeset de la rama donde el especificado fue creado.
    - Si la opción '--allbranches' se indica sin ningún intervalo, el comando
      muestra la misma información que aparecería si solo se hubiera especificado
      'csetspec'.
    - Si se usa la opción '--from', el comando muestra información desde el
      changeset 'csetspec_from'+1 en adelante.
    - El repositorio qe se utiliza para mostrar la información de los changesets
      es el repositorio cargado en la ruta desde donde se ejecuta el comando.

    Este comando acepta una cadena de formato para los ítems ('--itemformat') y
    una cadena de formato para los changesets ('--csformat').

    Los parámetros de salida de '--csformat' son los siguientes:
        {tab}           Inserta un tabulador.
        {newline}       Inserta una línea nueva.
        {changesetid}   Número de changeset.
        {branch}        Rama donde se creó el changeset.
        {date}          Fecha de creación del changeset.
        {owner}         Propietario del changeset.
        {comment}       Comentario del changeset.
        {items}         Ítems involucrados en el changeset.
        {repository}    Repositorio donde existe el changeset.
        {repserver}     Nombre del servidor.

    Los parámetros de salida de '--itemformat' son los siguientes:
        {tab}           Inserta un tabulador.
        {newline}       Inserta una línea nueva.
        {path}          Ruta del ítem.
        {branch}        Rama donde se creó el changeset.
        {date}          Fecha de creación del changeset.
        {owner}         Propietario del changeset.
        {shortstatus}   Imprime el estado en el formato corto. Vea abajo.
        {fullstatus}    Imprime el estado en el formato largo. Vea abajo.

        Formato corto para el estado y su correspondiente formato largo:
            'A'   Added
            'D'   Deleted
            'M'   Moved
            'C'   Changed

    Estas son cadenas válidas de formateo de salida:
        --csformat="{newline}Changeset {changesetid} creado en {date};{tab} ítems cambiados: {items}."
        --itemformat="{newline}El ítem {path} se creó en la rama {branch}."

    --dateformat:
        Para especificar el formato en el que se escribirán las fechas en pantalla.
        Consulte los formatos soportados:
        https://docs.microsoft.com/en-us/dotnet/standard/base-types/custom-date-and-time-format-strings


Ejemplos:

    cm log
    (Muestra información de todos los changesets creados en el último mes en
    cualquier rama.)

    cm log cs:16
    (Muestra la información de los cambios realizados en el changeset 16 en la
    rama en que se creó dicho changeset.)

    cm log cs:16 --csformat="{newline}Changeset {changesetid} creado el día {date}; \
      {tab} ítems cambiados: {items}."
    (Muestra la información en el formato especificado.)

    cm log  --from=cs:20 cs:50
    (Muestra información de todas las revisiones de todos los changesets
    comprendidos entre el 21 y el 50.)

    cm log --from=cs:20 cs:50 --allbranches
    (Muestra información de todas las revisiones de todos los changesets
    comprendidos entre el 21 y el 50 en todas las ramas del repositorio.)

    cm log rep:myrep@localhost:8084
    (Muestra información de todos los changesets creados en el repositorio
    especificado. No hay que especificar ningún espacio de trabajo para ejecutar
    el comando.)

    cm log --from=cs:20@rep:mainRep@localhost:8084
    (Muestra información de todas las revisiones de todos los changesets desde
    el changeset 21. No hay que especificar ningún espacio de trabajo para
    ejecutar el comando porque se está indicando una especificación completa de
    changeset.)

== CMD_DESCRIPTION_LIST ==
Lista el contenido de árbol.

== CMD_USAGE_LIST ==
Sintaxis:

    cm ls | dir [<paths>[ ...]] [--format=<str_format>] [--symlink]
                [--selector[=<selector_format>]] [--tree=<obj_spec>]
                [-R | -r | --recursive]
                [--xml[=<output_file>]] [--encoding=<name>]

Opciones:

    paths               Lista de rutas a mostrar. Use un espacio en blanco para
                        separar rutas.
                        Use comillas dobles (" ") para especificar rutas que
                        contengan espacios.
    --format            Muestra el mensaje de salida en el formato indicado.
                        Consulte las Notas para más información. No puede
                        combinarse con --xml.
    --symlink           Incluye en el checkin al symlink pero no al destino
                        (target).
    --selector          Obtiene el contenido del selector del espacio de trabajo
                        activo.
                        Si se especifica un valor para 'selector_format', entonces
                        muestra el selector especificado.
                        Esta opción está prácticamente obsoleta ya que los
                        selectores no son una parte central de Unity VCS desde la
                        versión 4.x.
    --tree              Lista el árbol en el changeset o rama especificados.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones.)
    -R                  Lista recursivamente.
    --xml               Imprime el resultado en formato XML a la salida estándar.
                        También es posible especificar un fichero de salida. No
                        puede combinarse con --format.
    --encoding          Usado con la opción --xml, especifica el encoding que se
                        usará en la salida XML, por ejemplo, utf-8.
                        Consulte la documentación de MSDN en
                        http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                        para obtener la tabla de codificaciones soportadas y su
                        formato (al final de la página en la columna "Name").

== CMD_HELP_LIST ==
Notas:

    - Es posible especificar rutas con metacaracteres (*, ?, ...).
    - La lista resultante depende del selector del espacio de trabajo.
    - La salida del comando se puede formatear especificando una cadena de formato.
    - Si no se especifica alguna de estas opciones '--tree' o '--selector',
      entonces la ruta indicada tiene que ser una ruta de servidor ('cm path'),
      por ejemplo, /dir/file.txt, y no una ruta de espacio de trabajo, por
      ejemplo, C:\Users\<USER>\mywk\dir\file.txt.
    - Si no se indica ninguna ruta, se asumirá que la ruta de espacio de trabajo
      es el directorio actual. Si se especifica alguna de estas opciones '--tree'
      o '--selector', entonces se asumirá como ruta la raíz ("/").

    La cadena de formato por defecto que se mostrará es:
      "{size,10} {date:dd/MM/yyyy} {date:HH:mm} \
       {type,-6} {location,-12} {checkout,-5} {name} \
       {symlinktarget}"

    Parámetros de formato de salida (opción --format):
        Este comando acepta una cadena de formato para mostrar la salida.
        Los parámetros de salida de este comando son los siguientes:
        {size}
        {formattedsize}
        {date}
        {type}
            dir     directorio,
            txt     fichero de texto,
            File    fichero.
        {location}      Por ejemplo: br:branch#cset
        {checkout}
        {name}
        {changeset}
        {path}
        {repspec}
        {owner}
        {revid}
        {parentrevid}
        {itemid}
        {brid}
        {repid}
        {server}
        {symlinktarget}
        {hash}
        {chmod}
        {wkpath}        Ruta relativa a la raíz del espacio de trabajo
        {branch}
        {newlocation}   cset@branch
        {guid}          (El comando tardará más tiempo en resolverse)
        {itemguid}

    You can customize the 'ls' format setting the PLASTIC_LS_FORMAT environment
    variable.


    Configure la variable de entorno PLASTIC_LS_FORMAT para especificar la
    salida del comando y evitar así tener que especificar un valor para la
    opción '--format' cada vez que ejecute el comando 'cm ls'.

Ejemplos:

    cm ls
    cm ls c:\workspace\src

    cm ls --format={name}
    (Muestra solo nombres de ficheros.)

    cm ls --symlink
    (Muestra información de los symlinks y no del fichero o directorio al que
    apuntan.)

    cm ls code --selector
    (Muestra el contenido del subdirectorio 'code' del selector del espacio de
    trabajo actual.)

    cm ls /code --selector="rep 'myrep' path '/' branch '/main'"
    (Muestra el contenido del subdirectorio '/code' del selector especificado.
    La ruta se ha especificado en formato servidor.)

    cm ls /code --tree=44@myrep@denver:7070
    (Lista el subdirectorio '/code' en el changeset 44 del repositorio 'myrep'
    en el servidor 'denver:7070'.)

    cm ls /code --tree=br:/main/scm13596@myrep@denver:7070
    (Lista el subdirectorio '/code' en el último changeset de la rama
    '/main/scm13596' del repositorio 'myrep' en el servidor 'denver:7070'.)

    cm ls /code --tree=ae1390ed-7ce9-4ec3-a155-e5a61de0dc77@myrep@denver:7070
    (Lista el subdirectorio '/code' en el changeset
    ae1390ed-7ce9-4ec3-a155-e5a61de0dc77 del repositorio 'myrep' en el servidor
    'denver:7070'.)

== CMD_DESCRIPTION_TRIGGER_LIST ==
Lista los triggers de un determinado tipo en un servidor.

== CMD_USAGE_TRIGGER_LIST ==
Sintaxis:

    cm trigger | tr list | ls [<subtype-type>] [--server=<repserverspec>]
                          [--format=<str_format>]

Opciones:
    subtype-type        Ejecución y operación del trigger.
                        (Use 'cm showtriggertypes' para ver la lista de tipos de
                        triggers.)
    --server            Lista los triggers del servidor especificado.
                        Si no se especifica ningún servidor, el comando se
                        ejecuta en el servidor configurado en el cliente.
                        (Use 'cm help objectspec' para más información sobre
                        las especificaciones de servidor.)
    --format            Muestra el mensaje de salida en el formato indicado.
                        Consulte las Notas para más información.

== CMD_HELP_TRIGGER_LIST ==
Notas:
    Si no se especifica el tipo, muestra todos los triggers del servidor.

    Parámetros de formato de salida (opción --format):
        Este comando acepta una cadena de formato para mostrar la salida.
        Los parámetros de salida de este comando son los siguientes:
        {0}             Posición del trigger.
        {1}             Nombre del trigger.
        {2}             Ruta del trigger.
        {3}             Propietario del trigger.
        {4}             Tipo del trigger.
        {5}             Filtro del trigger.

Ejemplos:
    cm trigger list after-mklabel
    (Lista todos los triggers del tipo 'after-mklabel' en el servidor configurado
    en el cliente.)

    cm tr ls before-mkbranch --server=myserver:8084
    (Lista todos los triggers del tipo 'before-mkbranch' en el servidor
    'myserver:8084'.)

== CMD_DESCRIPTION_MANIPULATESELECTOR ==
Cambia el selector a una fecha.

== CMD_USAGE_MANIPULATESELECTOR ==
Este comando está pensado para la automatización de otros comandos. Con lo que,
es posible, que no resulte muy intuitivo.

Sintaxis:

    cm manipulateselector | ms [<wk_path> | <wk_spec>] --atdate=<sel_date>

    wk_path         Ruta del espacio de trabajo.
    wk_spec         Especificación del espacio de trabajo.
                    (Use 'cm help objectspec' para más información sobre
                    especificaciones de espacios de trabajo.)

Opciones:
    --atdate        Devuelve un selector que recreará el espacio de trabajo con
                    los contenidos que se cargarían en la fecha especificada.

== CMD_HELP_MANIPULATESELECTOR ==
Notas:

      Si no se indica ni la ruta o la especificación del espacio de trabajo, el
      comando tomará el directorio actual como ruta del espacio de trabajo.

Ejemplos:

      cm manipulateselector c:\workspace --atdate=yyyy-MM-ddTHH:mm:ss
      cm manipulateselector --atdate=yyyy-MM-ddTHH:mm:ss
      cm manipulateselector > mySelector.txt --atdate=yyyy-MM-ddTHH:mm:ss
      cm manipulateselector wk:build_wk@BUILDER --atdate=yyyy-MM-ddTHH:mm:ss

== CMD_DESCRIPTION_MERGE ==
Mezcla una rama con otra.

== CMD_USAGE_MERGE ==
Sintaxis:

    cm merge <source_spec> [--merge] [--cherrypicking] [--forced]
                           [--mergetype=(onlyone|onlysrc|onlydst|try|forced)]
                           [--interval-origin=<csetspec> | --ancestor=<csetspec>]
                           [--keepsource | --ks] [--keepdestination | --kd]
                           [--automaticresolution=<conflict-types>[;...]]
                           [--subtractive] [--mount] [--printcontributors]
                           [--noprintoperations] [--silent]
                           [(--to=<brspec> | --destination=<brspec>)[--shelve]]
                           [--no-dst-changes]
                           [-c=<str_comment> | --commentsfile=<comments_file>]
                           [--resolveconflict --conflict=<index>
                           --resolutionoption=(src|dst|(rename --resolutioninfo=<strname>))
                           --mergeresultfile=<path> --solvedconflictsfile=<path>]
                           [--nointeractiveresolution]
                           [--xml[=<output_file>]] [--encoding=<name>]   
                           [--machinereadable [--startlineseparator=<sep>]
                             [--endlineseparator=<sep>] [--fieldseparator=<sep>]]

    source_spec           Especificación del objeto origen de la mezcla (merge).
                          Este puede ser:
                          - especificación de una rama: '[br:/]br_name'
                          - especificación de una etiqueta: 'lb:lb_name'
                          - especificación de un changeset: 'cs:cs_number'
                          - especificación de un almacenamiento (shelve): 'sh:shelve_number'
                          (Use 'cm help objectspec' para más información sobre
                          especificaciones.)
Opciones:

    --merge                   Realiza el merge. Si no se especifica esta opción,
                              se muestran los conflictos encontrados.
    --cherrypicking           Realiza el merge de los cambios incluidos en los
                              changesets origen. Esta opción se omitirá si el
                              origen del merge es una etiqueta.
    --forced                  No comprueba si el origen y el destino ya están
                              conectados.
                              Esta opción solo es válida para el merge de intervalo
                              y para el cherry picking.
    --mergetype               Especifica el tipo de merge. Consulte las Notas
                              para más información.
    --interval-origin         Especifica el changeset inicio del intervalo. El
                              merge solo tendrá en cuenta las diferencias entre
                              el changeset origen y el inicio del intervalo
                              especificado.
    --ancestor                Alias para '--interval-origin'.
    --keepsource              Acepta todos los cambios del contribuidor origen
                              para los elementos con conflictos.
    --keepdestination         Preserva los cambios del contribuidor destino
                              para elementos con conflictos.
    --automaticresolution     Opción para resolver conflictos de directorio. Esta
                              opción permite elegir automáticamente entre el
                              contribuidor origen (source) o el contribuidor
                              destino (destination) para resolver el conflicto.
                              Use punto y coma (;) para separar los tipos de
                              conflicto. Consulte las Notas para más información.
    --subtractive             Elimina los cambios introducidos por un merge.
                              El parámetro (source_spec) que se pasa al comando
                              especifica el origen a partir del cual eliminar los
                              cambios. Debe ser un changeset. En el caso de un
                              intervalo de changesets, debe utilizar la opción
                              '--interval-origin' para definir el inicio del
                              intervalo. Para eliminar un cambio, el sistema
                              crea una nueva revisión desprotegida con todo el
                              contenido anterior excepto los cambios eliminados.
    --mount                   El punto de montaje del repositorio especificado.
    --printcontributors       Muestra los contribuidores (base, origen y destino).
    --noprintoperations       Resuelve las mezclas (merges) sin mostrar
                              información sobre la resolución.
    --silent                  Solo se imprimen mensajes en caso de error.
    --to | --destination      Realiza una operación de merge-to a la rama de
                              especificada con resolución completa de conflictos.
                              (Use 'cm help objectspec' para más información sobre
                              las especificaciones de ramas.)
                              Una mezcla "merge-to" (o mezcla sin espacio de
                              trabajo) es una mezcla que se hace en el lado del
                              servidor. Mientras que las mezclas normales
                              ocurren en un espacio de trabajo mezclando desde
                              ("from") una rama, etiqueta o changeset, una mezcla
                              "merge-to" tiene lugar en el servidor. Mientras que
                              en mezclas normales el destino es el espacio de
                              trabajo, en una mezcla "merge-to" se debe
                              especificar el destino (por eso se llama "to",
                              'hacia' en inglés).
                              Lea la siguiente guía para más información:
                              https://www.plasticscm.com/download/help/mergeto
    --shelve                  Crea un almacenamiento (shelve) con los cambios del
                              resultado del merge (además de información de
                              trazabilidad de la mezcla) en lugar de crear
                              un nuevo changeset. Esta opción no es válida cuando
                              el origen del merge es un shelve. Esta opción sólo
                              está disponible para el merge del lado del servidor
                              (merge-to). Por ello, hay que especificar las
                              opciones '--to' y '--merge'.
    --no-dst-changes          Asegura que el contribuidor destino no tiene cambios
                              (el changeset destino es también el antecesor
                              común). Cuando hay cambios en el destino, no se
                              permite hacer el merge.
    -c                        Añade un comentario al changeset que se crea al
                              hacer la mezcla.
    --commentsfile            Asigna el comentario especificado en el fichero al
                              changeset que se crea al hacer la mezcla.
    --resolveconflict         (Usado principalmente por plugins. Consulte las
                              Notas para más información.)
                              Usado para resolver un conflicto de directorio.
    --conflict                Usado con '--resolveconflict', especifica el
                              índice del conflicto a resolver empezando por 1.
    --resolutionoption        Usado con '--resolveconflict', indica el tipo de
                              resolución de conflicto. Puede ser una de las
                              siguientes opciones: 'src', 'dst', 'rename'.
                              Consulte las Notas para más información.
    --resolutioninfo          Usado con '--resolveconflict', proporciona el
                              nombre que se usará cuando la opción 
                              '--resolutionoption' es 'rename'.
    --mergeresultfile         Usado con '--resolveconflict', guarda en un fichero
                              la información del resultado de la mezcla entre
                              diferentes llamadas. El fichero especificado se
                              crea en la primera llamada y se actualiza en las
                              las siguientes llamadas.
    --solvedconflictsfile     Usado con '--resolveconflict', guarda en un fichero
                              la información de los conflictos solucionados
                              entre diferentes llamadas. El fichero especificado
                              se crea en la primera llamada y se actualiza en
                              las siguientes llamadas.
    --nointeractiveresolution (Usado principalmente por plugins. Consulte las
                              Notas para más información.)
                              Evita preguntar al usuario cuando hay un conflicto
                              manual. Así, por ejemplo, un conflicto de directorio
                              no podrá ser resuelto.
    --machinereadable         (Usado principalmente por plugins. Consulte las
                              Notas para más información.)
                              Muestra el resultado en un formato fácil de parsear.
    --xml                     Imprime el resultado en formato XML a la salida estándar.
                              También es posible especificar un fichero de salida. No
                              puede combinarse con --format ni --resolveconflict.
    --encoding                Usado con la opción --xml, especifica el encoding que se
                              usará en la salida XML, por ejemplo, utf-8.
                              Consulte la documentación de MSDN en
                              http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                              para obtener la tabla de codificaciones soportadas y su
                              formato (al final de la página en la columna "Name").
    --startlineseparator      Usado con '--machinereadable', indica cómo deben
                              empezar las líneas del resultado. (Por defecto,
                              una cadena de texto vacía.)
    --endlineseparator        Usado con '--machinereadable', indica cómo deben
                              terminar las líneas del resultado. (Por defecto,
                              una cadena de texto vacía.)
    --fieldseparator          Usado con '--machinereadable', indica cómo deben
                              separarse los campos de cada línea resultante.
                              (Por defecto, un espacio en blanco.)

== CMD_HELP_MERGE ==
Notas:

    Este comando se utiliza para meclar cambios entre dos ramas, o entre una
    etiqueta y una rama. El destino del merge siempre debe ser una rama.
    El origen del merge se especifica como argumento.
    El destino es el contenido actual del espacio de trabajo.
    Por ejemplo, para mostrar los elementos que se mezclarán desde la rama
    task001 a la rama principal, el selector debe apuntar a la rama principal,
    el espacio de trabajo debe estar actualizado, y entonces ejecutar:
        cm merge br:/task001

    Para que el merge se realice, hay que añadir la opción '--merge':
        cm merge br:/task001 --merge

    Para definir el origen de la mezcla, puede usar las siguientes especificaciones:

    - Especificación de rama (brspec):
        [br:/]br_name
        Por ejemplo: br:/main/task001
        (Mezcla desde el último changeset de la rama especificada.)

    - Especificación de etiqueta (lbspec):
        lb:lb_name
        Por ejemplo: lb:BL001
        (Mezcla desde el último changeset etiquetado.)

    - Especificación de changeset (csetspec):
        cs:cs_number
        Por ejemplo: cs:25
        (Mezcla desde el contenido del changeset especificado.)

    - Especificación de almancenamiento o shelve (shspec):
        sh:shelve_number
        Por ejemplo: sh:2
        (Mezcla desde el contenido del shelve.)

    Para resolver automáticamente conflictos de directorio use la opción
    "--automaticresolution" y especifique el tipo de conflicto seguido por el
    contribuidor (origen o destino) que se seleccionará durante el merge.
    (Separe cada par "tipo de conflicto"-"contribuidor" con punto y coma (;).)
    Por ejemplo:
        cm merge cs:2634 --merge --automaticresolution=eviltwin-src;changedelete-src
        (La operación de merge desde el changeset 2634 resuelve los conflictos
        "eviltwin" y "changedelete" manteniendo al contribuidor origen ("-src")
        en ambos conflictos.)
    - El sufijo "-src" añadido después del tipo de conflicto indica al comando
      merge que debe mantener los cambios del contribuidor origen (source).
    - El sufijo "-dst" mantendrá los cambios del contribuidor destino
      (destination).
    Esta es la lista de tipos de conflictos que soporta el comando merge: 
      "movedeviltwin", "eviltwin", "changedelete", "deletechange", "movedelete",
      "deletemove", "loadedtwice", "addmove", "moveadd", "divergentmove",
      "cyclemove", "all". 
    El valor "all" anula el resto de opciones. En el siguiente ejemplo, el
    conflicto "eviltwin-dst" será ignorado:
        cm merge br:/main/task062 --merge --automaticresolution=all-src;eviltwin-dst
    Consulte el siguiente enlace para más información sobre conflictos de merge:
    https://www.plasticscm.com/download/help/directorymerges

    Estas son los valores para la opción --mergetype:
        onlyone     Merge automático si solo uno de los contribuidores modificó
                    el elemento.
        onlysrc     Merge automático si solo el contribuidor origen modificó el
                    elemento.
        onlydst     Merge automático si solo el contribuidor destino modificó el
                    elemento.
        try         Merge automático si solo un contribuidor ha modificado la
                    parte del fichero en conflicto (para cada conflicto).
        forced      Intenta resolver siempre los conflictos no automáticos.

    Estas son las opciones que se usan principalmente por plugins e integraciones:
        - "--resolveconflict" para resolver un conflicto de directorio. Se 
          deben usar las siguientes opciones:
              - "--conflict" es el índice del conflicto que se quiere resolver
                (empezando por 1).
              - "--resolutionoption" indica el tipo de resolución de conflicto.
                Puede ser:
                    - "src" para mantener el cambio del origen y descartar el
                      cambio del destino
                    - "dst" para mantener el cambio del destino y descartar el
                      cambio del origen
                    - "rename" (solo si el tipo de conflicto soporta esta
                      resolución) para renombar el destino al nombre
                      proporcionado en la opción "--resolutioninfo".
                        - "--resolutioninfo" para proporcionar el nombre a usar
                          con el tipo de resolución "rename"
              - "--mergeresultfile" y "--solvedconflictsfile", ambos se usan
                para guardar informacación de la mezcla entre las diferentes
                llamadas.
        - "nointeractiveresolution" indica a la mezcla que no pregunte al usuario
          en caso de necesitarse una resolución manual de un conflicto.
        - "--machinereadable" y "--startlineseparator", "--endlineseparator",
          "fieldseparator" se usan para imprimir la salida a un formato fácil
          de leer, tratar o parsear.
        Por ejemplo:
        cm merge --machinereadable --startlineseparator=start@_@line \
          --endlineseparator=new@_@line --fieldseparator=def#_#sep \
          --mergeresultfile=C:\Users\<USER>\AppData\Local\Temp\2tmp4D6C.tmp \
          --solvedconflictsfile=C:\Users\<USER>\AppData\Local\Temp\2tmp4D6D.tmp \
          --resolveconflict --conflict=1 --resolutionoption=rename \
          --resolutioninfo=bin_dst br:/main/task --merge

    Configure la variable de entorno PLASTICEDITOR para especificar un editor
    para escribir el comentario. Si la variable de entorno PLASTICEDITOR está
    configurada pero no se indica ningún comentario, el editor se abrirá para
    que pueda escribir el comentario.

Ejemplos:

    cm merge br:/task001
    (No realiza la mezcla, simplemente muestra los elementos que se van a
    mezclar.)

    cm merge br:/task001 --merge
    (Realiza el merge desde la rama task001.)

    cm merge cs:5 --merge --cherrypicking --interval-origin=cs:2
    (Cherry pick del intervalo de changesets (2,5].)

    cm merge cs:8 --merge --subtractive --keepdestination
    (Merge sustractivo del changeset 8, preservando los cambios del destino
    para aquellos elementos con conflictos.)

    cm merge br:/main/task001 --to=br:/main --merge -c="Integrated new UI"
    (Realiza el merge en el lado del servidor -a.k.a. "merge-to"-, desde la
    rama 'task001' hasta la rama 'main' y añade un comentario.)

    cm merge br:/main/task001 --to=br:/main --merge --shelve
    (Realiza el merge en el lado del servidor desde la rama 'task001' a la rama
    'main' y deja el resultado en un shelve.)

    cm merge sh:2 --to=br:/main --merge --no-dst-changes
    (Aplica el shelve 2 en la rama 'main' solo si fue creado desde la cabeza
    actual de 'main'.)

== CMD_DESCRIPTION_ATTRIBUTE_CREATE ==
Crea un atributo nuevo.

== CMD_USAGE_ATTRIBUTE_CREATE ==
Sintaxis:

    cm attribute | att create | mk <att_name>

    att_name            Nombre del atributo.

== CMD_HELP_ATTRIBUTE_CREATE ==
Notas:

    Configure la variable de entorno PLASTICEDITOR para especificar un editor
    para escribir el comentario. Si la variable de entorno PLASTICEDITOR está
    configurada pero no se indica ningún comentario, el editor se abrirá para
    que pueda escribir el comentario.

Ejemplos:

    cm attribute create status
    (Crea el atributo 'status'.)

    cm att mk integrated
    (Crea el atributo 'integrated'.)

== CMD_DESCRIPTION_BRANCH ==
Permite al usuario gestionar ramas.

== CMD_USAGE_BRANCH ==
Sintaxis:

    cm branch | br <command> [options]

Comandos:

    create | mk
    delete | rm
    rename
    hide
    history
    showmain
    showmerges
    unhide

    Para obtener más información sobre cada comando:
    cm branch command --usage
    cm branch command --help

== CMD_HELP_BRANCH ==
Ejemplos:

    cm branch /main/scm21345
    cm branch create /main/scm21345
    cm branch delete /main/scm21345
    cm branch rename /main/scm21345 scm21346
    cm branch hide /main/scm21345
    cm branch history /main/scm21345
    cm branch showmain
    cm branch showmerges file.txt
    cm branch unhide /main/scm21345

== CMD_DESCRIPTION_BRANCH_CREATE ==
Crea una rama.

== CMD_USAGE_BRANCH_CREATE ==
Sintaxis:

    cm branch | br [create | mk] <brspec> 
                   [--changeset=<csetspec> | --label=<lbspec>]
                   [-c=<str_comment> | -commentsfile=<comments_file>]

    brspec         Nombre o especificación de la nueva rama.
                   (Use 'cm help objectspec' para más información sobre
                   especificaciones de ramas.)

Opciones:

    --changeset         Changeset usado como punto de partida de la nueva rama.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de changesets.)
    --label             Etiqueta usada como punto de partida de la nueva rama.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de ramas.)
    -c                  Asigna el comentario especificado a la nueva rama.
    --commentsfile      Asigna el comentario especificado en el fichero a la
                        nueva rama.

== CMD_HELP_BRANCH_CREATE ==
Notas:

    Para crear una rama de primer nivel, especifique el nombre sin jerarquía.
    Por ejemplo:

        cm br /dev

    Si no se especifica el parámetro '--changeset', la base de la nueva rama
    será el último changeset de la rama madre. En caso de que la rama sea de
    primer nivel, se utilizará como base el changeset 0.

    Para especificar comentarios use la opción "-c" o "-m" del siguiente modo:

        cm branch main/task001 -c="Este es el comentario"
        cm branch main/task001 -m "Este es el comentario"

    Configure la variable de entorno PLASTICEDITOR para especificar un editor
    para escribir el comentario. Si la variable de entorno PLASTICEDITOR está
    configurada pero no se indica ningún comentario, el editor se abrirá para
    que pueda escribir el comentario.

Ejemplos:

    cm branch task001
    cm branch create task001
    cm branch mk task001
    cm br mk task001
    (Todos los ejemplos anteriores, crean una rama de primer nivel llamada
    'task001' en el repositorio del espacio de trabajo actual.)

    cm branch br:/task001/task002
    (Crea la rama 'task002' como hija de 'task001'.)

    cm br /main/task001@myrep@myserver:8084 -c="my comment"
    (Crea la rama 'task001' como hija de 'main' en el repositorio
    'myrepo@myserver:8084' y asigna como comentario el texto 'my comment'.)

    cm br mk br:/main/task001 --changeset=2837 -commentsfile=commenttask001.txt
    (Crea la rama 'task001' como hija de 'main' con base el changeset '2837'
    y asigna como comentario el texto del fichero 'commenttask001.txt'.)

== CMD_DESCRIPTION_BRANCH_DELETE ==
Borra una o más ramas.

== CMD_USAGE_BRANCH_DELETE ==
Sintaxis:

    cm branch | br delete | rm <brspec>[ ...]
                          [--delete-changesets]

    brspec          Rama a borrar. Use un espacio en blanco para separar ramas.
                    (Use 'cm help objectspec' para más información sobre
                    especificaciones de ramas.)

Opciones:

    --delete-changesets     Para ramas que no estén vacías, primero borra los
                            changesets de la rama, y después borra la rama.
                            La operación fallará en los siguientes casos:
                            - La rama incluye al changeset cero (la rama
                            principal de su repositorio).
                            - Uno o más changesets en la rama tienen una
                            etiqueta apuntándolos.
                            - Se crearon uno o más shelvesets a partir de
                            changesets en la rama.
                            - La rama tiene ramas hijas (incluso si la rama hija
                            está vacía).
                            - Uno o más changesets en la rama son el origen de
                            un merge, y el destino no está incluido en la rama
                            que se está borrando.
                            La operación es atómica. Si no se puede borrar uno
                            o más changesets de la rama, no se borrará ninguno.

== CMD_HELP_BRANCH_DELETE ==
Notas:

   Este comando borra una o más ramas.

Ejemplos:

    cm branch delete /main/task001
    (Borra la rama con nombre 'task001' que es hija de 'main' en el repositorio
    del espacio de trabajo actual.)

    cm br rm main/task002 /main/task012@reptest@myserver:8084
    (Borra la rama '/main/task002' del repositorio del espacio de trabajo actual
    y la rama '/main/task012' del repositorio 'reptest@myserver:8084'.)

    cm br rm main/task002 --delete-changesets
    (Borra la rama '/main/task002' y todos sus changesets en una sola operación.)

== CMD_DESCRIPTION_BRANCH_HIDE ==
Oculta una o más ramas.

== CMD_USAGE_BRANCH_HIDE ==
Sintaxis:

    cm branch | br hide <brspec>[ ...]

    brspec          Rama a ocultar. Use un espacio en blanco para separar ramas.
                    (Use 'cm help objectspec' para más información sobre
                    especificaciones de ramas.)

== CMD_HELP_BRANCH_HIDE ==
Notas:

    Este comando oculta una o más ramas.

Ejemplos:

    cm branch hide /main/task0 /main/task1
    (Oculta las ramas con nombre 'task0' y 'task1' que son hijas de 'main' en el
    repositorio del espacio de trabajo actual.)

    cm br hide br:/main@reptest@server2:8084
    (Oculta la rama'/main' del repositorio 'reptest@myserver:8084'.)

== CMD_DESCRIPTION_BRANCH_RENAME ==
Renombra una rama.

== CMD_USAGE_BRANCH_RENAME ==
Sintaxis:

    cm branch | br rename <brspec> <new_name>

    brspec          Rama a renombrar.
                    (Use 'cm help objectspec' para más información sobre
                    especificaciones de ramas.)
    new_name        Nuevo nombre para la rama.

== CMD_HELP_BRANCH_RENAME ==
Notas:

    Este comando renombra una rama.

Ejemplos:

   cm branch rename /main/task0 task1
   (Renombra la rama '/main/task0' a '/main/task1'.)

   cm br rename br:/main@reptest@server2:8084 secondary
   (Renombra la rama 'main' del repositorio 'reptest' a 'secondary'.)

== CMD_DESCRIPTION_BRANCH_HISTORY ==
Muestra la historia de una rama.

== CMD_USAGE_BRANCH_HISTORY ==
Sintaxis:

    cm branch | br history <brspec> [--dateformat=<date_format>]
                           [--machinereadable]

    brspec          Especificación de la rama de la que obtener la historia.
                    (Use 'cm help objectspec' para más información sobre
                    especificaciones de ramas.)

Opciones:

    --dateformat            Configura el formato de salida para la impresión de
                            fechas.
    --machinereadable       Muestra el resultado en un formato fácil de parsear.

== CMD_HELP_BRANCH_HISTORY ==
Ejemplos:

    cm branch history br:/main/scm001@myrepository@myserver:8084
    (Muestra la historia de la rama '/main/scm001' del repositorio 'myrepository'
    en el servidor 'myserver'.)

    cm br history main --dateformat="yyyy, dd MMMM" --machinereadable
    (Muestra la historia de la rama 'main' del repositorio actual con un formato
    de fecha determinado y con un formato sencillo de parsear.)

== CMD_DESCRIPTION_BRANCH_SHOWMAIN ==
Muestra la rama principal de un repositorio.
Con toda probabilidad, la rama principal de sus repositorios se denominará '/main'.
Este comando está pensado para la automatización de otros comandos.

== CMD_USAGE_BRANCH_SHOWMAIN ==
Sintaxis:

    cm branch | br showmain [<repspec>] [--encoding=<name>]
                            [--format=<format_str>] [--dateformat=<date_format>]

    repspec     Especificación del repositorio de donde mostrar la rama principal.
                Si no se especifica ningún repositorio, se mostrará la rama
                principal del repositorio del espacio de trabajo actual.
                (Use 'cm help objectspec' para más información sobre
                especificaciones de repositorio.)

Opciones:

    --encoding          Especifica el encoding que se usará en la salida, por
                        ejemplo, utf-8. Consulte la documentación de MSDN en
                        http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                        para obtener la tabla de codificaciones soportadas y
                        su formato (al final de la página en la columna "Name").
    --format            Muestra el mensaje de salida en el formato indicado.
                        Consulte las Notas para más información.
    --dateformat        Configura el formato de salida para la impresión de fechas.

== CMD_HELP_BRANCH_SHOWMAIN ==
Notas:

    Este comando muestra la rama principal de un repositorio.

    Parámetros de formato de salida (opción --format):
        Este comando acepta una cadena de formato para mostrar la salida.
        Los parámetros de salida de este comando son los siguientes:
        {id}                Id de la rama.
        {comment}           Comentario.
        {date}              Fecha.
        {name}              Nombre.
        {owner}             Propietario.
        {parent}            Nombre de la rama madre.
        {repository}        Repositorio.
        {repname}           Nombre del repositorio.
        {repserver}         Servidor.
        {changeset}         Último changeset de la rama (head).

Ejemplos:

    cm branch showmain
    (Muestra la rama principal del repositorio del espacio de trabajo actual.)

    cm branch showmain repo@server:8084
    (Muestra la rama principal del repositorio 'repo' en el servidor
    'server:8084'.)

    cm br showmain --dateformat="yyyy, dd MMMM" --encoding=utf-8
    (Muestra la rama principal del repositorio con las fechas en un determinado
    formato y la salida codificada en utf-8.)

    cm br showmain --format="{id} - {name}"
    (Muestra la rama principal del repositorio mostrando únicamente su id y
    nombre.)


== CMD_DESCRIPTION_BRANCH_SHOWMERGES ==
Muestra las ramas que están pendientes de mezclar (merge).

== CMD_USAGE_BRANCH_SHOWMERGES ==
Este comando está pensado para la automatización de otros comandos. Con lo que,
es posible, que no resulte muy intuitivo.

Sintaxis:

    cm branch | br showmerges <item_path>[ ...]
                              [--format=<format_str>] 
                              [--dateformat=<date_format>]

Opciones:

    --format            Muestra el mensaje de salida en el formato indicado.
                        Consulte las Notas para más información.
    --dateformat        Configura el formato de salida para la impresión de fechas.

== CMD_HELP_BRANCH_SHOWMERGES ==
Notas:

    Parámetros de formato de salida (opción --format):
        Este comando acepta una cadena de formato para mostrar la salida.
        Los parámetros de salida de este comando son los siguientes:
        {id}                Id de la rama.
        {comment}           Comentario.
        {date}              Fecha.
        {name}              Nombre.
        {owner}             Propietario.
        {parent}            Nombre de la rama madre.
        {parentid}          Id de la rama madre.
        {repid}             Id del repositorio.
        {repository}        Repositorio.
        {repname}           Nombre del repositorio.
        {repserver}         Servidor del repositorio.

Ejemplos:

    cm branch showmerges file.txt
    (Muestra las ramas involucradas en el merge pendiente del fichero file.txt.)

    cm branch showmerges file.txt --format="{date} {name}" --dateformat="ddMMyy"
    (Muestra las ramas involucradas en el merge mostrando solo la fecha y el
    nombre y con las fechas en un determinado formato.)

== CMD_DESCRIPTION_BRANCH_UNHIDE ==
Desoculta una o más ramas.

== CMD_USAGE_BRANCH_UNHIDE ==
Sintaxis:

    cm branch | br unhide <brspec>[ ...]

    brspec          Rama a desocultar. Use un espacio en blanco para separar ramas.
                    (Use 'cm help objectspec' para más información sobre
                    especificaciones de ramas.)

== CMD_HELP_BRANCH_UNHIDE ==
Notas:

    Este comando desoculta una o más ramas.

Ejemplos:

    cm branch unhide /main/task0 /main/task1
    (Desoculta las ramas con nombre 'task0' y 'task1' que son hijas de 'main' en
    el repositorio del espacio de trabajo actual.)

    cm br unhide br:/main@reptest@server2:8084
    (Desoculta la rama'/main' del repositorio 'reptest@myserver:8084'.)

== CMD_DESCRIPTION_REPOSITORY ==
Permite al usuario administrar repositorios.

== CMD_USAGE_REPOSITORY ==
Sintaxis:

    cm repository | repo <command> [options]

Comandos:

    create | mk
    delete | rm
    list   | ls
    rename
    add

    Para obtener más información sobre cada comando:
    cm repository <command> --usage
    cm repository <command> --help

== CMD_HELP_REPOSITORY ==
Ejemplos:

    cm repository
    cm repository list
    cm repository newrepo
    cm repository create newrepo
    cm repository rename oldname newname
    cm repository add C:\repo\

== CMD_DESCRIPTION_REPOSITORY_CREATE ==
Crea un repositorio en un servidor.

== CMD_USAGE_REPOSITORY_CREATE ==
Sintaxis:

    cm repository | repo <rep_name>
    cm repository | repo <repserverspec> <rep_name>[ ...]
    cm repository | repo [create | mk] <rep_name>

    repserverspec           Especificación del servidor de repositorios.
                            (Use 'cm help objectspec' para más información sobre
                            especificaciones de repositorios de servidor.)
    rep_name                Nombre del nuevo repositorio. Use un espacio en
                            blanco para separar nombres de repositorio.

== CMD_HELP_REPOSITORY_CREATE ==
Ejemplos:

    cm repository MyRep
    cm repo *************:8087 Rep01 Rep01/ModuleA Rep01/ModuleB
    cm repo create Rep01
    cm repo mk list

== CMD_DESCRIPTION_REPOSITORY_DELETE ==
Borra un repositorio de un servidor.

== CMD_USAGE_REPOSITORY_DELETE ==
Sintaxis:

    cm repository | repo delete | rm <repspec>

    repspec         Especificación del repositorio.
                    (Use 'cm help objectspec' para más información sobre
                    especificaciones de repositorios.)

== CMD_HELP_REPOSITORY_DELETE ==
Nota:

    Borra un repositorio del servidor de repositorios.
    No borra los datos de la base de datos; simplemente "desconecta" el
    repositorio de la base de datos para que no sean accesibles. (Los datos
    se pueden "reconectar" de nuevo con el comando 'cm repository add'.)

Ejemplos:

    cm repository delete myrepository@repserver:myserver:8084
    cm repository rm myrepository@myserver:8084
    cm repo rm myrepository

== CMD_DESCRIPTION_REPOSITORY_LIST ==
Lista los repositorios de un servidor.

== CMD_USAGE_REPOSITORY_LIST ==
Sintaxis:

    cm repository | repo [list | ls] [<repserverspec>] [--format=<str_format>]

    repserverspec       Especificación del servidor de repositorios.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de servidor de repositorios.)
    --format            Muestra el mensaje de salida en el formato indicado.
                        Consulte las Notas para más información.

== CMD_HELP_REPOSITORY_LIST ==
Notas:

    Parámetros de formato de salida (opción --format):
        Este comando acepta una cadena de formato para mostrar la salida.
        Los parámetros de salida de este comando son los siguientes:
        {repid}     | {0}           Identificador del repositorio.
        {repname}   | {1}           Nombre del repositorio.
        {repserver} | {2}           Nombre del servidor del repositorio.
        {repowner}  | {3}           Propietario del repositorio.
        {repguid}   | {4}           Identificador único del repositorio.
        {tab}                       Inserta un tabulador.
        {newline}                   Inserta una nueva línea.

    Si el valor de opción '--format' es 'TABLE', la salida se imprimirá en formato
    tabla con los siguientes campos: {repid}, {repname} y {repserver}.

Ejemplos:

    cm repository
    (Lista todos los repositorios.)

    cm repository list localhost:8084 --format="{1, -20} {3}"
    (Muestra el nombre del repositorio utilizando 20 espacios y alineado a la
    izquierda, un espacio y el propietario del repositorio.)

    cm repository ls localhost:8084 --format="{repname, -20} {repowner}"
    (Muestra el mismo resultado que el ejemplo anterior.)

    cm repo ls localhost:8084 --format=TABLE
    (Muestra la lista de repositorios en formato tabla con los siguientes
    campos: id del repositorio, nombre del repositorio, y el nombre del servidor
    de repositorios.)

== CMD_DESCRIPTION_REPOSITORY_RENAME ==
Renombra un repositorio.

== CMD_USAGE_REPOSITORY_RENAME ==
Sintaxis:

    cm repository | repo rename [<repspec>] <new_name>

    repspec             Repositorio a renombrar.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de repositorio.)
    new_name            Nuevo nombre para el repositorio.

== CMD_HELP_REPOSITORY_RENAME ==
Notas:

   Este comando renombre un repositorio.
   Si no se proporciona ninguna especificación de repositorio, el comando tomará
   por defecto el repositorio actual.

Ejemplos:

   cm repository rename development
   (El repositorio actual se renombra a 'development'.)

   cm repo rename rep:default@SERVER:8084 development
   (El repositorio 'default' del servidor 'SERVER' se renombra a 'development'.)

== CMD_DESCRIPTION_REPOSITORY_ADD ==
Conecta un repositorio existente añadiendo su base de datos.

== CMD_USAGE_REPOSITORY_ADD ==
Sintaxis:

    cm repository | repo add <db_file> <rep_name> <repserverspec>

    db_file             Nombre del fichero de la base de datos.
    rep_name            Nombre del repositorio.
    repserverspec       Especificación del servidor de repositorios.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de servidor de repositorios.)

== CMD_HELP_REPOSITORY_ADD ==
Nota:

    Reconecta una base de datos de repositorio ya existente al servidor. Este
    comando es útil para mover un repositorio de un servidor a otro, o para
    restaurar o reconectar un repositorio después de haber usado el comando
    'cm repository delete'.

Ejemplos:

    cm repository add rep_27 myrepository myserver:8084

== CMD_DESCRIPTION_TRIGGER_CREATE ==
Crea un nuevo trigger en un servidor.

== CMD_USAGE_TRIGGER_CREATE ==
Sintaxis:

    cm trigger | tr create | mk <subtype-type> <new_name> <script_path>
                                [--position=<new_position>]
                                [--filter=<str_filter>]
                                [--server=<repserverspec>]

    subtype-type        Ejecución y operación del trigger.
                        (Use 'cm showtriggertypes' para ver la lista de tipos de
                        triggers.)
    new_name            Nombre del nuevo trigger.
    script_path         Ruta en disco del servidor donde se encuentra el script
                        a ejecutar. Si la línea comienza con "webtrigger ", el
                        trigger entenderá que se trata de un trigger web.
                        Consulte las Notas para más información.

Opciones:
    --position          Posición para el trigger especificado. Esta posición no
                        tiene que estar en uso por ningún otro trigger del mismo
                        tipo.
    --filter            Comprueba solo los ítems que concuerdan con el filtro
                        especificado.
    --server            Crea el trigger en el servidor especificado.
                        Si no se especifica ningún servidor, el comando se
                        ejecuta en el servidor configurado en el cliente.
                        (Use 'cm help objectspec' para más información sobre
                        las especificaciones de servidor.)

== CMD_HELP_TRIGGER_CREATE ==
Notas:

    Web triggers: Para crear triggers web escriba "webtrigger <target-uri>"
    como comando de trigger. En este caso, el trigger ejecutará una consulta
    POST contra el URI especificado (donde el cuerpo de la petición contiene un
    diccionario JSON con las variables de entorno del trigger) y una clave INPUT
    apuntando a un array de cadenas de texto.

Ejemplos:
    cm trigger create after-setselector "BackupMgr" "/path/to/script" --position=4

    cm tr mk before-mklabel new "/path/to/script" --server=myserver:8084

    cm tr mk after-mklabel Log "/path/to/script" --filter="rep:myRep,LB*"
    (Este trigger se ejecutará solo si la etiqueta ha sido creada en el
    repositorio 'myRep' y si el nombre de la etiqueta comienza por 'LB'.)

    cm tr mk after-checkin NotifyTeam "webtrigger http://myserver.org/api"

== CMD_DESCRIPTION_MOVE ==
Mueve o renombra un ítem.

== CMD_USAGE_MOVE ==
Sintaxis:

    cm move ruta_origen ruta_destino

    ruta_origen: Ruta origen del ítem.
    ruta_destino: Ruta destino del ítem.

== CMD_HELP_MOVE ==
Notas:

    Este comando mueve o renombra un ítem en el repositorio.
    Los cambios también se realizan en el sistema de ficheros local.
    Si la ruta de origen es un fichero, el destino puede ser un fichero o un
    directorio. En el primer caso, el fichero se renombra, si no, el ítem se
    mueve al directorio destino.
    Si la ruta de origen es un directorio, la ruta destino ha de serlo también.

    Requisitos para mover o renombrar un ítem:
        El ítem ha de estar controlado por Unity VCS.
        El directorio padre del ítem origen y del ítem destino han de estar desprotegidos.

Ejemplos:

    cm move fichero.txt fichero.old (renombra el ítem)
    cm move .\fichero.old .\antiguos (mueve fichero.old al directorio antiguos)
    cm move .\src .\src2 (renombra el directorio)

== CMD_DESCRIPTION_LABEL ==
Este comando permite al usuario efectuar operaciones con etiquetas.

== CMD_USAGE_LABEL ==
Sintaxis:

    cm label | lb command [options]

Comandos:

    create | mk
    delete | rm
    rename

    Para obtener más información sobre cada uno de los comandos ejecute:
    cm label command --usage
    cm label command --help

== CMD_HELP_LABEL ==
Ejemplos

    cm label myNewLabel cs:42
    (el comando 'create' es opcional)
    cm label rename myNewLabel newLabelName
    cm label delete newLabelName

== CMD_DESCRIPTION_LABEL_CREATE ==
Crea una etiqueta nueva o etiqueta una changeset dado.

== CMD_USAGE_LABEL_CREATE ==
Sintaxis:

    cm label [create] spec_etiqueta [spec_changeset | ruta]
                        [--allxlinkedrepositories]
                        [-c=str_comment | -commentsfile=<comments_file>]

    spec_etiqueta: Especificación de la etiqueta. Usa 'cm help objectspec' para
    obtener más información sobre especificaciones.
    spec_changeset: Nombre o especificación del changeset a etiquetar. Usa 
    'cm help objectspec' para obtener más información sobre especificaciones.
    ruta: Ruta del workspace a etiquetar

    Si no se especifica spec_changeset ni ruta se asumirá el directorio actual
    como workspace a etiquetar.

Opciones:

    --allxlinkedrepositories (-s): Crea la etiqueta en todos los repositorios
      enlazados.
    -c: Añade un comentario a la etiqueta creada.
    --commentsfile=comment.txt: Carga el comentario del fichero especificado.

== CMD_HELP_LABEL_CREATE ==
Notas:

    Configure la variable de entorno PLASTICEDITOR para especificar un editor
    para escribir el comentario. Si la variable de entorno PLASTICEDITOR está
    configurada pero no se indica ningún comentario, el editor se abrirá para
    que pueda escribir el comentario.

Ejemplos:

    cm label create lb:BL001 cs:1203 -commentsfile=commentlb001.txt
    cm label BL002 cs:=1203 -c="first release"

== CMD_DESCRIPTION_LABEL_DELETE ==
Borra una o más etiquetas.

== CMD_USAGE_LABEL_DELETE ==
Sintaxis:

    cm label delete spec_etiqueta1 spec_etiqueta2 ...

    spec_etiqueta:  Etiquetas a borrar, separadas por espacios.
                    Usa 'cm help objectspec' para obtener más información
                    sobre la especificación de etiquetas.

== CMD_HELP_LABEL_DELETE ==
Notas:

   Este comando borra una o más etiquetas creadas previamente.

Ejemplos:

   cm label delete lb:BL001 (Se borra la etiqueta 'BL001')
   cm label delete lb:BL001 lb:BL002

== CMD_DESCRIPTION_LABEL_RENAME ==
Renombra una etiqueta.

== CMD_USAGE_LABEL_RENAME ==
Sintaxis:

    cm label rename spec_etiqueta nuevo_nombre

    sepec_etiqueta: Especificación de la etiqueta que se va a renombrar.
                    Usa 'cm help objectspec' para obtener más información sobre
                    la especificación de etiquetas.
    nuevo_nombre:   Nuevo nombre para la etiqueta.

== CMD_HELP_LABEL_RENAME ==
Notas:

   Este comando renombra una etiqueta.

Ejemplos:

   cm label rename lb:BL001 BL002
   (La etiqueta 'BL001' se renombra a 'BL002')

== CMD_DESCRIPTION_OBJECTSPEC ==
Explica cómo escribir object specs (especificaciones de objetos).

== CMD_USAGE_OBJECTSPEC ==
Sintaxis:
    cm objectspec
    Para obtener toda la información sobre cómo construir specs de objetos.

== CMD_HELP_OBJECTSPEC ==
Muchos comandos de Unity VCS esperan 'object specs' como entrada para
referirse a un objeto concreto (normalmente una rama, un changeset, un
repositorio, etc.).

Esta documentación describe los diferentes tipos de "specs" disponibles y
cómo construirlas.

Cada tipo de especificación comienza con un tag único, por ejemplo "rep:" o "cs:".
El tag se debe indicar para comandos que reciban especificaciones generales
de objetos, como por ejemplo "cm setowner object_spec", pero a menudo se pueden
omitir en comandos que solo acepten un tipo de especificación, como por ejemplo
"cm getfile revision_spec".

== Repository server spec (repserverspec) ==
    repserver:name:port

    Ejemplos:
        cm lrep repserver:skull:8084
        cm lrep skull:8084

    Nota:
        Lo llamamos "rep" server spec (con el 'rep' delante) en lugar de
        simplemente 'server spec' por motivos históricos. Hace mucho tiempo
        Unity VCS tenía servidores de workspaces y repositorios separados, y
        el nombre ha sobrevivido.

== Repository spec (repspec) ==
    rep:rep_name@[repserverspec]

    Ejemplos:
        cm showowner rep:codice@localhost:6060
        (Aquí la "rep:" es necesaria porque el comando showowner admite
         todo tipo de objetos).

== Branch spec (brspec) ==
Hay distintos tipos de specs de rama:

    br:[/]br_name[@repspec]

    br:brguid:br_guid[@repspec]

    Ejemplos:
        cm switch br:/main@rep:plastic@repserver:skull:9095
        (En este caso "br:", "rep:" y "repserver:" no son necesarios así
         que el comando se puede escribir de forma mucho más compacta:
         "cm switch main@plastic@skull:9095" .)

        cm find revisions "where branch='br:/main/task001'"

        cm switch br:brguid:68846cdd-6a46-458c-a47f-52454cc150d9@plastic@skull:9095

        cm find branch "where parent='brguid:68846cdd-6a46-458c-a47f-52454cc150d9'"        

    Nota:
        La barra inicial '/' en las ramas no es obligatoria. Solíamos escribir
        todas las ramas como /main, /main/task001, etc. Pero ahora preferimos
        user formatos más cortos tipo main, main/task001.

        La spec por guid no es válida en comandos que necesitan el nombre
        completo de la rama (como "cm create branch").

== Changeset spec (csetspec) ==
    cs:cs_number|cs_guid[@repspec]

    Se puede especificar el número o el GUID del changeset.

    Ejemplos:
        cm ls /code --tree=ae1390ed-7ce9-4ec3-a155-e5a61de0dc77@code@skull:7070

== Label spec (labelspec) ==
    lb:lb_name[@repspec]

    Ejemplos:
        cm switch lb:RELEASE2.0
        cm switch lb:RELEASE1.4@myrep@MYSERVER:8084

== Revision spec ==
Hay distintos tipos de specs de revisiones:

    rev:item_path[#(brspec|csetspec|labelspec)]

    rev:serverpath:item_path#(brspec|cset_spec|lb_spec)

    rev:revid:rev_id[@rep_spec]

    rev:itemid:item_id#(br_spec|cset_spec|lb_spec)

    Ejemplos:
        cm diff rev:readme.txt#cs:19 rev:readme.txt#cs:20

        cm diff serverpath:/doc/readme.txt#cs:19@myrepo \
            serverpath:/doc/readme.txt#br:/main@myrepo@localhost:8084

        cm cat revid:1230@rep:myrep@repserver:myserver:8084

== Item spec ==
Hay distintos tipos de specs de ítem. Se usan principamente con los
comandos relacionados con bloqueos (checkout exclusivo).

    item:item_path
    item:itemid:item_id[@rep_spec]

    Ejemplos:
        cm lock unlock item:audio.wav
        cm lock unlock itemid:1234@rep:myrep@repserver:myserver:8084

== Attribute spec ==
    att:att_name[@repspec]

    Ejemplo:
        cm rmattributereal att:merged@code@doe:8084 cs:25@code@doe:8084

== Workspace specs ==
    wk:name@clientmachine

Se usa muy poco porque solamente aplica a los comandos relacionados con los
workspaces. Es útil para indicar el nombre del workspace en lugar del path.

    Ejemplo:
        cm showselector wk:codebase@modok

    Nota:
        Estas specs vienen de los viejos tiempos de Plastic SCM 2.x en los que
        los 'workspace servers' eran una forma de almacenar metadatos de forma
        centralizada. Se eliminaron por cuestiones de rendimiento.

== CMD_DESCRIPTION_PARTIAL ==
Ejecuta comandos en un espacio de trabajo parcial.

== CMD_USAGE_PARTIAL ==
Sintaxis:

    cm partial comando [opciones]

Comandos:

    configure
    add
    undo
    co     | checkout
    unco   | undocheckout
    ci     | checkin
    mv     | move
    rm     | remove
    stb    | switch
    upd    | update
    shelve | shelveset

    Para obtener más información de cada uno de los comandos ejecute:
    cm partial comando --usage
    cm partial comando --help

== CMD_HELP_PARTIAL ==
Ejemplos:

    cm partial configure +/fondo-azul.png
    cm partial update paisaje-1024.png
    cm partial checkin ojos-verde.png ojos-negro.png

== CMD_DESCRIPTION_PARTIAL_ADD ==
Añade un ítem al repositorio.

== CMD_USAGE_PARTIAL_ADD ==
Sintaxis:

    cm partial add [-R | -r | --recursive] [--silent] [--parents] [--ignorefailed] 
                   [--skipcontentcheck]
                   [--format=<str-format>] [--errorformat=<str-format>]
                   <rutas>+

    rutas               Rutas de los ítems a añadir separados por espacios.
                        Usa comillas (") para especificar rutas con espacios.
                        Usa * para añadir todo el contenido del directorio 
                        actual.

Opciones:

    -R                  Añade los ítems recursivamente.
    --silent            No muestra ninguna salida.
    --parents           Incluye los directorios padre de los ítems indicados en
                        la operación.
    --ignorefailed      Si un ítem no se puede añadir, la operación de add
                        continuará sin él. Importante: si un directorio no se 
                        puede añadir, su contenido tampoco se añadirá.
    --skipcontentcheck  Cuando la extensión no es suficiente para determinar si 
                        el fichero es de tipo texto o binario, se asumirá que es
                        binario en lugar de usar el contenido para detectar el
                        tipo.
    --format            Devuelve la salida en el formato especificado. Consulta
                        los ejemplos para más información.
    --errorformat       En caso de error, devuelve el mensaje en el formato
                        especificado. Consulta los ejemplos para más información.

== CMD_HELP_PARTIAL_ADD ==
Notas:

    Requisitos para añadir ítems:
    - El directorio padre del ítem a añadir debe existir en el repositorio.

Ejemplos:

    cm partial add pic1.png pic2.png
    (Añade los ítems pic1.png y pic2.png.)

    cm partial add c:\workspace\imagen.png
    (Añade el ítem imagen.png en el directorio c:\workspace.)

    cm partial add -R c:\workspace\src
    (Añade recursivamente todos los ítems del directorio especificado.)
    
    cm partial add --parents ejemplos\diseño01.png
    (Añade el ítem diseño01.png y su directorio padre ejemplos.)
    
    cm partial add -R *
    (Añade recursivamente todo el contenido del directorio actual.)

    cm partial add -R * --format="ADD {0}" --errorformat="ERR {0}"
    (Añade recursivamente todo el contenido del directorio actual imprimiendo
    por pantalla 'ADD <item>' para los archivos añadidos correctamente y
    'ERR <item>' para los archivos que no se pudieron añadir.)

== CMD_DESCRIPTION_PARTIAL_CHECKIN ==
Crea una nueva revisión de un ítem.

== CMD_USAGE_PARTIAL_CHECKIN ==
Sintaxis:

    cm partial checkin | ci [opciones] [<ruta>+]
                            [-c=<str_comment> | -commentsfile=<comments_file>]
                            [--all | -a] [--applychanged] [--keeplock]
                            [--symlink] [--ignorefailed]
                            [--machinereadable [--startlineseparator=<sep>]
                              [--endlineseparator=<sep>] [--fieldseparator=<sep>]]

    ruta                Rutas de los ítems a proteger, separados por espacios.
                        Usa comillas (") para especificar rutas con espacios.
                        Usa . para aplicar la operación de checkin al directorio
                        actual.

Opciones:

    -c                      Añade un comentario al changeset creado en la operación
                            de protección.
    -commentsfile           Añade el comentario existente en el fichero especificado
                            al changeset creado en la operación de protección.
    --all | -a              Los ítems cambiados, movidos y borrados localmente en
                            las rutas especificadas también son protegidos.
    --applychanged          Protege los ítems cambiados localmente junto con los 
                            ítems desprotegidos.
    --private               Incluye en el checkin los ítems privados.
    --keeplock              Mantiene el bloqueo de aquellos elementos que estaban
                            bloqueados después de la operación de protección.
    --symlink               Realizar la operación de checkin sobre el fichero de
                            link y no al que apunta.
    --ignorefailed          Los cambios locales que no se pueden aplicar (porque el
                            bloqueo (checkout exclusivo) no se puede obtener o 
                            porque están en conflicto con los cambios del servidor)
                            son descartados y el checkin continúa sin ellos.
    --machinereadable       Muestra el resultado en un formato fácil de parsear.
    --startlineseparator    Usado con '--machinereadable', indica cómo deben
                            empezar las líneas del resultado.
    --endlineseparator      Usado con '--machinereadable', indica cómo deben
                            terminar las líneas del resultado.
    --fieldseparator        Usado con '--machinereadable', indica cómo deben
                            separarse los campos de cada línea resultante.

== CMD_HELP_PARTIAL_CHECKIN ==
Notas:

    - Si no se especifican [opciones] ni [rutas], la operación involucrará a
    todos los cambios pendientes en el espacio de trabajo.
    - La operación de protección se aplica siempre recursivamente desde la
    ruta indicada.
    - Requisitos para proteger un ítem:
      - El ítem debe estar controlado por Unity VCS.
      - El ítem ha de estar desprotegido.
      - Si un ítem ha sido cambiado pero no desprotegido, no es necesario
        especificar la opción --applychanged, salvo que en las rutas a ser
        protegidas se incluyan directorios o rutas con wildcards ('*').

    La revisión ha de tener cambios. Es decir, no se pueden proteger los
    mismos datos.

    Configure la variable de entorno PLASTICEDITOR para especificar un editor
    para escribir el comentario. Si la variable de entorno PLASTICEDITOR está
    configurada pero no se indica ningún comentario, el editor se abrirá para
    que pueda escribir el comentario.

Ejemplos:

    cm partial checkin figura.png paisaje.png
    (Protege los ítems figura.png and paisaje.png.)

    cm partial checkin . -commentsfile=micomentario.txt
    (Protege los ítems de la ruta actual y aplica el comentario existente en el
    fichero micomentario.txt.)

    cm partial ci fondo.png -c="Nuevo fondo añadido" --keeplock
    (Protege el ítem fondo.png, incluye el comentario especificado y mantiene el
    bloqueo.)

    cm partial checkin --applychanged
    (Protege todos los cambios pendientes en el espacio de trabajo.)
    
    cm partial checkin link --symlink
    (Proteger el fichero de symlink y no al que apunta.)

== CMD_DESCRIPTION_PARTIAL_CHECKOUT ==
Desprotege los ítems dejándolos preparados para ser modificados.

== CMD_USAGE_PARTIAL_CHECKOUT ==
Síntaxis:

    cm partial checkout | co [<ruta>+] [--resultformat=str_format] 
                     [--silent] [--ignorefailed]

Opciones:

    ruta                Rutas de los ítems a desproteger, separados por 
                        espacios.
                        Usa comillas (") para especificar rutas con espacios.
                        Usa . para aplicar la operación de checkout al 
                        directorio actual.
    --resultformat      Devuelve la salida en el formato especificado.
    --silent            No muestra ninguna salida.
    --ignorefailed      Si un ítem no se puede desproteger/bloquear (no se puede
                        hacer un checkout exclusivo), la operación de checkout
                        continuará sin él.

== CMD_HELP_PARTIAL_CHECKOUT ==
Notas: 

    Para desproteger un ítem:
    - El ítem debe estar controlado por Unity VCS.
    - El ítem debe estar protegido.
        
    Si se han configurado bloqueos en el servidor (existe el fichero lock.conf),
    entonces cada vez que se produzca una desprotección, Unity VCS comprobará si
    se aplica alguna de las reglas. Si es así, la ruta permanecerá en checkout
    exclusivo (bloqueada), con lo que nadie podrá desprotegerlo simultáneamente.
    Puedes obtener todos los bloqueos del servidor usando 'cm listlocks'.
    Lee la documentación Administrator Guide para más información.

Examples:

    cm partial checkout imagen1.png imagen2.png
    (Desprotege los ítems imagen1.png and imagen2.png.)
    
    cm partial co *.png
    (Desprotege todos los ítems png.)

    cm partial checkout . 
    (Desprotege el directorio actual.)
    
    cm partial checkout -R c:\workspace\paisaje
    (Desprotege recursivamente el directorio paisaje.)

    cm partial co diseño01.png --exclusive 
    (Desprotege el ítem exclusivamente.)

== CMD_DESCRIPTION_PARTIAL_CONFIGURE ==
Permite configurar tu espacio de trabajo, cargando o descargando elementos.

== CMD_USAGE_PARTIAL_CONFIGURE ==
Sintaxis:

    cm partial configure <+|-ruta>+ [--silent] [--ignorefailed] [--ignorecase]
                                    [--restorefulldirs]
                                    [--machinereadable [--startlineseparator=<sep>]
                                      [--endlineseparator=<sep>] [--fieldseparator=<sep>]]

    ruta           Rutas a cargar o descargar, separadas por espacios. 
                   Usa comillas (") para especificar rutas con espacios.
                   Todas las rutas deben comenzar con "/".

Opciones:

    --silent              No muestra ninguna salida.
    --ignorefailed        Ignora cualquier error durante la operación.
    --ignorecase          Ignora mayúsculas y minúsculas. Con esta opción el
                          comando funcionará para "/Data/Textures" incluso si el
                          usuario indica "/data/teXtures".
    --restorefulldirs     Resetea una configuración de directorio incorrecta
                          (ocurre cuando una operación no parcial es ejecutada en
                          un espacio de trabajo parcial).
                          Los directorios de esta lista se configuran por
                          completo (full check), lo que significa que bajarán
                          automáticamente el nuevo contenido durante el update.
                          Esta operación no descarga ningún fichero, simplemente
                          restaura la configuración de directorio en un espacio
                          de trabajo parcial.
    --machinereadable     Muestra el resultado en un formato fácil de parsear.
    --startlineseparator  Usado con '--machinereadable', indica cómo deben
                          empezar las líneas del resultado.
    --endlineseparator    Usado con '--machinereadable', indica cómo deben
                          terminar las líneas del resultado.
    --fieldseparator      Usado con '--machinereadable', indica cómo deben
                          separarse los campos de cada línea resultante.

== CMD_HELP_PARTIAL_CONFIGURE ==
Notas:

    El comando siempre asume recursividad.

Ejemplos:

    cm partial configure +/paisaje-gris.png
    (Carga paisaje-gris.png.)

    cm partial configure -/paisaje-negro.png
    (Descarga paisaje-negro.)

    cm partial configure +/textura -/textura/textura01.png
    (Carga todo el contenido del directorio textura excepto textura01.png.)

    cm partial configure -/
    (Descarga todo el directorio de trabajo.)

    cm partial configure -/ +/
    (Carga todo el directorio de trabajo.)

    cm partial configure +/figura-64.png --ignorefailed
    (Carga figura-64.png, estuviera o no ya cargado previamente.)

    cm partial configure +/ --restorefulldirs
    (Fija todos los directorios para que descarguen el nuevo contenido automáticamente.)

    cm partial configure +/src/lib --restorefulldirs
    (Fija sólo /src/lib y sus subdirectorios para que descarguen el nuevo contenido automáticamente.)

== CMD_DESCRIPTION_PARTIAL_MOVE ==
Mueve o renombra un archivo o directorio.

== CMD_USAGE_PARTIAL_MOVE ==
Sintaxis:

    cm partial move | mv src_path dst_path [--format=str_format]

    src_path            Ruta origen del ítem.
    dst_path            Ruta destino del ítem.

Options:

    --format            Devuelve la salida en el formato especificado.

== CMD_HELP_PARTIAL_MOVE ==
Notas:

    Este comando mueve o renombra un ítem en el repositorio.
    Los cambios también se aplican localmente.
    
    Si la ruta de origen es un fichero, el destino puede ser un fichero o un
    directorio. En el primer caso, el fichero se renombra. En el segundo, el
    fichero se renombra.
    Si la ruta de origen en un directorio, el destino ha de serlo también.

    El ítem a mover o renombrar debe existir.

    Formato:
        {0}             Ruta de origen.
        {1}             Ruta de destino.

Examples:

    cm partial move fichero.png fichero-azul.png
    (Renombra el ítem.)

    cm partial mv .\fichero-azul.png .\azulFicheros
    (Mueve fichero-azul.png a azulFicheros.)

    cm partial move .\diseño .\marketing
    (Renombra un directorio.)

== CMD_DESCRIPTION_PARTIAL_REMOVE ==
Borra un ítem del control de versiones.

== CMD_USAGE_PARTIAL_REMOVE ==
Sintaxis:

    cm partial remove | rm <ruta>+ [--nodisk]

    ruta            Ítems a borrar, separados por espacios.
                    Usa comillas (") para especificar rutas con espacios.

Opciones:

    --nodisk            Borra del control de versiones, pero mantiene el ítem en
                        disco.

== CMD_HELP_PARTIAL_REMOVE ==
Notas:

    Los ítems se borran de disco. Los ítems borrados son borrados del directorio
    padre en el control de versiones.

    Requisitos:
    - El ítem debe estar controlado.

Ejemplos:

    cm partial remove src
    (Borra src. Si src es un directorio, equivale a ejecutar: 
    cm partial remove -R src.)

    cm partial remove c:\workspace\pic01.png --nodisk
    (Borra pic01.png del control de versiones, pero lo mantiene en el disco.)

== CMD_DESCRIPTION_PARTIAL_SHELVESET ==
Use este comando para administrar shelvesets parciales.

== CMD_USAGE_PARTIAL_SHELVESET ==
Sintaxis:

    cm partial shelveset | shelve <command> [options]

Comandos:

    create | mk
    apply
    delete | rm

    Para obtener más información sobre cada comando:
    cm partial shelveset <command> --usage
    cm partial shelveset <command> --help

== CMD_HELP_PARTIAL_SHELVESET ==
Ejemplos:

    cm partial shelveset create -c="my comment"
    cm partial shelveset apply sh:3
    cm partial shelveset delete sh:5

== CMD_DESCRIPTION_PARTIAL_SHELVESET_CREATE ==
Almacena los cambios pendientes escogidos.

== CMD_USAGE_PARTIAL_SHELVESET_CREATE ==
Sintaxis:

    cm partial shelveset | shelve create | mk [<item_path>[ ...]]
                            [-c=<str_comment> | -commentsfile=<comments_file>]
                            [--applychanged] [--symlink] [--ignorefailed]
                            [--machinereadable [--startlineseparator=<sep>]
                              [--endlineseparator=<sep>] [--fieldseparator=<sep>]]

Opciones:

    -c                      Añade un comentario al shelveset creado en la operación
                            de protección.
    -commentsfile           Añade el comentario existente en el fichero especificado
                            al shelveset creado en la operación de protección.
    --applychanged          Protege los ítems cambiados localmente junto con los 
                            ítems desprotegidos.
    --symlink               Realizar la operación de shelve sobre el fichero de
                            link y no al que apunta.
    --ignorefailed          Los cambios locales que no se pueden aplicar (porque el
                            bloqueo (checkout exclusivo) no se puede obtener o 
                            porque están en conflicto con los cambios del servidor)
                            son descartados y el shelve continúa sin ellos.
    --machinereadable       Muestra el resultado en un formato fácil de parsear.
    --startlineseparator    Usado con '--machinereadable', indica cómo deben
                            empezar las líneas del resultado.
    --endlineseparator      Usado con '--machinereadable', indica cómo deben
                            terminar las líneas del resultado.
    --fieldseparator        Usado con '--machinereadable', indica cómo deben
                            separarse los campos de cada línea resultante.

== CMD_HELP_PARTIAL_SHELVESET_CREATE ==
El comando partial shelveset create almacena los ítems en checkout especificados
    dentro del repositorio. De este modo el contenido queda almacenado en el
    servidor sin necesidad de hacer checkin.

Notas:

    - Si no se especifican [opciones] ni [rutas], la operación involucrará a
    todos los cambios pendientes en el espacio de trabajo.
    - La operación de protección se aplica siempre recursivamente desde la
    ruta indicada.
    - La operacion de creación de protección es la operación por defecto, 
    lo que significa que si no se especifica ninguna otra, el comando tratará 
    de ejecutar una nueva creación de protección.
    - Requisitos para proteger un ítem:
      - El ítem debe estar controlado por Unity VCS.
      - El ítem ha de estar desprotegido.
      - Si un ítem ha sido cambiado pero no desprotegido, no es necesario
        especificar la opción --applychanged, salvo que en las rutas a ser
        protegidas se incluyan directorios o rutas con wildcards ('*').

    La revisión ha de tener cambios. Es decir, no se pueden proteger los
    mismos datos.

    Configure la variable de entorno PLASTICEDITOR para especificar un editor
    para escribir el comentario. Si la variable de entorno PLASTICEDITOR está
    configurada pero no se indica ningún comentario, el editor se abrirá para
    que pueda escribir el comentario.

Ejemplos:

    cm partial shelveset figura.png paisaje.png
    (Crea un nuevo shelveset con los ítems figura.png and paisaje.png.)

    cm partial shelveset . -commentsfile=micomentario.txt
    (Crea un nuevo shelveset con los ítems de la ruta actual y aplica el 
    comentario existente en el fichero micomentario.txt.)

    cm partial shelve fondo.png -c="Nuevo fondo añadido"
    (Crea un nuevo shelveset el ítem fondo.png, incluye el comentario 
    especificado.)

    cm partial shelveset --applychanged
    (Crea un nuevo shelveset con todos los cambios locales junto con los 
    cambios controlados pendientes en el espacio de trabajo.)
    
    cm partial shelveset link --symlink
    (Crea un nuevo shelveset con fichero de symlink, en lugar del fichero al 
    que apunta.)

== CMD_DESCRIPTION_PARTIAL_SHELVESET_APPLY ==
El comando partial shelveset apply restaura los contenidos almancenados en el
repositorio.

== CMD_USAGE_PARTIAL_SHELVESET_APPLY ==
Sintaxis:
    cm partial shelveset apply shelve_spec [opciones]

    shelve_spec: Restaura los contenidos almancenados en el repositorio
    identificados por "shelve_spec"

Opciones:
    --encoding=codificación: Especifica la codificación de los ficheros de
      origen (por ejemplo, UTF-8 o Unicode)
    --dontcheckout: No hace checkout de los ficheros con cambios a aplicar,
      los mantiene como cambios locales. Esto es útil para evitar el checkout
      exclusivo al aplicar un shelve.
    --comparisonmethod=tipo: cualquiera de las siguientes opciones:
      ignoreeol:               Ignora diferencias de final de línea.
      ignorewhitespaces:       Ignora diferencias de espacios en blanco.
      ignoreeolandwhitespaces: Ignora diferencias de final de línea y espacios en
                               blanco.
      recognizeall:            Detecta diferencias de final de línea y espacios en
                               blanco.

== CMD_HELP_PARTIAL_SHELVESET_APPLY ==
El comando partial shelveset apply restaura los contenidos almancenados en el
repositorio.

Ejemplo: Aplicar un shelve almacenado en el repositorio:

    cm partial shelveset apply sh:3

== CMD_DESCRIPTION_PARTIAL_SHELVESET_DELETE ==
Borra un shelveset.

== CMD_USAGE_PARTIAL_SHELVESET_DELETE ==
Sintaxis:
    cm partial shelveset delete shelve_spec

    shelve_spec: Elimina los contenidos almancenados en el repositorio
    identificados por "shelve_spec"

== CMD_HELP_PARTIAL_SHELVESET_DELETE ==
El comando partial shelveset delete borra un shelveset creado anteriormente.

Ejemplo: 

    cm partial shelveset delete sh:3
    (Elimina un shelveset almacenado en el repositorio.)


== CMD_DESCRIPTION_PARTIAL_SWITCH ==
Establece la rama especificada como rama de trabajo.

== CMD_USAGE_PARTIAL_SWITCH ==
Sintaxis:

    cm switch branch_spec [--report | --silent] [--workspace=path] [--noinput]
    (Establece la rama de trabajo y actualiza el workspace.)
    
    cm switch branch_spec --configure <+|-ruta>+ [--silent]
                          [--ignorefailed] [--ignorecase] [--workspace=path]
    (Establece la rama de trabajo y configura el workspace como haría el
    comando 'cm partial configure'.)

    branch_spec              Especificación de rama.
    ruta                     Rutas a cargar o descargar, separadas por espacios.
                             Usa comillas (") para especificar rutas con espacios.
                             Todas las rutas deben comenzar con "/".
                        
Opciones:               
                        
    --silent                 No muestra salida.
    --report                 Imprime una lista de los cambios efectuados una vez
                             termina la operación. Pierde efecto si se utiliza
                             la opción --silent conjuntamente.
    --configure              Configura (carga / descarga rutas) el espacio de
                             trabajo después de haber actualizado la rama de trabajo.
                             Usa 'cm partial configure --help' para aprender más
                             sobre como especificar las rutas a configurar.
    --ignorefailed           Ignora cualquier error durante la operación
                             de configuración.
    --ignorecase             Ignora mayúsculas y minúsculas en las rutas a
                             configurar. Con esta opción el comando funcionará
                             para "/Data/Textures" incluso si el usuario indica
                             "/data/teXtures".
    --noinput               Ignora las pregunta interactiva para continuar la
                            operación haciendolos shelve a los cambios pendientes.
    --workspace=path        Ruta del espacio de trabajo a cambiar.
    --forcedetailedprogress Fuerza mostrar progreso detallado incluso cuando se
                            redirige la salida estándar.

Usa 'cm help objectspec' para aprender más sobre cómo indicar especificaciones
de ramas.

== CMD_HELP_PARTIAL_SWITCH ==
Notas:

    Este comando permite a los usuarios especificar una rama, como rama de
    trabajo. Después de actualizar la rama, actualiza el espacio de trabajo
    a la nueva rama como haría un 'cm partial update'. Sin embargo, si se
    especifica la opción --configure, permite configurar el espacio de trabajo
    teniendo en cuenta los elementos cargados en la nueva rama como haría un
    'cm partial configure'.

Ejemplos:

    cm switch br:/main/task
    (Establece /main/task como rama de trabajo y actualiza el espacio de
    trabajo.)

    cm switch br:/main/task --configure +/art/images
    (Establece /main/task como rama de trabajo y configura el directorio
    /art/images.)

== CMD_DESCRIPTION_PARTIAL_UNDOCHECKOUT ==
Deshace la desprotección de un ítem.

== CMD_USAGE_PARTIAL_UNDOCHECKOUT ==
Sintaxis:

    cm partial undocheckout | unco <ruta>+ [--silent] [--keepchanges | -k]

    ruta                Ítems sobre los que aplicar la operación, separados por
                        espacios. 
                        Usa comillas (") para especificar rutas con espacios.
                        Usa . para aplicar la operación al directorio actual.

Opciones:

    --silent            No muestra ninguna salida.
    --keepchanges (-k)  Deshace la desprotección dejando los cambios locales.
                        Ejemplo: un checkout lo deja como changed con el 
                        contenido en disco que tenia antes de hacer el unco.
                        Esta opción no puede utilizarse con workspaces dinámicos.

== CMD_HELP_PARTIAL_UNDOCHECKOUT ==
Notas:

    Si un ítem está desprotegido y no quieres protegerlo, puede deshacer la 
    desprotección usando este comando. 
    Se puede deshacer la desprotección tanto a ficheros como a carpetas.
    El ítem se actualizará al estado anterior previo a ser desprotegido.

    Requisitos: 
      - El ítem debe estar controlado.
      - El ítem debe estar desprotegido.

Ejemplos:

    cm partial undocheckout . 
    (Deshace las desprotecciones del directorio actual.)

    cm partial undocheckout pic1.png pic2.png
    cm unco c:\workspace\diseño01.png
    (Deshace las desprotecciones de los ítems especificados.)

== CMD_DESCRIPTION_PARTIAL_UNDO ==
Deshace cambios en un workspace.

== CMD_USAGE_PARTIAL_UNDO ==
Syntaxis:

    cm partial undo [path]+ [--symlink] [-r | --recursive] [Filtros]+
                                                    [Opciones de salida]

    path                    Rutas de los ficheros o carpetas a los que aplicar
                            la operación, separados por espacios. Se pueden
                            utilizar comillas (") para especificar rutas con
                            espacios. Si no se especifica ninguna ruta, por
                            defecto la operación se ejecutará sobre los ficheros
                            en el directorio actual.

Opciones:

    --symlink               Aplica la operación de undo al enlace simbólico y
                            no a su destino.
    -r                      Ejecuta el undo de manera recursiva.

Filtros:

    Si ninguna de estas opciones está presente, por defecto se deshacen todos
    los tipos de cambio, pero los ficheros pueden filtrarse utilizando estas
    opciones.
    Si un fichero o directorio tiene uno o más de los tipos de cambio
    especificados, todos los cambios en ese fichero o directorio serán
    deshechos.
    Por ejemplo, si se especifica tanto --checkedout como --moved, si un fichero
    está tanto movido como checkedout, ambos cambios se desharán.

    --checkedout            Selecciona ficheros y directorios marcados para
                            cambios.
    --unchanged             Selecciona ficheros sin modificaciones en su
                            contenido.
    --changed               Selecciona ficheros y directorios cambiados o
                            marcados para cambios.
    --deleted               Selecciona ficheros y directorios eliminados.
    --moved                 Selecciona ficheros y directorios movidos.
    --added                 Selecciona ficheros y directorios añadidos.

Opciones de salida:

    --silent | --machinereadable [--startlineseparator=sep]
                                [--endlineseparator=sep] [--fieldseparator=sep]

    --silent                No muestra ninguna salida.
    --machinereadable       Muestra la salida en un formato fácil de tratar.
    --startlineseparator    Usado en conjunto con la opción '--machinereadable',
                            especifica cómo deberían comenzar las líneas.
    --endlineseparator      Usado en conjunto con la opción '--machinereadable',
                            especifica cómo deberían terminar las líneas.
    --fieldseparator        Usado en conjunto con la opción '--machinereadable',
                            especifica cómo deberían separarse las líneas.

== CMD_HELP_PARTIAL_UNDO ==
Notas:

    El comand undo es peligroso - deshace trabajo de una manera irreversible.
    Una vez que el undo termina, no hay manera posible de recuperar el estado
    anterior de los ficheros y directorios afectados. Si no se especifica
    ninguna ruta en los argumentos, por defecto se desharán los cambios en todos
    los elementos del directorio actual, pero NO de manera recursiva.
    Estos comandos son equivalentes ejecutados desde el directorio /src:

        /src
        |- file.txt
        |- code.cs
        \- /test
           |- test_a.py
           \- test_b.py

        $ cm partial undo
        $ cm partial undo *
        $ cm partial undo file.txt code.cs /test

        $ cm partial undo .
        $ cm partial undo /src file.txt code.cs

    Si se quiere que la operación sea recursiva, se necesita especificar la
    opción -r.

    Para deshacer todos los cambios debajo de un directorio (incluyendo los
    cambios sobre el propio directorio):

        $ cm partial undo dirpath -r

    Si dirpath es la ruta de un workspace, todos los cambios dentro del
    workspace serán deshechos.

Examples:

    $ cm partial undo . -r
    (Deshace todos los cambios en el directorio actual de manera recursiva. Si
    se ejecuta desde la raíz del workspace, deshace todos los cambios en el
    workspace completo.)

    $ cm partial co file.txt
    $ cm partial undo file.txt
    (Deshace el checkout en file.txt.)

    $ echo content >> file.txt
    $ cm partial undo file.txt
    (Deshace el cambio local en file.txt.)

    $ cm partial undo src
    (Deshace los cambios en el directorio src y en todos los ficheros
    controlados que contenga)

    $ cm partial undo src/*
    (Deshace los cambios en todos los elementos contenidos en src, sin afectar
    al propio directorio. Por la expansión del wildcard, es equivalente a
    'cm partial undo src/file1.txt src/file2.txt').

    $ cm partial undo *.cs
    (Deshace cambios en cada elemento que encaje con el patrón *.cs en el
    directorio actual.)

    $ cm partial undo *.cs -r
    (Deshace cambios en cada elemento que encaje con el patrón *.cs en el
    directorio actual, y en cada directorio por debajo de una manera recursiva.)

    $ cm partial co file1.txt file2.txt
    $ echo content >> file1.txt
    $ cm partial undo --unchanged
    (Deshace el checkout en file2.txt por no estar modificado, ignorando
    file1.txt ya que tiene cambios locales.)

    $ echo content >> file1.txt
    $ echo content >> file2.txt
    $ cm partial co file1.txt
    $ cm partial undo --checkedout
    (Deshace el cambio en el fichero en checkout file1.txt, ignorando file2.txt
    ya que no está en checkout.)

    $ cm partial add file.txt
    $ cm partial undo file.txt
    (Deshace el añadido de file.txt, dejándolo de nuevo como privado.)

    $ rm file1.txt
    $ echo content >> file2.txt
    $ cm partial add file3.txt
    $ cm partial undo --deleted --added *
    (Deshace el borrado de file1.txt y el añadido de file3.txt, ignorando
    el cambio en file2.txt.)

== CMD_DESCRIPTION_PARTIAL_UPDATE ==
Actualiza el espacio de trabajo parcial.

== CMD_USAGE_PARTIAL_UPDATE ==
Sintaxis:

    cm partial update [<ruta>+]
                      [--changeset=número]
                      [--silent | --report] [--dontmerge]
                      [--xml[=<output_file>]] [--encoding=<name>]
                      [--machinereadable [--startlineseparator=<sep>]
                        [--endlineseparator=<sep>] [--fieldseparator=<sep>]]

    ruta            Rutas a actualizar, separadas por espacios. Las dobles 
                    comillas (") deben usarse para especificar rutas que 
                    contienen espacios.
                    Si se usa . entonces se actualiza el directorio actual.
                    Si no se especifica ninguna ruta, entonces se actualiza todo
                    el espacio de trabajo parcial.

Opciones:

    --changeset           Actualiza el espacio de trabajo parcial al changeset 
                          especificado. Esta opción no tiene efecto si se utiliza
                          la opción --incoming.
    --silent              No muestra salida a no ser que ocurra un error.
    --xml                 Imprime por defecto a la salida estándar en formato 
                          XML. Se permite especificar un fichero de salida.
    --encoding            Se usa junto con la opción '--xml' y especifica qué formato
                          de encoding se va a usar en la salida de XML, p.e.: utf-8.
                          Se puede consultar información sobre posibles encodings aquí:
                          http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                          (tabla "List of encodings", columna "Name").
                          Por defecto, se asigna "utf-8".
    --report              Imprime una lista de los cambios efectuados una vez que
                          termina la operación. Esta opción no tiene efecto si se 
                          utiliza la opción --silent.
    --dontmerge           No procesa los conflictos de fichero. El resto de cambios
                          se procesan con normalidad. Esta opción puede ser útil en
                          automatización para evitar interacción con el usuario.
    --incoming            Ejecuta la operación de Incoming Changes aplicando sólo los
                          cambios locales involucrados en la operación.
    --machinereadable     Muestra el resultado en un formato fácil de parsear.
    --startlineseparator  Usado con '--machinereadable', indica cómo deben
                          empezar las líneas del resultado.
    --endlineseparator    Usado con '--machinereadable', indica cómo deben
                          terminar las líneas del resultado.
    --fieldseparator      Usado con '--machinereadable', indica cómo deben
                          separarse los campos de cada línea resultante.


== CMD_HELP_PARTIAL_UPDATE ==
Notas:

    El comando update actualiza los ficheros desactualizados.

    El comando update siempre asume recursividad.

    Si todas las rutas especificadas son ficheros dentro del mismo xlink y
    se usa la opción --changeset, entonces las versiones a actualizar se buscan
    dentro del changeset del repositorio del xlink.

Ejemplos:

    cm partial update
    (Actualiza todo el espacio de trabajo parcial.)

    cm partial update .
    (Actualiza todos los hijos del directorio actual.)

    cm partial update fichero1.txt
    (Actualiza el fichero fichero1.txt.)

    cm partial update fichero1.txt fichero2.txt
    (Actualiza los ficheros fichero1.txt y fichero2.txt.)

    cm partial update src --report
    (Actualiza todos los hijos del directorio src, mostrando una lista de los
    cambios aplicados al finalizar.)

    cm partial update src --changeset=4
    (Actualiza todos los hijos del directorio src, al contenido
    que cargaban en el changeset 4.)
    
    cm partial update xlink/first.png --changeset=4
    (Actualiza el fichero xlink/first.png al contenido que cargaba en el
    changeset 4 del repositorio del xlink.)

    cm partial update . --changeset=2 --xml=output.xml 2>errors.txt 
    (Actualiza todos los hijos del directorio actual al contenido que cargaba en el
    changeset 2 del repositorio e informa del resultado en formato XML. La salida 
    la almacena en un archivo output.xml y los potenciales errores los redirige 
    al archivo errors.txt.
    NOTA: la redirección puede variar dependiendo de la consola empleada.)

== CMD_DESCRIPTION_PATCH ==
Genera un parche a partir de una spec o aplica un parche generado en el workspace actual.

== CMD_USAGE_PATCH ==
Sintaxis:

    cm patch [source_spec] source_spec
                        [--output=output_file] [--tool=/path/to/diff]

    cm patch --apply patch_file [--tool=/path/to/patch]

    source_spec         Especificación de rama o changeset
    output_file         Fichero que contendrá la salida del comando. Se
                        mostrará en pantalla si no se especifica.

Opciones:

    --output            Indica el fichero de destino para almacenar los
                        contenidos del parche.
    --tool              Especifica la ruta hacia la herramienta necesaria para
                        ejecutar la operación (diff o patch).

== CMD_HELP_PATCH ==
Notas:

    Este comando genera un fichero patch que contiene las diferencias de una
    rama o un changeset, así como las diferencias entre changesets. Encuentra
    diferencias para ficheros tanto de texto como binarios.

    El parámetro --apply permite aplicar los contenidos de un fichero patch 
    en el workspace actual.

Limitaciones:

    Si el fichero de salida de patch ya existe, el comando no lo
    sobreescribirá.

    Si el fichero con modificaciones en el patch no existe, el comando no
    creará uno nuevo para aplicar los cambios.

Importante:

    Este comando requiere las utilidades Diff y Patch, disponibles
    en http://gnuwin32.sourceforge.net/packages/patch.htm y 
    http://gnuwin32.sourceforge.net/packages/diffutils.htm
    
    Tras su instalación se recomienda agregar su ubicación a la variable
    de entorno PATH.

Ejemplos:

    cm patch cs:4@default@localhost:8084
    (Muestra en pantalla las diferencias del changeset 4 en formato patch.)

    cm patch br:/main --output=file.patch
    (Obtiene un fichero file.patch con las diferencias de la rama "main".)

    cm patch br:/main --output=file.patch --tool=C:\gnu\diff.exe
    (Misma operación con un ejecutable indicado explícitamente.)
    
    cm patch cs:2@default cs:4@default
    (Muestra en pantalla las diferencias entre los changeset 2 y 4.)

    cm patch --apply file.patch --tool=C:\gnu\patch.exe
    (Aplica el parche contenido en file.patch al workspace actual con un
    ejecutable indicado explícitamente.)

== CMD_DESCRIPTION_PROFILE ==
Permite al usuario administrar perfiles de conexión configurados en el cliente.

== CMD_USAGE_PROFILE ==
Sintaxis:

    cm profile | profile <command> [options]

Comandos:

    list   | ls
    create | mk
    delete | rm

    Para obtener más información sobre cada comando:
    cm profile <command> --usage
    cm profile <command> --help

== CMD_HELP_PROFILE ==
Ejemplos:

    cm profile
    cm profile list
    cm profile create
    cm profile delete 1

== CMD_DESCRIPTION_PROFILE_LIST ==
Lista los perfiles de conexión configurados en el cliente.

== CMD_USAGE_PROFILE_LIST ==
Sintaxis:

    cm profile [list | ls] [--format=<str_format>]

Opciones:

    --format        Muestra el mensaje de salida en el formato indicado.
                    Consulte las notas para más información.

== CMD_HELP_PROFILE_LIST ==
Notas:

    Parámetros de formato de salida (opción --format):
        Este comando acepta una cadena de formato para mostrar la salida.
        Los parámetros de salida de este comando son los siguientes:
        {index}          | {0}       Índice del perfil dentro de la lista
        {name}           | {1}       Nombre del perfil
        {server}         | {2}       Servidor del perfil
        {user}           | {3}       Usuario del perfil
        {workingmode}    | {4}       Modo de trabajo del perfil
        {securityconfig} | {5}       Configuración de seguridad del perfil
        {tab}                        Inserta un tabulador
        {newline}                    Inserta una nueva línea

Ejemplos:

    cm profile
    (Lista todos los perfiles usando el formato por defecto.)

    cm profile --format="{index,2}  {server,-20}"
    (Muestra el índice del perfil utilizando 2 espacios y alineado a la derecha,
    dos espacios, y el servidor de repositorios utilizando veinte espacios,
    alineado a la izquierda.)

    cm profile --format="{0,2}  {2,-20}"
    (Muestra el mismo resultado que el ejemplo anterior.)

== CMD_DESCRIPTION_PROFILE_CREATE ==
Crea un nuevo perfil de conexión.

== CMD_USAGE_PROFILE_CREATE ==
Sintaxis:

    cm profile [create | mk]
     (Crea un nuevo perfil de conexión de manera interactiva.)

    cm profile [create | mk] --server=<server_addr> --username=<username>
                      --password=<password> --workingmode=<workingmode>
     (Crea un nuevo perfil de conexión usando un modo de autenticación basado
     en usuario y contraseña.)

    cm profile [create | mk] --server=<server_addr> --username=<username>
                      --token=<token> --workingmode=SSOWorkingMode
     (Crea un nuevo perfil de conexión usando un modo de autenticación basado
     en Single Sign On.)

Opciones:

    --server        Crea el perfil de conexión para el servidor especificado.
    --username      El nombre de usuario que va a ser utilizado en el perfil de
                    conexión.
    --password      La contraseña, en texto plano, que será usada en el perfil
                    de conexión. Esta opción solo es válida para los modos de
                    autenticación basados en usuario y contraseña.
    --token         El token, en texto plano, que será usado en el perfil de
                    conexión. Esta opción solo es válida para los modos de
                    autenticación basados en token (por ahora solo SSOWorkingMode).
    --workingmode   El modo de autenticación del servidor.
                    Modos de autenticación disponibles:
                    LDAPWorkingMode (LDAP)
                    UPWorkingMode (User and password)
                    SSOWorkingMode (Single Sign On)

== CMD_HELP_PROFILE_CREATE ==
Notas:

    Cuando se utiliza este comando en modo interactivo (sin opciones) el cliente
    trata de conectarse con el servidor para obtener el modo de autenticación y
    comprobar las credenciales. Esto garantiza que el perfil de conexión
    resultante sea correcto.

    Cuando se utiliza este comando con opciones, el cliente genera el perfil de
    conexión sin conectar con el servidor. Esto es útil para crear perfiles de
    conexión para su uso en automatización.

Ejemplos:

    cm profile create
    (Crea un perfil de conexión de manera interactiva.)

    cm profile create --server=plastic.domain.com:8087 --username=sergio
      --password=thisissupersecret --workingmode=LDAPWorkingMode
    (Crea un perfil de conexión contra 'plastic.domain.com:8087' con el usuario
    'sergio' y la contraseña 'thisissupersecret' usando el modo de autenticación
    LDAP.)

    cm profile mk --server=plastic.domain.com:8087 --username=sergio
      --token="TOKENAMoKJ9iAA(...)12fssoprov:unityid"
    (Crea un perfil de conexión contra 'plastic.domain.com:8087' con el usuario
    'sergio' y el token especificado usando el modo de autenticación Single Sign
    On.)

== CMD_DESCRIPTION_PROFILE_DELETE ==
Borra un perfil de conexión de la configuración del cliente.

== CMD_USAGE_PROFILE_DELETE ==
Sintaxis:

    cm profile delete | rm <index | name>
    cm profile delete | rm --index=<index>
    cm profile delete | rm --name=<name>

    index       Índice del perfil en la lista de perfiles de conexión.
    name        Nombre del perfil.

Opciones:

    --index     Utilizado para desambiguación en caso de que un perfil tenga un
                número por nombre.
    --name      Utilizado para desambiguación en caso de que un perfil tenga un
                número por nombre.

== CMD_HELP_PROFILE_DELETE ==
Notas:

    Borra un perfil de conexión de la configuración del cliente.
    Funciona especificando tanto el índice del perfil como su nombre.
    El comando 'cm profile list' no muestra el nombre del perfil por defecto,
    use 'cm profile list --help' para saber cómo mostrar el nombre del perfil.

Ejemplos:

    cm profile delete 1
    (Borra el perfil con índice 1)

    cm profile delete ***********:8087_UPWorkingMode
    (Borra el perfil con nombre '***********:8087_UPWorkingMode'.)

    cm profile delete --name=12
    (Borra el perfil con nombre '12'.)

== CMD_DESCRIPTION_QUERY ==
Ejecuta una consulta SQL contra la base de datos del servidor.

== CMD_USAGE_QUERY ==
Uso:

    cm query comandosql [--repository=<name>]

    --repository: repositorio para consultar.

== CMD_HELP_QUERY ==
Notas:

    Este comando permite a los usuarios ejecutar consultas SQL directamente
    contra la base de datos del servidor.

Además, a la hora de escribir consultas, existen dos functiones predefinidas
para trabajar con usuarios y rutas de disco.
Estas funciones son las siguientes:

* SolveUser(nombre_usuario), resuelve el nombre de usuario dado al formato que
maneja Unity VCS.
* SolvePath(path), resuelve rutas de disco a identificadores de ítem itemid.

== CMD_DESCRIPTION_ATTRIBUTE_DELETE ==
Borra uno o más atributos.

== CMD_USAGE_ATTRIBUTE_DELETE ==
Sintaxis:

    cm attribute | att delete | rm <att_spec>[ ...]

    att_spec            Atributos a borrar. Use un espacio en blanco para separar
                        atributos.
                        (Use 'cm help objectspec' para más información sobre
                        las especificaciones de atributos.)

== CMD_HELP_ATTRIBUTE_DELETE ==
Notas:

   Este comando borra uno o más atributos.

Ejemplos:

    cm attribute delete att:status
    (Borra el atributo 'status'.)

    cm att rm status att:integrated@reptest@server2:8084
    (Borra los atributos 'status' e 'integrated'.)

== CMD_DESCRIPTION_ATTRIBUTE_UNSET ==
Desasigna un atributo a un objeto.

== CMD_USAGE_ATTRIBUTE_UNSET ==
Sintaxis:

    cm attribute | att unset <att_spec> <object_spec>

    att_spec            Especificación del atributo. (Use 'cm help objectspec'
                        para más información sobre las especificaciones de
                        atributos.)
    object_spec         Especificación del objeto sobre el que desasignar el
                        atributo. Se pueden fijar atributos a ramas, changesets,
                        shelvesets, etiquetas, items y revisiones.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones.)

== CMD_HELP_ATTRIBUTE_UNSET ==
Notas:

    Este comando elimina la asignación de un atributo que ha sido aplicado
    previamente a un objeto. Este comando no borra el atributo en sí.

Ejemplo:

    cm attribute unset att:status br:/main/SCM105
    (Desasigna el atributo 'status' que estaba aplicado a la rama 'main/SCM105'.)

    cm att unset att:integrated@reptest@localhost:8084 cs:25@reptest@localhost:8084
    (Desasigna el atributo 'integrated' que estaba aplicado al changeset 25.
    Tanto el atributo como el changeset se encuentran en el repositorio 'reptest'.)

== CMD_DESCRIPTION_ATTRIBUTE_RENAME ==
Renombra un atributo.

== CMD_USAGE_ATTRIBUTE_RENAME ==
Sintaxis:

    cm attribute | att rename <att_spec> <new_name>

    att_spec            Atributo a renombrar. (Use 'cm help objectspec' para
                        más información sobre especificaciones de atributos.)
    new_name            Nuevo nombre para el atributo.

== CMD_HELP_ATTRIBUTE_RENAME ==
Notas:

   Este comando renombra un atributo.

Ejemplos:

   cm attribute rename att:status state
   (Renombra el atributo 'status' a 'state')

== CMD_DESCRIPTION_ATTRIBUTE_EDIT ==
Edita el comentario de un atributo.

== CMD_USAGE_ATTRIBUTE_EDIT ==
Sintaxis:

    cm attribute | att edit <att_spec> <new_comment>

    att_spec        Atributo al cual editar su comentario.
                    (Use 'cm help objectspec' para más información sobre
                    especificaciones de atributos.)
    new_comment     Nuevo comentario para el atributo. Este comentario puede ser
                    una lista de posibles valores que el usuario puede seleccionar
                    cuando asigne un atributo a un objeto. Consulte las Notas
                    para más información.

== CMD_HELP_ATTRIBUTE_EDIT ==
Notas:

    Este comando cambia el comentario de un atributo.

    Para especificar la lista de valores por defecto para un atributo, indique
    un texto como este en el comentario del atributo:
    'default: valor_uno, "valor dos", valor3, "Valor final"'.

Ejemplos:

    cm attribute edit att:status "The status of a branch in the CI pipeline."
    (Edita el comentario del atributo 'status'.)

    cm attribute edit att:status "Status of a branch. default: open, resolved, reviewed"
    (Edita el comentario del atributo 'status'. Y también especifica una lista
    de valores. De este modo, cuando el usuario asigne el atribute 'status' a un
    objeto, podrá seleccionar uno de los siguientes valores: 'open', 'resolved'
    o 'reviewed'.)

== CMD_DESCRIPTION_REPLICATE ==
ATENCIÓN: Este comando se ha declarado obsoleto.

Ha sido reemplazado por 'pull' (equivalente a 'replicate') y por 'push'
(equivalente a 'replicate --push').

Replica datos desde un repositorio remoto.

== CMD_USAGE_REPLICATE ==
Uso:

    cm replicate br_spec dst_rep_spec [--nodata] [translateOptions] [authOptions]
    cm replicate hydrate br_spec [src_rep_spec] [authOptions]
    cm replicate hydrate cs_spec [src_rep_spec] [authOptions]
    cm replicate br_spec --package=packagename [--changeset=número]
    cm replicate dst_rep_spec --import=packagename

    br_spec: la rama para ser replicada/hidratada. Es una especificación
    completa de rama con el siguiente formato:
      br:/NOMBRE_RAMA[@rep:NOMBRE_REP[@repserver:NOMBRE_SERVIDOR:puerto]]

    cs_spec: changeset para ser hidratado. Es una espec. de changeset:
        cs:NUMERO_CHANGESET[@rep:NOMBRE_REP[@repserver:NOMBRE_SERVIDOR:puerto]]

    src_rep_sec: repositorio origen de los datos. Es una especificación
    completa de repositorio:
      rep:NOMBRE_REPOSITORIO@repserver:NOMBRE_SERVIDOR:puerto

    dst_rep_sec: repositorio destino de la replica. Es una especificación
    completa de repositorio:
      rep:NOMBRE_REPOSITORIO@repserver:NOMBRE_SERVIDOR:puerto

Opciones:

    --package para crear un paquete de replicación.
    --import para importar un paquete concreto.
    --nodata para replicar los cambios de una rama sin replicar sus datos. Esta
        opción no se puede usar para la réplica con paquetes ni con la opción
        --push.

Opciones de traducción:

    --trmode=[copy | name | table] para especificar cómo tratar los
        nombres de usuario cuando se importen los datos en el
        repositorio de destino:
        * copy  -> es el utilizado por defecto. Copia los SEIDs en el
          repositorio de destino.
        * name  -> realiza una traducción por nombre entre el modo
          de autentificación de origen y el de destino.
        * table -> se usará una tabla de traducción en la que se
          especifican los nombres de origen y cómo han de quedar
          en el destino.

    --trtable=[translationTable]
        una tabla de traducción es un fichero que contiene entradas
        en la forma nombre antiguo;nombre nuevo.

Opciones de autentificación:

    Hay dos formas de especificar los datos de autentificación:

    1) Especificando un fichero de autentificación.

       --authfile=fichero, un fichero que contiene dos líneas: el modo de
           autentificación en la primera línea y los datos de autentificación
           (ver --authdata) o una cadena vacía en la segunda.

    2) Especificando la autentificación mediante parámetros.

       Para ello hay que especificar el modo de autentificación mediante el
       modificador:

       --authmode=[NameWorkingMode | NameIDWorkingMode |
            LDAPWorkingMode | ADWorkingMode | UPWorkingMode]

        Si está trabajando en UPWorkingMode o LDAPWorkingMode, puede
        especificar los datos de autentificación de dos formas:

        2.1) Para UPWorkingMode o LDAPWorkingMode:

            --authdata=datos_de_autentificación

            Ejemplos:
              --authdata=ActiveDirectory:192.168.1.3:389:<EMAIL>:fPBea2rPsQaagEW3pKNveA==:dc=factory,dc=com (LDAPWorkingMode)
              --authdata=john:fPBea2rPsQaagEW3pKNveA== (UPWorkingMode)

        2.2) Solamente para UPWorkingMode:

             --user=usuario
             --password=contraseña

== CMD_HELP_REPLICATE ==
Notas:

    El comando 'replicate' replica datos desde una rama hacia un
    repositorio de destino. Todos los datos de la rama de origen, incluyendo
    revisiones, ítems, changesets, etiquetas, revisiones de código, links
    y seguridad serán replicados.

    Su cliente Unity VCS debe estar configurado para trabajar con el
    repositorio de destino.

    La replicación también gestiona la 'reconciliación' de ramas que han
    sido modificadas tanto en origen como en destino, mediante la creación
    de 'ramas de entrega' (fetch branches) que podrán ser integradas
    posteriormente para resolver los conflictos.

    La replicación puede funcionar en tres modos diferentes:

    1- Comunicación directa entre servidores: lo que significa que el servidor
    de destino se comunicará con el de origen para sincronizar la rama
    especificada.

    2- Generación de paquete de exportación: el cliente se conectará
    únicamente con el destino para generar un paquete de replicación con los
    datos y los metadatos de la rama especificada. Se usará el modificador
    --package. El usuario podrá especificar, opcionalmente, un changeset
    inicial desde el cual generar la replicación.

    3- Importar un paquete generado previamente, usando la opción --import.

    En los modos 1 y 2 será necesaria la autentificación contra el servidor
    de origen. El comando 'replicate' acepta parámetros para especificar los
    diferentes modos de autentificación.

Ejemplos:

    cm replicate br:/main@rep:default@repserver:LONDRES:8084 rep:myrep@repserver:MADRID:9090
    cm replicate br:/main@rep:default@repserver:LONDRES:8084 rep:myrep@repserver:MADRID:9090 --trmode=name
    cm replicate br:/main@rep:default@repserver:LONDRES:8084 rep:myrep@repserver:MADRID:9090 --trmode=table --trtable=tabla.txt

    cm replicate br:/main@rep:default@repserver:LONDRES:8084 rep:myrep@repserver:MADRID:9090 --authmode=NameWorkingMode --user=john.doe
      Significa que se autentificará contra el servidor 'MADRID' usando NameWorkingMode como usuario 'john.doe'

    cm replicate br:/main/releaseBL060@rep:blackbird@repserver:barcelona:9090 --package=replicationpackage.data
      Creará un paquete de replicación

    cm replicate rep:mine@repserver:casa:9094 --import=replicationpackage.data
      Importará el paquete creado con anterioridad

    cm replicate br:/main/releaseBL060@rep:blackbird@repserver:barcelona:9090 --package=replicationpackage.data --changeset=1230
      Generará un paquete de replicación de la rama br:/main/releaseBL060 desde el changeset 1230

    cm replicate /main@project1@LONDRES:8084 projectx@localhost:8084 --nodata
      Replicará la rama /main del servidor LONDRES a mi servidor sin replica los datos.

    cm replicate hydrate /main@projectx@localhost:8084 project1@LONDRES:8084
      Hidratará los datos de la rama /main de mi servidor, usando los datos del servidor LONDRES.

Nota adicional:
    La tabla de traducción tiene entradas (una por línea) con el siguiente
    formato:
    nombre antiguo;nombre nuevo

== CMD_DESCRIPTION_PULL ==
Replica datos desde un repositorio remoto.

== CMD_USAGE_PULL ==
Uso:

    cm pull src_br_spec dst_rep_spec
            [--preview] [--nodata] [translateOptions]
            [--user=usr_name [--password=pwd] | AuthOptions]
     (Replicación directa entre servidores. Hace pull de una rama desde un
      repositorio.)

    cm pull dst_rep_spec --package=pack_file [AuthOptions]
     (Replicación basada en paquetes. Importa el paquete en el servidor de
      destino.)

    cm pull hydrate dst_br_spec [src_rep_spec]
            [--user=usr_name [--password=pwd] | AuthOptions]
     (Introduce los datos restantes para todos los changesets de una rama
      replicada previamente con la opción --nodata. Si no se especifica un
      repositorio del que obtener los datos, Unity VCS intentará usar el
      origen de replicación original de la rama.)

    cm pull hydrate dst_cs_spec [src_rep_spec]
            [--user=usr_name [--password=pwd] | AuthOptions]
     (Introduce los datos restantes para un changeset replicado previamente
      con la opción --nodata. Si no se especifica un repositorio del que obtener
      los datos, Unity VCS intentará usar el origen de replicación original de
      la rama.)

    src_br_spec        La rama a ser replicada desde un repositorio remoto.
    dst_br_spec        La rama a ser hidratada.
                       Especificación de ramas:
                       [br:][/]nombre_rama[@[rep:]nombre_rep[@[repserver:]nombre_servidor:puerto]]
    dst_cs_spec        El changeset a ser hidratado.
                       Especificación de changesets:
                       cs:numero_cset[@[rep:]nombre_rep[@[repserver:]nombre_servidor:puerto]]
    dst_rep_spec       El repositorio de destino.
                       Especificación de repositorio:
                       [rep:]nombre_rep[@[repserver:]nombre_servidor:puerto]
    --package          El fichero de donde se importa un paquete de replicación 
                       creado anteriormente.
                       Es útil para mover datos entre servidores sin una
                       conexión de red directa.
                       Consule 'cm push' para crear paquetes de replicación.

Opciones:

    --preview           Proporciona información sobre qué cambios serán traídos
                        durante el pull, pero no se ejecuta ninguna acción más.
                        Esta opción es útil para comprobar qué datos serán
                        replicados antes de ejecutar el pull.
    --user, --password  Credenciales para utilizar en caso de que el modo de
                        autenticación de los servidores origen y destino 
                        difieran, y no exista un profile para autenticarse 
                        contra el remoto.
    --nodata            Replica los metadatos de una rama sin replicar los
                        datos. Esta opción no se puede utilizar al importar un
                        paquete de replicación.

Opciones de traducción (translateOptions):

    --trmode={copy|name|table}
        El servidor de origen y de destino pueden utilizar modos distintos de
        autenticación. Este argumento especifica cómo se han de traducir los
        nombres de usuario del origen al destino.
        - copy    El modo por defecto, indica que los nombres de usuario serán
                  simplemente copiados.
        - name    Los nombres de usuario serán emparejados entre origen y
                  destino por coincidencia.
        - table   Se utilizará una tabla de traducción (más información a
                  continuación).

    --trtable={translation_table_file}
        Si el modo de traducción es 'table', entonces una tabla de traducción
        es un fichero que contiene líneas de la forma nombreantiguo;nombrenuevo.
        Cuando la rama se escribe en el repositorio de destino, los objetos
        creados por un usuario identificado por "nombreantiguo" en el
        repositorio de origen serán asignados al usuario identificado por
        "nombrenuevo" en el repositorio de destino.

Opciones de autentificación (AuthOptions):

    Hay dos formas de especificar los datos de autentificación:

    1) Especificando la autentificación mediante parámetros:

        --authmode={NameWorkingMode|LDAPWorkingMode|ADWorkingMode|UPWorkingMode}
        (LDAPWorkingMode) --authdata=::0:dave:fPBea2rPsQaagEW3pKNveA
        (UPWorkingMode)   --authdata=dave:fPBea2rPsQaagEW3pKNveA==

        El parámetro '--authdata' es el contenido del campo <SecurityConfig>
        en los ficheros client.conf o profiles.conf. El fichero profiles.conf
        puede ser generado desde la GUI de Unity VCS (Windows).

        Si se utiliza UPWorkingMode, se puede especificar simplemente:

        --authmode=UPWorkingMode --user=user --password=pwd

        2.1) Para UPWorkingMode o LDAPWorkingMode:

            --authdata=datos_de_autentificación

            Ejemplos:
              --authdata=ActiveDirectory:192.168.1.3:389:<EMAIL>:fPBea2rPsQaagEW3pKNveA==:dc=factory,dc=com (LDAPWorkingMode)
              --authdata=john:fPBea2rPsQaagEW3pKNveA== (UPWorkingMode)

        2.2) Solamente para UPWorkingMode:

             --user=usuario
             --password=contraseña

    1) Especificando un fichero de autentificación. Se pueden tener varios
       ficheros de autenticación, uno por servidor al que se conecte,
       conteniendo las credenciales específicas para dicho servidor.

       --authfile=fichero
            Contiene 2 líneas:
            Línea 1) modo de autenticación, igual que para --authmode.
            Línea 2) datos de autenticación, igual que para --authdata.

== CMD_HELP_PULL ==
Notas:

    El comando pull replica ramas (junto a sus changesets) entre un repositorio
    origen y otro destino. Los repositorios pueden encontrarse en diferentes
    servidores.

    Hay dos operaciones de replicación: push y pull.

    Una operación de 'pull' significa que la operación de replicación hará que
    el servidor destino obtenga datos desde el origen. El cliente se conectará
    con el servidor destino, y, desde ese host, establecerá una conexión con el
    repositorio origen para recuperar los datos pedidos. Durante el pull, será
    el servidor destino el que se conecte al origen.

    Aunque en un escenario distribuido típico el desarrollador haga pùsh de los
    cambios en su servidor local al servidor central, también puede ser
    necesario hacer pull de los últimos cambios en el servidor central al
    servidor local.

    La replicación puede resolver situaciones en las que se hagan cambios
    concurrentes en la misma rama en dos repositorios replicados:

    - Push: si se intenta hacer push de datos locales a un repositorio que tenga
      datos más nuevos que los que se están enviando, el sistema pedirá hacer
      pull de los últimos cambios, resolver el merge resultante y, finalmente,
      tratar de repetir el push.

    - Pull: cuando se hace pull de changesets de una rama remota, estos estarán
      correctamente enlazados a sus changesets padre. Si el changeset que se ha
      traído no es un hijo del último changeset de la rama, entonces aparecerá
      un escenario de múltiples cabezas. La rama tendrá más de una 'cabeza', o
      dicho de otro modo, más de un changeset final de rama. Será necesario
      hacer merge de ambas cabezas antes de que se pueda hacer push de nuevo.

    El pull puede funcionar de dos modos:

    1) Comunicación directa entre servidores: lo que significa que el servidor
    de destino se comunicará con el de origen para sincronizar la rama
    especificada.

    2) Importación de un paquete de replicación generado con push, con la opción
    --package.

    El modo 1) requere que el usuario que ejecute el comando esté autenticado
    contra el servidor remoto, bien o usando la autenticación por defecto en
    el fichero client.conf, mediante un profile creado previamente, o
    especificando los argumentos --authmode y --authdata (o --user y --password
    en caso de que el modo de autenticación sea UPWorkingMode).

    El modo 2) require usar un paquete de replicación generado previamente con
    el comando push.

    Recuerde que la replicación pull funciona de una manera indirecta. Cuando
    se ejecuta, el comando pide al repositorio destino que se conecte con el
    origen para obtener la rama especificada.

    Sin embargo, esto se puede hacer de manera directa utilizando el comando
    push, que hará que sea el repositorio origen quien envíe los datos
    directamente al destino.

Ejemplos:

    cm pull br:/main@project1@remoteserver:8084 projectx@myserver:8084
    (Hace pull de la rama principal de remoteserver a myserver. En este caso,
    ambos servidores están configurados con el mismo modo de autenticación.)

    cm pull br:/main@project1@remoteserver:8084 projectx@myserver:8084 --authmode=LDAPWorkingMode --authdata=::0:dave:fPBea2rPsQaagEW3pKNveA
    (Hace pull de la misma rama que antes, pero ahora el servidor remoto está
    configurado para autenticar usuarios contra un Active Directory. Por ejemplo,
    se puede conectar de una máquina Linux a un servidor Windows configurado
    para usar Active Directory. Para ello, se especificará el usuario y la
    contraseña cifrada.)

    cm pull br:/main@project1@remoteserver:8084 projectx@myserver:8084 --authmode=UPWorkingMode --user=dave --password=mysecret
    (Hace pull de la misma rama, pero ahora ambos usuarios están autenticados
    en el servidor remoto mediante el sistema de gestión de usuarios integrado
    en Unity VCS.)

    cm pull br:/main@project1@remoteserver:8084 projectx@myserver:8084 --nodata
    (Hace pull de la rama principal de remoteserver a myserver pero sin datos.)

    cm pull hydrate br:/main@projectx@myserver:8084 projectx@remoteserver:8084
    (Introduce los datos de todos los changesets de la rama principal obteniendo
    dichos datos del repositorio en remoteserver.)

    cm pull hydrate cs:122169@projectx@myserver:8084 projectx@remoteserver:8084
    (Introduce los datos del changeset 122169 en myserver obteniendo dichos
    datos del repositorio en remoteserver.)

Nota adicional:
    La tabla de traducción tiene entradas (una por línea) de la forma:
        nombre antiguo;nombre nuevo

== CMD_DESCRIPTION_PUSH ==
Replica datos hacia un repositorio remoto.

== CMD_USAGE_PUSH ==
Uso:

    cm push src_br_spec dst_rep_spec
            [--preview] [translateOptions]
            [--user=usr_name [--password=pwd] | AuthOptions]
     (Replicación directa entre servidores. Hace push de una rama hacia un
      repositorio.)

    cm push src_br_spec --package=pack_file [AuthOptions]
     (Replicación basada en paquetes. Crea un paquete de replicación con la
      rama especificada.)

    src_br_spec        La rama a ser replicada desde un repositorio remoto.
                       Especificación de ramas:
                       [br:][/]nombre_rama[@[rep:]nombre_rep[@[repserver:]nombre_servidor:puerto]]
    dst_cs_spec        El changeset a ser hidratado.
                       Especificación de changesets:
                       cs:numero_cset[@[rep:]nombre_rep[@[repserver:]nombre_servidor:puerto]]
    dst_rep_spec       El repositorio de destino.
                       Especificación de repositorio:
                       [rep:]nombre_rep[@[repserver:]nombre_servidor:puerto]
    --package          Exporta un paquete de replicación al fichero especificado.
                       Es útil para mover datos entre servidores sin una
                       conexión de red directa.

Opciones:

    --preview           Proporciona información sobre qué cambios serán enviados
                        durante el push, pero no se ejecuta ninguna acción más.
                        Esta opción es útil para comprobar qué datos serán
                        replicados antes de ejecutar el push.
    --user, --password  Credenciales para utilizar en caso de que el modo de
                        autenticación de los servidores origen y destino 
                        difieran, y no exista un profile para autenticarse 
                        contra el remoto.
    --nodata            Replica los metadatos de una rama sin replicar los
                        datos. Esta opción no se puede utilizar al exportar un
                        paquete de replicación.

Opciones de traducción (translateOptions):

    --trmode={copy|name|table}
        El servidor de origen y de destino pueden utilizar modos distintos de
        autenticación. Este argumento especifica cómo se han de traducir los
        nombres de usuario del origen al destino.
        - copy    El modo por defecto, indica que los nombres de usuario serán
                  simplemente copiados.
        - name    Los nombres de usuario serán emparejados entre origen y
                  destino por coincidencia.
        - table   Se utilizará una tabla de traducción (más información a
                  continuación).

    --trtable={translation_table_file}
        Si el modo de traducción es 'table', entonces una tabla de traducción
        es un fichero que contiene líneas de la forma nombreantiguo;nombrenuevo.
        Cuando la rama se escribe en el repositorio de destino, los objetos
        creados por un usuario identificado por "nombreantiguo" en el
        repositorio de origen serán asignados al usuario identificado por
        "nombrenuevo" en el repositorio de destino.

Opciones de autentificación (AuthOptions):

    Hay dos formas de especificar los datos de autentificación:

    1) Especificando la autentificación mediante parámetros:

        --authmode={NameWorkingMode|LDAPWorkingMode|ADWorkingMode|UPWorkingMode}
        (LDAPWorkingMode) --authdata=::0:dave:fPBea2rPsQaagEW3pKNveA
        (UPWorkingMode)   --authdata=dave:fPBea2rPsQaagEW3pKNveA==

        El parámetro '--authdata' es el contenido del campo <SecurityConfig>
        en los ficheros client.conf o profiles.conf. El fichero profiles.conf
        puede ser generado desde la GUI de Unity VCS (Windows).

        Si se utiliza UPWorkingMode, se puede especificar simplemente:

        --authmode=UPWorkingMode --user=user --password=pwd

        2.1) Para UPWorkingMode o LDAPWorkingMode:

            --authdata=datos_de_autentificación

            Ejemplos:
              --authdata=ActiveDirectory:192.168.1.3:389:<EMAIL>:fPBea2rPsQaagEW3pKNveA==:dc=factory,dc=com (LDAPWorkingMode)
              --authdata=john:fPBea2rPsQaagEW3pKNveA== (UPWorkingMode)

        2.2) Solamente para UPWorkingMode:

             --user=usuario
             --password=contraseña

    1) Especificando un fichero de autentificación. Se pueden tener varios
       ficheros de autenticación, uno por servidor al que se conecte,
       conteniendo las credenciales específicas para dicho servidor.

       --authfile=fichero
            Contiene 2 líneas:
            Línea 1) modo de autenticación, igual que para --authmode.
            Línea 2) datos de autenticación, igual que para --authdata.

== CMD_HELP_PUSH ==
Notas:

    El comando pull replica ramas (junto a sus changesets) entre un repositorio
    origen y otro destino. Los repositorios pueden encontrarse en diferentes
    servidores.

    Hay dos operaciones de replicación: push y pull.

    Una operación de 'push' significa que la operación de replicación enviará
    datos desde el repositorio origen al repositorio destino. En este caso, el
    cliente se conectará al repositorio origen, recuperando los datos para ser
    replicados, y entonces los enviará al repositorio destino. Mientras que el
    origen necesita tener conectividad con el destino, el destino no se
    conectará con el origen.

    En un escenario distribuido típico el desarrollador hace push de los
    cambios en su servidor local al servidor central. También puede ser
    necesario hacer pull de los últimos cambios en el servidor central al
    servidor local.

    La replicación puede resolver situaciones en las que se hagan cambios
    concurrentes en la misma rama en dos repositorios replicados:

    - Push: si se intenta hacer push de datos locales a un repositorio que tenga
      datos más nuevos que los que se están enviando, el sistema pedirá hacer
      pull de los últimos cambios, resolver el merge resultante y, finalmente,
      tratar de repetir el push.

    - Pull: cuando se hace pull de changesets de una rama remota, estos estarán
      correctamente enlazados a sus changesets padre. Si el changeset que se ha
      traído no es un hijo del último changeset de la rama, entonces aparecerá
      un escenario de múltiples cabezas. La rama tendrá más de una 'cabeza', o
      dicho de otro modo, más de un changeset final de rama. Será necesario
      hacer merge de ambas cabezas antes de que se pueda hacer push de nuevo.

    El push puede funcionar de dos modos:

    1) Comunicación directa entre servidores: lo que significa que el servidor
    de origen se comunicará con el de destino para sincronizar la rama
    especificada.

    2) Exportación de un paquete de replicación: El cliente únicamente se
    conectará con el origen y generará un paquete de replicación que contenga
    tanto los datos como los metadatos para la rama especificada. Para ello
    se utilizará el argumento --package.

    Ambos modos requeren que el usuario ejecutando el comando esté autenticado
    contra el servidor, bien o usando la autenticación por defecto en el fichero
    client.conf, mediante un profile creado previamente, o especificando los
    argumentos --authmode y --authdata (o --user y --password en caso de que
    el modo de autenticación sea UPWorkingMode).

    La replicación push funciona de una manera directa. Cuando se ejecuta, el
    comando replicará la rama seleccionada del origen al destino, en vez de
    pedir al repositorio destino que se conecte al repositorio origen para
    obtener la rama especificada (como hace pull).

Ejemplos:

    cm push br:/main@project1@myserver:8084 projectx@remoteserver:8084
    (Hace push de la rama main desde myserver a remoteserver. En este caso,
    ambos servidores están configurados con el mismo modo de autenticación.)

    cm push br:/main@project1@remoteserver:8084 projectx@myserver:8084 --authmode=LDAPWorkingMode --authdata=::0:dave:fPBea2rPsQaagEW3pKNveA
    (Hace push de la misma rama que antes, pero ahora el servidor remoto está
    configurado para autenticar usuarios contra un Active Directory. Por ejemplo,
    se puede conectar de una máquina Linux a un servidor Windows configurado
    para usar Active Directory. Para ello, se especificará el usuario y la
    contraseña cifrada.)

    cm push br:/main@project1@remoteserver:8084 projectx@myserver:8084 --authmode=UPWorkingMode --user=dave --password=mysecret
    (Hace push de la misma rama, pero ahora ambos usuarios están autenticados
    en el servidor remoto mediante el sistema de gestión de usuarios integrado
    en Unity VCS.)

Nota adicional:
    La tabla de traducción tiene entradas (una por línea) de la forma:
        nombre antiguo;nombre nuevo

== CMD_DESCRIPTION_CLONE ==
Clona un repositorio remoto.

== CMD_USAGE_CLONE ==
Sintaxis:

    cm clone <src_rep_spec> [<dst_rep_spec> | <dst_repserver_spec>]
                [--user=<usr_name> [--password=<pwd>] | AuthOptions]
                [TranslateOptions]
     (Clonado directo repositorio-a-repositorio.)

    cm clone <src_rep_spec> --package=<pack_file>
                [--user=<usr_name> [--password=<pwd>] | AuthOptions]
     (Clonado a un paquete intermedio que se podrá importar posteriormente al
     repositorio destino mediante un pull.)

    src_rep_spec        Repositorio origen de la operación de clonado.
                        (Use 'cm help objectspec' para más información sobre
                        las especificaciones de repositorio.)
    dst_rep_spec        Repositorio destino de la operación de clonado. Si
                        existe, debe estar vacío. Si no existe, se creará.
                        Si no se especifica, el comando utilizará el servidor
                        de repositorios por defecto.
                        (Use 'cm help objectspec' para más información sobre
                        las especificaciones de repositorios.
    dst_repserver_spec  Servidor de repositorios destino de la operación de
                        clonado. Si existe un repositorio con el mismo nombre
                        que <src_rep_spec>, debe estar vacío. Si no existe, se
                        creará.
                        Si no se especifica, el comando utilizará el servidor
                        de repositorios por defecto.
                        (Use 'cm help objectspec' para más información sobre
                        las especificaciones de servidor de repositorio.)

Options:

    --user, --password  Credenciales para utilizar en caso de que el modo de
                        autenticación de los servidores origen y destino
                        difieran, y no exista un perfil para autentificarse
                        en el destino.
    --package           Exporta el repositorio indicado a un fichero de paquete
                        en lugar de a otro repositorio.
                        Es útil para mover datos entre servidores sin una
                        conexión de red directa.
                        El paquete resultante se importará mediante el comando
                        pull.
    TranslateOptions    Consulte las opciones de traducción para más información.
    AuthOptions         Consulte las opciones de autentificación para más
                        información.

Opciones de traducción (TranslateOptions):

    --trmode=(copy|name|table --trtable=<translation_table_file>)
      Los repositorios origen y destino pueden utilizar distintos modos de
        autenticación. La opción --trmode especifica cómo se han de traducir los
        nombres de usuario del origen al destino. La opción --trmode puede tener
        uno de los siguientes valores:
            copy    (Defecto.) Indica que los identificadores de los usuario
                    serán simplemente copiados.
            name    Los identificadores de usuario serán emparejados entre origen
                    y destino por nombre.
            table   Se utilizará la tabla de traducción indicada en la opción
                    --trtable (más información a continuación).

    --trtable=<translation_table_file>
        Si el modo de traducción es 'table', entonces una tabla de traducción
        es un fichero que contiene líneas de la forma <nombreantiguo;nombrenuevo>
        (uno por línea). Cuando la rama se escribe en el repositorio de destino,
        los objetos creados por un usuario identificado por "nombreantiguo" en
        el repositorio de origen serán asignados al usuario identificado por
        "nombrenuevo" en el repositorio de destino.

Opciones de autentificación (AuthOptions):

    Hay dos formas de especificar los datos de autentificación:

    1) Usando parámetros de autentificación: --authmode=<mode> --authdata=<data>

        --authmode=(NameWorkingMode|LDAPWorkingMode|ADWorkingMode|UPWorkingMode)
        Por ejemplo:
        (LDAPWorkingMode) --authdata=::0:dave:fPBea2rPsQaagEW3pKNveA
        (UPWorkingMode)   --authdata=dave:fPBea2rPsQaagEW3pKNveA==

        La línea '--authdata' es el contenido de la entrada <SecurityConfig>
        en los ficheros client.conf o profiles.conf. El fichero profiles.conf
        puede ser generado desde la GUI de Unity VCS en la pestaña Perfiles
        de Conexión en la ventana de Preferencias.

        Si se utiliza UPWorkingMode, se puede especificar:

        --authmode=UPWorkingMode --user=<user> --password=<pwd>

    2) Especificando un fichero de autentificación. Pueden existir varios
       ficheros de autentificación, uno por cada servidor al que se conecte,
       conteniendo las credenciales específicas para dicho servidor.

       --authfile=<authentication_file>
            Contiene 2 líneas:
            Línea 1) modo de autenticación, igual que para --authmode.
            Línea 2) datos de autenticación, igual que para --authdata.

== CMD_HELP_CLONE ==
Notas:

    El comando clone puede replicar ramas (junto a sus changesets, etiquetas,
    atributos, reviews, etc.) desde un repositorio origen a un repositorio
    destino. Los repositorios pueden encontrarse en diferentes servidores.

    El repositorio destino puede existir de antemano, pero si contiene datos
    previos, la operación de clonado fallará.

    La operación de clonado NO clona submódulos ni repositorios que se encuentren
    bajo un Xlink.

Ejemplos:

    cm clone awesomeProject@tardis@cloud
    (Clona el repositorio 'awesomeProject' de la organización Cloud tardis@cloud 
    a un repositorio local del mismo nombre.)

    <NAME_EMAIL>:9095 repo-local
    (Clona 'repo' desde 'server.home:9095' en 'repo-local' localizado en el 
    servidor de repositorios por defecto del usuario.)

    cm clone project@192.168.111.130:8084 repserver:192.168.111.200:9095
    (Clona 'project' desde '192.168.111.130:8084' a
    'project@192.168.111.200:9095'.)

    cm clone project@ldapserver:8084 --authfile=credentials.txt --trmode=table --trtable=table.txt
    (Clona el repositorio 'project' desde 'ldapserver:8084' utilizando un fichero
    de autentificación contra el servidor remoto, y traduciendo los usuarios
    siguiendo la tabla de traducción especificada.)

    <NAME_EMAIL>:9095 --package=project.plasticpkg
    <NAME_EMAIL>:8084
    cm pull --package=project.plasticpkg <EMAIL>:8084
    (Clona 'project' desde 'server.home:9095' al paquete 'project.plasticpkg'
    que se importa posteriormente en el repositorio 'project' de
    'mordor.home:8084' mediante una operación de pull.)

== CMD_DESCRIPTION_REVERT ==
Carga en el espacio de trabajo la revisión especificada y desprotege el elemento.

== CMD_USAGE_REVERT ==
Sintaxis:

    cm revert cset_spec

    cset_spec   Especificación del changeset que contiene la revisión del ítem
    cuyo contenido se desea cargar en el espacio de trabajo.
                Usa 'cm help objectspec' para obtener más información sobre la
                especificación de changesets.

== CMD_HELP_REVERT ==
Notas:

     El ítem ha de esta estar protegido.

      Ejemplos:

      cm revert dir#cs:0
      cm revert C:\mywks\dir\file1.txt#cs:23456

== CMD_DESCRIPTION_HISTORY ==
Muestra la historia de un ítem.

== CMD_USAGE_HISTORY ==
Sintaxis:

    cm history | hist <item_path>[ ...] [--long | --format=<str_format>]
                      [--symlink] [--xml[=<output_file>]] [--encoding=<name>]
                      [--moveddeleted]

    item_path       Ruta de los ítems. Use un espacio en blanco para separar
                    rutas de ítems. Use comillas dobles (" ") para especificar
                    rutas que contengan espacios.
                    Las rutas pueden ser rutas de revisiones en servidor.
                    (Use 'cm help objectspec' para más información sobre las
                    especificaciones.)

Opciones:

    --long              Muestra información adicional.
    --format            Muestra el mensaje de salida en el formato indicado.
                        Consulte las Notas para más información. No puede
                        combinarse con --xml.
    --symlink           Aplica la operación al symlink pero no al destino (target).
    --xml               Imprime el resultado en formato XML a la salida estándar.
                        También es posible especificar un fichero de salida. No
                        puede combinarse con --format.
    --encoding          Usado con la opción --xml, especifica el encoding que se
                        usará en la salida XML, por ejemplo, utf-8.
                        Consulte la documentación de MSDN en
                        http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                        para obtener la tabla de codificaciones soportadas y su
                        formato (al final de la página en la columna "Name").
    --moveddeleted      Incluye las operaciones de movidos y borrado en la historia.                        
    --limit             Muestra las N revisiones más recientes para los elementos 
                        especificados, ordenados por fecha y por changeset id. Si
                        se proporciona un número negativo, el comando devolverá una
                        lista vacía. Si se proporciona  un número mayor al número de 
                        revisiones del elemento solicitado, devolverá las existentes.

== CMD_HELP_HISTORY ==
Notas:

    Este comando muestra una lista de revisiones para el ítem indicado, e
    información de etiqueta, rama y comentario de cada revisión.

    Parámetros de formato de salida (opción --format):
        Este comando acepta una cadena de formato para mostrar la salida.
        Los parámetros de salida de este comando son los siguientes:
        {0} | {date}              Fecha.
        {1} | {changesetid}       Número de changeset.
        {2} | {branch}            Rama.
        {4} | {comment}           Comentario.
        {5} | {owner}             Propietario de la revisión.
        {6} | {id}                Id de revisión.
        {7} | {repository}        Repositorio.
        {8} | {server}            Servidor.
        {9} | {repspec}           Especificación de repositorio.
        {10}| {datastatus}        Disponibilidad del dato de la revision.
        {11}| {path}              Ruta o spec pasada como <item_path>.
        {12}| {itemid}            Id del elemento.
        {13}| {size}              Tamaño.
        {14}| {hash}              Código hash.
        {tab}                     Inserta un tabulador.
        {newline}                 Inserta una nueva línea.

Ejemplos

    cm history file1.txt "file2.txt"

    cm hist c:\workspace --long
    (Muestra toda la información.)

    cm history link --symlink
    (Aplica la operación al fichero de symlink y no al destino.)

    cm history serverpath:/src/foo/bar.c#br:/main/task001@myserver
    (Muestra la historia de una revisión de una ruta de servidor en la rama
    especificada.)

    cm history bar.c, foo.c --long --limit=2
    (Muestra las últimas 2 revisiones para los elemetos bar.c and foo.c.)

== CMD_DESCRIPTION_REVISION_TREE ==
Muestra un árbol de revisiones para el ítem dado.

== CMD_USAGE_REVISION_TREE ==
Sintaxis:

    cm tree ruta

    ruta: Ruta del ítem sobre el que se mostrará el árbol.

Opciones:

    --symlink: Efectúa la operación sobre el fichero de link y no sobre el ítem
    al que apunta.

== CMD_HELP_REVISION_TREE ==
Ejemplos:

    cm tree fichero1.txt
    cm tree c:\workspace
    cm tree link --symlink
    (Efectúa la operación sobre el fichero de symlink
    y no sobre el que apunta.)

== CMD_DESCRIPTION_REMOVE ==
Use este comando para borrar ficheros y directorios.

== CMD_USAGE_REMOVE ==
Sintaxis:

    cm remove | em comando [opciones]

Comandos:

    controlled (opcional)
    private

    Para obtener más información sobre cada comando:
    cm remove comando --usage
    cm remove comando --help

== CMD_HELP_REMOVE ==
Ejemplos:

    cm remove \fichero_controlado.txt
    cm remove private \fichero_privado.txt

== CMD_DESCRIPTION_REMOVE_CONTROLLED ==
Borra un ítem del control de versiones.

== CMD_USAGE_REMOVE_CONTROLLED ==
Sintaxis:

    cm remove [opciones] rutas

    rutas: Rutas de los ítem a borrar.

Opciones:

    -R: Descender recursivamente dentro de directorios.

== CMD_HELP_REMOVE_CONTROLLED ==
Notas:

    El ítem nunca borra del disco.

    Requisitos para borrar:

        El ítem ha de estar controlado en Unity VCS.
        El directorio padre debe estar desprotegido.
        El elemento no debe estar desprotegido.
        Si el ítem es un directorio, no debe tener desprotecciones pendientes.

Ejemplos:

    cm remove src (borra el directorio 'src')
    cm remove c:\workspace\fichero.txt

== CMD_DESCRIPTION_REMOVE_PRIVATE ==
Borra ficheros y directorios privados.

Advertencia: este comando elimina permanentemente ficheros y directorios, y no
son recuperables. Es recomendable utilizar antes la opción '--dry-run' para
comprobar qué ficheros y directorios serán afectados por el comando.

== CMD_USAGE_REMOVE_PRIVATE ==
Sintaxis:

    cm remove | rm private <ruta>+ [-r] [--verbose] [--dry-run]

    ruta                Ruta del fichero o directorio a borrar.
                        Se pueden usar comillas (") para especificar rutas que
                        contengan espacios.

Opciones:

    --r                 Elimina ficheros privados recursivamente dentro de
                        directorios controlados.
    --ignored           También se elimina ficheros y directorios ignorados y
                        cloaked.
    --verbose           Muestra todos las rutas afectadas.
    --dry-run           Ejecuta el comando sin hacer cambios en disco.

== CMD_HELP_REMOVE_PRIVATE ==
Notas:

    Si la ruta especificada corresponde a un fichero o directorio privados, este
    será eliminado de disco.
    Si la ruta corresponde a un fichero bajo control de versiones, el comando
    fallará.
    Si la ruta pertenece a un directorio bajo control de versiones, el comando
    fallará salvo que se especifique la opción '-r', en cuyo caso se borrarán
    todos los ficheros y directorios privados dentro del directorio controlado
    especificado.

Ejemplos:

    cm remove private private_directory
    (Eliminina el directorio 'private_directory'.)

    cm remove private c:\workspace\controlled_directory
    (Falla, pues el directorio 'controlled_directory' no es privado.)

    cm remove private -r c:\workspace\controlled_directory
    (Elimina todos los ficheros y directorios privados dentro de
    'controlled_directory'.)

    cm rm private --dry-run --verbose c:\workspace\controlled_directory -r
    (Muestra todas las rutas afectadas por la operación de borrado de elementos
    privados dentro de 'controlled_directory' sin borrar nada.)

    cm rm private --verbose c:\workspace\controlled_directory -r
    (Muestra todas las rutas afectadas por la operación de borrado de elementos
    privados dentro de 'controlled_directory', ejecutando el borrado.)

== CMD_DESCRIPTION_TRIGGER_DELETE ==
Borra un trigger.

== CMD_USAGE_TRIGGER_DELETE ==
Sintaxis:

    cm trigger | tr delete | rm <subtype-type> <position_number>
                                [--server=<repserverspec>]

    subtype-type        Ejecución y operación del trigger.
                        (Use 'cm showtriggertypes' para ver la lista de tipos de
                        triggers.)
    position_number     Posición asignada al trigger cuando se creó.

Opciones:

    --server            Borra el trigger del servidor especificado.
                        Si no se especifica ningún servidor, el comando se
                        ejecuta en el servidor configurado en el cliente.

== CMD_HELP_TRIGGER_DELETE ==
Ejemplos:

    cm trigger delete after-setselector 4
    cm tr rm after-setselector 4

== CMD_DESCRIPTION_ATTRIBUTE_SET ==
Asigna un atributo al objeto indicado.

== CMD_USAGE_ATTRIBUTE_SET ==
Sintaxis:

    cm attribute | att set <att_spec> <object_spec> <att_value>

    att_spec            Especificación del atributo. (Use 'cm help objectspec'
                        para más información sobre especificaciones de atributos.)
    object_spec         Especificación del objeto al que asignar el atributo.
                        Objetos válidos para este comando: rama, changeset,
                        shelveset, etiqueta, item y revisión.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones.)
    att_value           Valor del atributo que se asignará al objeto.

== CMD_HELP_ATTRIBUTE_SET ==
Notas:

   Un atributo se asigna a un objeto para añadirle más información.
   Se pueden asignar atributos a los siguientes objetos: ramas, changesets,
   shelvesets, etiquetas, items y revisiones.

Ejemplo:

    cm attribute set att:status br:/main/SCM105 open
    (Aplica el atributo 'status' a la rama 'SCM105' asignándola el valor 'open'.)

    cm att set att:integrated@reptest@server2:8084 lb:LB008@reptest@server2:8084 yes
    (Aplica el atributo 'integrated' a la etiqueta 'LB008' del repositorio 'reptest
    asignándola el valor 'yes'.)

== CMD_DESCRIPTION_SETOWNER ==
Establece el propietario de un objeto.

== CMD_USAGE_SETOWNER ==
Sintaxis:

    cm setowner | sto --user=usuario | --group=grupo spec_objeto

    --user              Nombre de usuario. Nuevo propietario del objeto.
    --group             Nombre de grupo. Nuevo propietario del objeto.
    spec_objeto         Especificación del objeto sobre el que asignar el nuevo
                        propietario.
                        Se puede asignar nuevo usuario a los siguientes objetos:
                        repserver, repositorio, rama, changeset, etiqueta, item,
                        revisión y atributo.
                        Usa 'cm help objectspec' para obtener más información
                        sobre la especificación de objetos.

== CMD_HELP_SETOWNER ==
Notas:

    Es posible modificar el propietario de un objeto mediante el comando
    setowner. El propietario de un objeto puede ser un usuario o un grupo.
    Para especificar un usuario se utiliza la opción –-user=xxx y para
    especificar un grupo se utiliza la opción -–group=xxx. El objeto que se
    quiere modificar se especifica mediante una especificación de objeto.

    Se puede modificar el propietario de los siguientes objetos:
    servidor de repositorios, repositorio, rama, changeset, etiqueta, item,
    revisión y atributo.

Ejemplos:

    cm setowner --user=danipen repserver:localhost:8084
    (establece a 'danipen' como propietario del servidor de repositorios)
    cm sto --group=development rep:principal@PlasticServer:8084
    (establece al grupo 'development' como el propietario del repositorio 'principal')

== CMD_DESCRIPTION_SETSELECTOR ==
Establece un selector para un espacio de trabajo.

== CMD_USAGE_SETSELECTOR ==
Sintaxis:
    cm setselector [opciones] [wk_ruta | wk_spec] [--noinput]

    wk_ruta                 Ruta del espacio de trabajo sobre el que establecer el
                            selector.
    wk_spec                 Especificación del espacio de trabajo sobre el que 
                            establecer el selector.
                            Usa 'cm help objectspec' para obtener más información
                            sobre especificaciones de espacios de trabajo.

Opciones:
    --file                  Fichero del que cargar un selector.
    --ignorechanges         Ignora el mensaje de aviso de cambios pendientes que se
                            muestra si existen cambios pendientes cuando se 
                            actualiza el espacio de trabajo.
    --forcedetailedprogress Fuerza mostrar progreso detallado incluso cuando se
                            redirige la salida estándar.
    --noinput               Ignora las preguntas interactivas para continuar la
                            operación con cambios pendientes o haciendolos shelve.

== CMD_HELP_SETSELECTOR ==
Notas:

    Este comando establece un selector para un espacio de trabajo determinado.

    Un workspace necesita información acerca de dónde obtener las revisiones
    con las que trabajará el usuario. Para especificar esa información se
    utilizan los selectores.

    Mediante un selector se puede indicar, por ejemplo, que se carguen las
    revisiones desde una rama determinada o desde la rama ‘main’, desde una
    etiqueta, y especificar la rama donde se alojarán las desprotecciones.

    Si no se especifica ningún fichero del que cargar el selector se abrirá
    un editor de texto.

    Indicar una ruta o una especificación de workspace es opcional. Si no se
    especifica ninguno el selector se fijará para el espacio de trabajo en
    el que se encuentra el usuario.

    Un selector de ejemplo:

    repository "default" // indica el repositorio con el que se trabaja
      path "/"           // se aplica al path raíz
        branch "/main"   // se obtendrán las últimas revisiones de br:/main
        checkout "/main" // los checkouts se ubicarán en la rama br:/main

Ejemplos:

    cm setselector
    cm setselector --file=c:\selectores\miRama.xml
    cm setselector --file=c:\selectores\rama5.xml MiWorkspace
    cm setselector wk:workspace_projA@BEARDTONGUE

== CMD_DESCRIPTION_SHELVE ==
El comando shelve almacena el contenido de las revisiones en checkout.

== CMD_USAGE_SHELVE ==
Este comando está obsoleto. Se ha reemplazado por 'cm shelveset'.

Sintaxis:

    cm shelve [opciones] [rutas]

    rutas: rutas de los items a guardar.

Opciones:
    --apply=shelve_spec : Restaura los contenidos almancenados en el
      repositorio, identificados por "shelve_spec" en el espacio de trabajo
      local.
    --delete=shelve_spec: Elimina los contenidos almancenados en el repositorio
      identificados por "shelve_spec"
    --all (-a): Los ítems cambiados, movidos y borrados localmente, en las
      rutas dadas, también serán guardados.
    --dependencies: Incluir las dependencias de los cambios locales entre los
      ítems a guardar.
    -c=comentarios: Añade un comentario al shelve creado en la operación.
    --commentsfile=comment.txt: Carga el comentario del fichero especificado.
    --summaryformat: El comando imprime sólamente la especificación del 
      objeto creado en el repositorio principal, omitiendo los objetos
      que pudieran crearse en repositorios 'xlinked'. También se omite
      cualquier otro mensaje que el comando pudiera imprimir.
      Opción diseñada para implementar automatizaciones.
    --mount: El punto de montaje del respositorio especificado.
    --encoding=codificación: Especifica la codificación de los ficheros de
      origen (por ejemplo, UTF-8 o Unicode)
    --comparisonmethod=tipo: cualquiera de las siguientes opciones:
      ignoreeol:               Ignora diferencias de final de línea.
      ignorewhitespaces:       Ignora diferencias de espacios en blanco.
      ignoreeolandwhitespaces: Ignora diferencias de final de línea y espacios en
                               blanco.
      recognizeall:            Detecta diferencias de final de línea y espacios en
                               blanco.

== CMD_HELP_SHELVE ==

Notas:

    - Si no se especifican [opciones] ni [rutas], la operación involucrará a
      todos los cambios pendientes en el espacio de trabajo.

    - La operación de shelve se aplica siempre recursivamente desde la
      ruta indicada.

    - Requisitos para hacer shelve de un ítem:
      * El ítem debe estar controlado por Unity VCS.
      * El ítem ha de estar desprotegido.

Ejemplos:

    cm shelve
    cm shelve fichero1.txt fichero2.txt

Comentarios:

    Para especificar comentarios use el switch "-c" o "-m" del siguiente modo:
      cm shelve -c="my comment"
      cm shelve -m "my comment"

    Configure la variable de entorno PLASTICEDITOR para especificar un editor
    para escribir el comentario. Si la variable de entorno PLASTICEDITOR está
    configurada pero no se indica ningún comentario, el editor se abrirá para
    que pueda escribir el comentario.

Ejemplo: Aplicar un shelve almacenado en el repositorio:
    cm shelve --apply=sh:3

Ejemplo: Eliminar un shelve almacenado en el repositorio
    cm shelve --delete=sh:3

Ejemplo: Shelve 'changelist'

      cm status --short --changelist=pending_to_review | cm shelve -
      (El comando listará los paths en el changelist llamado 'pending_to_review'
      y dicha lista será redirigida a la entrada del comando 'shelve').

== CMD_DESCRIPTION_SHELVESET ==
Use este comando para administrar shelvesets.

== CMD_USAGE_SHELVESET ==
Sintaxis:

    cm shelveset comando [opciones]

Comandos:

    create | mk
    delete | rm
    apply

    Para obtener más información sobre cada comando:
    cm shelveset command --usage
    cm shelveset command --help

== CMD_HELP_ATTRIBUTE ==
Ejemplos:

    cm shelveset create -c="my comment"
    cm shelveset delete sh:3
    cm shelve apply sh:3

== CMD_DESCRIPTION_SHELVESET_CREATE ==
El comando shelve almacena los cambios pendientes.

== CMD_USAGE_SHELVESET_CREATE ==
Sintaxis:
    cm shelveset create [opciones] [rutas]

    rutas: rutas de los items a guardar.

Opciones:
    --all (-a): Los ítems cambiados, movidos y borrados localmente, en las
      rutas dadas, también serán guardados.
    --dependencies: Incluir las dependencias de los cambios locales entre los
      ítems a guardar.
    -c=comentarios: Añade un comentario al shelve creado en la operación.
    --commentsfile=comment.txt: Carga el comentario del fichero especificado.
    --summaryformat: El comando imprime sólamente la especificación del 
      objeto creado en el repositorio principal.

== CMD_HELP_SHELVESET ==
Ejemplos:

    cm shelveset create -c="my comment"
    cm shelveset delete sh:3
    cm shelve apply sh:3

== CMD_HELP_SHELVESET_CREATE ==
El comando shelveset create almacena los ítems en checkout especificados
    dentro del repositorio. De este modo el contenido queda almacenado en el
    servidor sin necesidad de hacer check in.

Notas:

    - Si no se especifican [opciones] ni [rutas], la operación involucrará a
      todos los cambios pendientes en el espacio de trabajo.

    - La operación de shelve se aplica siempre recursivamente desde la
      ruta indicada.

    - Requisitos para hacer shelve de un ítem:
      * El ítem debe estar controlado por Unity VCS.
      * El ítem ha de estar desprotegido.

Ejemplos:

    cm shelveset create
    cm shelveset fichero1.txt fichero2.txt

Comentarios:

    Para especificar comentarios use el switch "-c" o "-m" del siguiente modo:
      cm shelveset -c="my comment"
      cm shelveset -m "my comment"

    Configure la variable de entorno PLASTICEDITOR para especificar un editor
    para escribir el comentario. Si la variable de entorno PLASTICEDITOR está
    configurada pero no se indica ningún comentario, el editor se abrirá para
    que pueda escribir el comentario.

Ejemplo: Shelve 'changelist'

      cm status --short --changelist=pending_to_review | cm shelveset -
      (El comando listará los paths en el changelist llamado 'pending_to_review'
      y dicha lista será redirigida a la entrada del comando 'shelve').

== CMD_DESCRIPTION_SHELVESET_DELETE ==
Borra un shelveset almacenado en el repositorio.

== CMD_USAGE_SHELVESET_DELETE ==
Sintaxis:
    cm shelveset delete shelve_spec

    shelve_spec: Elimina los contenidos almancenados en el repositorio
    identificados por "shelve_spec"

== CMD_HELP_SHELVESET_DELETE ==
El comando shelveset delete borra un shelveset creado anteriormente.

Ejemplo:

    cm shelveset delete sh:3
    (Elimina un shelveset almacenado en el repositorio.)

== CMD_DESCRIPTION_SHELVESET_APPLY ==
El comando shelveset apply restaura los contenidos almancenados en el
repositorio.

== CMD_USAGE_SHELVESET_APPLY ==
Sintaxis:
    cm shelveset apply shelve_spec [<change_path>[ ...]] [opciones]

    shelve_spec: Restaura los contenidos almancenados en el repositorio
    identificados por "shelve_spec"
    change_path: Ruta(s) del cambio(s) a aplicar. Es la ruta de servidor,
    una de las que se imprime con la opción --preview. Cuando no se indica
    ninguna ruta se aplican todos los cambios del shelve.

Opciones:
    --preview: Muestra los cambios a aplicar en el workspace sin aplicarles
    --mount: El punto de montaje del respositorio especificado.
    --encoding=codificación: Especifica la codificación de los ficheros de
      origen (por ejemplo, UTF-8 o Unicode)
    --dontcheckout: No hace checkout de los ficheros con cambios a aplicar,
      los mantiene como cambios locales. Esto es útil para evitar el checkout
      exclusivo al aplicar un shelve.       
    --comparisonmethod=tipo: cualquiera de las siguientes opciones:
      ignoreeol:               Ignora diferencias de final de línea.
      ignorewhitespaces:       Ignora diferencias de espacios en blanco.
      ignoreeolandwhitespaces: Ignora diferencias de final de línea y espacios en
                               blanco.
      recognizeall:            Detecta diferencias de final de línea y espacios en
                               blanco.

== CMD_HELP_SHELVESET_APPLY ==
El comando shelveset apply restaura los contenidos almancenados en el
repositorio.

Ejemplo: Aplicar un shelve almacenado en el repositorio:

    cm shelveset apply sh:3

Ejemplo: Aplicar solo 1 cambio almacenado en un shelve en el repositorio:

    cm shelveset apply sh:3 /src/foo.c

== CMD_DESCRIPTION_SHOW_FIND_OBJECTS ==
Muestra la lista de objetos disponibles y sus atributos correspondientes.

== CMD_USAGE_SHOW_FIND_OBJECTS ==
Sintaxis:

    cm showfindobjects

== CMD_HELP_SHOW_FIND_OBJECTS ==
Objetos y atributos disponibles: 

attribute:
    Se puede buscar atributos filtrando por los siguientes campos:

    Type    : string.
    Value   : string.
    Date    : fecha.
              Consulte la sección "constantes de fecha" en esta guía para más
                info.
    Owner   : usuario.
              Admite el usuario especial 'me'.
    GUID    : Identificador Global Único.
              Id hexadecimal con el formato xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.
    Comment : string.
    SrcObj  : object spec.
    ID      : integer.

attributetype:
    Se puede buscar tipos de atributo filtrando por los siguientes campos:

    Name    : string.
    Value   : string.
    Date    : fecha.
              Consulte la sección "constantes de fecha" en esta guía para más
                info.
    Owner   : usuario.
              Admite el usuario especial 'me'.
    GUID    : Identificador Global Único.
              Id hexadecimal con el formato xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.
    Comment : string.
    Source  : object spec.
    ID      : integer.

    Campos de replicación. Consulte la sección "campos relacionados con la réplica"
    en esta guía para más información sobre:
        ReplLogId
        ReplSrcDate
        ReplSrcId
        ReplSrcRepository
        ReplSrcServer

branch:
    Se puede buscar ramas filtrando por los siguientes campos:

    Name       : string.
    Date       : fecha.
                 Consulte la sección "constantes de fecha" en esta guía para más
                   info.
    Changesets : fecha (de los changesets en la rama).
                 Consulte la sección "constantes de fecha" en esta guía para más
                   info.
    Attribute  : string.
    AttrValue  : string.
    Owner      : usuario.
                 Admite el usuario especial 'me'.
    Parent     : branch spec.
    Comment    : string.
    Hidden     : boolean.
    GUID       : Identificador Global Único.
                 Id hexadecimal con el formato xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.
    Item       : item spec or item id (integer).
    ID         : integer.

    Es posible usar la cláusula 'order by' con este objeto. En concreto, puede ordenar
    por los siguientes campos:
        date
        branchname

    Campos de replicación. Consulte la sección "campos relacionados con la réplica"
    en esta guía para más información sobre:
        ReplLogId
        ReplSrcDate
        ReplSrcId
        ReplSrcRepository
        ReplSrcServer

changeset:
    Se puede buscar changesets filtrando por los siguientes campos:

    Branch            : branch spec.
    ChangesetId       : integer.
    Attribute         : string.
    AttrValue         : string.
    Date              : fecha.
                        Consulte la sección "constantes de fecha" en esta guía
                          para más info.
    Owner             : usuario.
                        Admite el usuario especial 'me'.
    GUID              : Identificador Global Único.
                        Id hexadecimal con el formato xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.
    Comment           : string.
    IgnoreHidden      : boolean.
    OnlyWithRevisions : boolean.
    ReturnParent      : boolean.
    Parent            : changeset id (integer).
    ID                : integer.

    Es posible usar la cláusula 'order by' con este objeto. En concreto, puede ordenar
    por los siguientes campos:
        date
        changesetid

    Campos de replicación. Consulte la sección "campos relacionados con la réplica"
    en esta guía para más información sobre:
        ReplLogId
        ReplSrcDate
        ReplSrcId
        ReplSrcRepository
        ReplSrcServer

label:
    Se puede buscar etiquetas filtrando por los siguientes campos:

    Name      : string.
    Attribute : string.
    AttrValue : string.
    Date      : fecha.
                Consulte la sección "constantes de fecha" en esta guía para más
                  info.
    Owner     : usuario.
                Admite el usuario especial 'me'.
    GUID      : Identificador Global Único.
                Id hexadecimal con el formato xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.
    Branch    : branch spec.
    Branchid  : integer.
    Changeset : changeset id (integer).
    Comment   : string.
    ID        : integer.

    Es posible usar la cláusula 'order by' con este objeto. En concreto, puede ordenar
    por los siguientes campos:
        date
        labelname

    Campos de replicación. Consulte la sección "campos relacionados con la réplica"
    en esta guía para más información sobre:
        ReplLogId
        ReplSrcDate
        ReplSrcId
        ReplSrcRepository
        ReplSrcServer

merge:
    Se puede buscar merges filtrando por los siguientes campos:

    SrcBranch    : branch spec.
    SrcChangeset : changeset id (integer).
    DstBranch    : branch spec.
    DstChangeset : changeset id (integer).
    Date         : fecha.
                   Consulte la sección "constantes de fecha" en esta guía para
                     más info.
    Owner        : usuario.
                   Admite el usuario especial 'me'.
    GUID         : Identificador Global Único.
                   Id hexadecimal con el formato xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.
    Type         : string.
                   Los posibles valores son 'merge', 'cherrypick',
                     'cherrypicksubstractive', 'interval', 'intervalcherrypick'
                     e 'intervalcherrypicksubstractive'
    ID           : integer.

replicationlog:
    Se puede buscar replication log filtrando por los siguientes campos:

    Branch         : branch spec.
    RepositoryName : string.
    Owner          : usuario.
                     Admite el usuario especial 'me'.
    Date           : fecha.
                     Consulte la sección "constantes de fecha" en esta guía para
                       más info.
    Server         : string.
    Package        : boolean.
    ID             : integer.

review:
    Se puede buscar code reviews filtrando por los siguientes campos:

    Status     : string.
    Assignee   : string.
    Title      : string.
    Target     : object spec.
    TargetId   : integer.
    TargetType : string.
                 Los posibles valores son 'branch' y 'changeset'.
    Date       : fecha.
                 Consulte la sección "constantes de fecha" en esta guía para más
                   info.
    Owner      : usuario.
                 Admite el usuario especial 'me'.
    GUID       : Identificador Global Único.
                 Id hexadecimal con el formato xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.
    Id         : integer.

    Es posible usar la cláusula 'order by' con este objeto. En concreto, puede ordenar
    por los siguientes campos:
        date
        modifieddate
        status

revision:
    Se puede buscar revisiones filtrando por los siguientes campos:

    Branch              : branch spec.
    Changeset           : changeset id (integer).
    Item                : string o integer.
    ItemId              : integer.
    Attribute           : string.
    AttrValue           : string.
    Archived            : boolean.
    Comment             : string.
    Date                : fecha.
                          Consulte la sección "constantes de fecha" en esta guía
                            para más info.
    GUID                : Identificador Global Único.
                          Id hexadecimal con el formato xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.
    Owner               : usuario.
                          Admite el usuario especial 'me'.
    Parent              : revision id (integer).
    ReturnParent        : boolean.
    Shelve              : shelve id (integer).
    Size                : integer (en bytes).
    Type                : string.
                          Los posibles valores son 'dir', 'bin' y 'txt'.
    WorkspaceCheckoutId : integer.
    ID                  : integer.

    Campos de replicación. Consulte la sección "campos relacionados con la réplica"
    en esta guía para más información sobre:
        ReplLogId
        ReplSrcDate
        ReplSrcId
        ReplSrcRepository
        ReplSrcServer

shelve:
    Se puede buscar shelves filtrando por los siguientes campos:

    Owner     : usuario.
                Admite el usuario especial 'me'.
    Date      : fecha.
                Consulte la sección "constantes de fecha" en esta guía para más
                  info.
    Attribute : string.
    AttrValue : string.
    Comment   : string.
    GUID      : Identificador Global Único.
                Id hexadecimal con el formato xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.
    Parent    : integer.
    ShelveId  : integer.
    ID        : integer.

    Campos de replicación. Consulte la sección "campos relacionados con la réplica"
    en esta guía para más información sobre:
        ReplLogId
        ReplSrcDate
        ReplSrcId
        ReplSrcRepository
        ReplSrcServer


Campos relacionados con la réplica:
    Muchos objetos mantienen información de replicación, lo que significa que
    Unity VCS mantiene el rastro de dónde se crearon originalmente.

    Los campos que se pueden utilizar son:

        ReplSrcServer : Significa "servidor origen de la réplica", y es el
                        servidor de donde se trajeron los objetos.

              Ejemplo:
              cm find branch "where replsrcserver='skull.codicefactory.com:9095'"

        ReplSrcRepository : string. Significa "repositorio origen de la réplica",
                            y es el repositorio de donde se trajeron los objetos.

              Ejemplo:
              cm find branch "where \
              replsrcserver = 'skull.codicefactory.com:9095' \
              and replsrcrepository = 'codice'"

        ReplLogId : integer. El identificador de la operación de réplica.
                    En Unity VCS, cada vez que un objeto nuevo se crea de una
                    réplica, también se crea un nuevo 'replicationlog'.

              Ejemplo:
              cm find replicationlog
              324054   9/13/2018 02:00:15 /main/scm23064 sluisp   plasticscm.com T
              327255   9/11/2018 12:33:38 /main/scm23042 maria    plasticscm.com T
              329631   9/17/2018 13:06:49 /main/scm23099 sluisp   plasticscm.com T

              Ahora se pueden buscar las ramas creadas en la réplica 327255, en
              caso de haber alguna:

              cm find branch "where replogid=327255"

        ReplSrcDate: fecha. Es la fecha en la que la operación de réplica tuvo
                     lugar. Los objetos replicados mantienen la fecha en la que
                     fueron creados originalmente, por lo que este campo es útil
                     si se necesita buscar objetos que fueron replicados en un
                     determinado marco temporal.

              Ejemplo:
              cm find replicationlog "where date > 'one week ago'"
              8780433  27/09/2018 8:49:38 codice@BACKYARD:8087 F   mbarriosc

              Ahora se puede verificar que la rama replicada se creó antes de la
              propia operación de réplica:

              cm find branch "where repllogid = 8780433"
              8780443  26/09/2018 12:20:55 /main/scm23078 maria    codice T

        ReplSrcId: integer. Es el ID del servidor origen de la réplica
                   (ReplSrcServer). Este ID se puede descubrir buscando objetos
                   de tipo 'replicationsource' con el comando 'cm find'.

              Ejemplo:
              cm find replicationsource
              7860739  codice@AFRODITA:8087 d9c4372a-dc55-4fdc-ad3d-baeb2e975f27
              8175854  codice@BACKYARD:8087 66700d3a-036b-4b9a-a26f-adfc336b14f9

              Ahora se pueden buscar los changesets replicados desde
              codice@AFRODITA:8087 de la siguiente forma:

              cm find changesets "where replsrcid = 7860739"


Constantes de fecha:
    Se pueden usar fechas formateadas siguiendo la configuración de localización
    del Sistema Operativo. Por ejemplo, si el SO muestra fechas en el formato
    'dd/MM/yyyy', se pueden utilizar fechas como '31/12/2018' en las consultas.

    Sin embargo, también se pueden utilizar las siguientes constantes para
    facilitar la escritura de las queries:
        'today'         : la fecha de hoy.
        'yesterday'     : la fecha de ayer.
        'this week'     : la fecha del lunes de la semana actual.
        'this month'    : la fecha del primer día del mes actual.
        'this year'     : la fecha del 1 de enero del año actual.
        'one day ago'   : un día antes de la fecha actual.
        'one week ago'  : una semana antes de la fecha actual.
        'one month ago' : un mes antes de la fecha actual.
        'n days ago'    : 'n' días antes de la fecha actual.
        'n months ago'  : 'n' meses antes de la fecha actual.
        'n years ago'   : 'n' años antes de la fecha actual.

    Las siguientes cláusulas 'where' son válidas para campos que admitan fechas:
        '(...) where date > 'today' (...)'
        '(...) where date < 'yesterday' (...)'
        '(...) where date > 'this week' (...)'
        '(...) where date > 'this month' (...)'
        '(...) where date < 'one day ago' and date > '3 days ago' (...)'
        '(...) where date < 'one week ago' and date > '3 weeks ago' (...)'
        '(...) where date < 'one month ago' and date > '3 months ago' (...)'
        '(...) where date > '1 year ago' (...)'

    También se puede forzar un determinado formato de fecha en el comando
    'cm find' utilizando el flag --dateformat. Lee 'cm find --help' para más
    información.

== CMD_DESCRIPTION_TRIGGER_SHOWTYPES ==
Muestra la lista de los tipos de triggers disponibles.

== CMD_USAGE_TRIGGER_SHOWTYPES ==
Sintaxis:

    cm trigger showtypes

== CMD_DESCRIPTION_SHOWACL ==
Muestra la ACL (Lista de Control de Acceso) de un elemento.

== CMD_USAGE_SHOWACL ==
Sintaxis:

    cm showacl [opciones] spec_objeto

    spec_objeto         Especificación del objeto sobre el que mostrar el ACL.
                        Objetos válidos para este comando son repserver,
                        repositorio, rama, changeset, etiqueta, item y atributo.
                        Usa 'cm help objectspec' para obtener más información
                        sobre la especificación de objetos.

Opciones:

    --extended          Muestra información detallada.
    --xml               Permite volcar la salida del comando en formato xml
                        a la salida estándar. También se permite especificar
                        un fichero de salida (--xml=output.xml).
    --encoding          Si se utiliza con la opción --xml, permite especificar
                        el encoding con el que se guardará la salida del
                        comando. Por ejemplo: --encoding=utf-8
                        Consulte la documentación de la MSDN para obtener la
                        tabla completa de codificaciones soportadas y su formato;
                        al final de la página en la columna "Name":
      http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx

== CMD_HELP_SHOWACL ==
Notas:

    Este comando se utiliza para obtener una lista de permisos de un usuario o
    un grupo sobre un elemento del repositorio.

Ejemplos:

    cm showacl repserver:PlasticServer:8084
    cm showacl br:/main --extended

== CMD_DESCRIPTION_SHOWCOMMANDS ==
Muestra todos los comandos disponibles.

== CMD_USAGE_SHOWCOMMANDS ==
Sintaxis:

    cm showcommands

== CMD_HELP_SHOWCOMMANDS ==

== CMD_DESCRIPTION_SHOWOWNER ==
Muestra el propietario de un elemento del repositorio.

== CMD_USAGE_SHOWOWNER ==
Sintaxis:

    cm showowner spec_objeto

    spec_objeto         Especificación del objeto para mostar su propietario.
                        Usa 'cm help objectspec' para obtener más información
                        sobre la especificación de objetos.
                        El objeto debe ser uno de los siguientes: repserver,
                        repositorio, rama, changeset, etiqueta, atributo,
                        revisión e item.

== CMD_HELP_SHOWOWNER ==
Notas:

    Este comando muestra el propietario de un elemento del repositorio.
    Puede ser un usuario o un grupo.
    El propietario se puede modificar con el comando 'cm setowner'.

Ejemplos:

    cm showowner repserver:PlasticServer:8084
    cm showowner rev:fichero.cs#br:/main#LAST

== CMD_DESCRIPTION_SHOWPERMISSIONS ==
Muestra la lista de los permisos disponibles.

== CMD_USAGE_SHOWPERMISSIONS ==
Sintaxis:

    cm showpermissions

== CMD_HELP_SHOWPERMISSIONS ==
Ejemplos:

    cm showpermissions

== CMD_DESCRIPTION_SHOWSELECTOR ==
Muestra el selector del espacio de trabajo actual.

== CMD_USAGE_SHOWSELECTOR ==
Sintaxis:

    cm showselector [wk_ruta | wk_spec]

    wk_ruta         Ruta del espacio de trabajo para mostrar su selector
    wk_spec         Especificación del espacio de trabajo para mostrar su
                    selector.
                    Usa 'cm help objectspec' para obtener más información sobre
                    especificaciones de espacios de trabajo.

== CMD_HELP_SHOWSELECTOR ==
Notas:

    Si no se especifica una ruta o un especificador de espacio de trabajo,
    el comando tomará como ruta el directorio actual.

Ejemplos:

    cm showselector c:\workspace
    cm showselector
    cm showselector > miSelector.txt
    cm showselector wk:build_wk@BUILDER

== CMD_DESCRIPTION_SUPPORT ==
Este comando permite al usuario efectuar operaciones de soporte.

== CMD_USAGE_SUPPORT ==
Sintaxis:

    cm support comando [opciones]

Comandos:

    bundle

    Para obtener más información sobre cada uno de los comandos ejecute:
    cm support comando --usage
    cm support comando --help

== CMD_HELP_SUPPORT ==
Ejemplos:

    cm support
    cm support bundle
    cm support bundle c:\outputfile.zip

== CMD_DESCRIPTION_SUPPORT_BUNDLE ==
Crea el paquete de soporte con los ficheros de log relevantes. 
Este paquete se puede adjuntar cuando se solicita la ayuda, cuando 
se pregunte por información extra o cuando se envíe un error.

== CMD_USAGE_SUPPORT_BUNDLE ==
Sintaxis:

    cm support bundle [outputfile]

Opciones:

    outputfile          Crea el paquete de soporte en la ruta especificado.

== CMD_HELP_SUPPORT_BUNDLE ==
Notas:

    Este comando permite al usuario crear un paquete de soporte con los
    ficheros de log relevantes. Es posible especificar un fichero de salida.

Ejemplos:

    cm support bundle
    (Crea el paquete de soporte en el directorio temporal)

    cm support bundle c:\outputfile.zip
    (Crea el paquete de soporte en la ruta especificado)

== CMD_DESCRIPTION_SWITCH ==
Actualiza el espacio de trabajo a la rama, etiqueta, changeset o shelve especificado.

== CMD_USAGE_SWITCH ==
Sintaxis:

    cm switch brspec | csetspec | lbspec | shspec
              [--workspace=path] [--repository=name]
              [--forcedetailedprogress] [--noinput]
              [--silent] [--verbose] [--xml[=<output_file>]] [--encoding=<name>]

Opciones:

    --workspace             Ruta del espacio de trabajo a cambiar.
    --repository            Repositorio al que se va a cambiar.
    --forcedetailedprogress Fuerza mostrar progreso detallado incluso cuando se
                            redirige la salida estándar.
    --noinput               Ignora las preguntas interactivas para continuar la
                            operación con cambios pendientes o haciendolos shelve.
                            Usar esta opción deshabilita la posibilidad de llevar
                            cambios pendientes.
    --silent                Solo se imprimen mensajes en caso de error.
    --verbose               Muestra información adicional.
    --xml                   Imprime el resultado en formato XML a la salida
                            estándar. También es posible especificar un fichero
                            de salida.
                            Usar esta opción deshabilita la posibilidad de llevar
                            cambios pendientes.
    --encoding              Usado con la opción --xml, especifica el encoding
                            que se usará en la salida XML, por ejemplo, utf-8.
                            Consulte la documentación de MSDN en
                            http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                            para obtener la tabla de codificaciones soportadas y
                            su formato (al final de la página en la columna "Name").

    Usa 'cm help objectspec' para aprender más sobre cómo indicar
    especificaciones de changesets, ramas y etiquetas.

== CMD_HELP_SWITCH ==
Notas:

    Este comando permite a los usuarios actualizar el espacio de trabajo
    con el contenido del objeto especificado (rama, etiqueta o changeset).

Ejemplos:

    cm switch br:/main
    cm switch lb:Rel1.1
    cm switch Rel2.0
    cm switch cs:4375
    cm switch 5632
    cm switch sh:2

== CMD_DESCRIPTION_SWITCH_TO_BRANCH ==
Establece la rama especificada como rama de trabajo.

== CMD_USAGE_SWITCH_TO_BRANCH ==
Sintaxis:

    cm switchtobranch [opciones] [spec_rama]

    spec_rama: Especificación de rama.

Opciones:

    --label=nombre | --changeset=número: Carga las revisiones de la etiqueta
      o changeset especificado. Si no se especifica spec_rama, una de
      estas opciones es obligatoria.

    --workspace | -wk=path: Ruta del espacio de trabajo a cambiar.

== CMD_HELP_SWITCH_TO_BRANCH ==
Notas:

    Este comando permite a los usuarios especificar una rama, como rama de
    trabajo. Adicionalmente se puede especificar una etiqueta o un changeset,
    desde la cual se cargarán las revisiones.
    Si no se especifica rama, se ha de especificar una etiqueta o un changeset
    obligatoriamente. Consulte el manual para obtener más información.
    Si no se especifica repositorio, la rama se fija sobre el repositorio
    actual.

Ejemplos:

    cm switchtobranch br:/main
    cm switchtobranch br:/main/tarea001
    cm switchtobranch --label=BL050
    (Configuración de sólo lectura. Se cargan revisiones del changeset etiquetado)

== CMD_DESCRIPTION_SYNC ==
Sincroniza con Git

== CMD_USAGE_SYNC ==
Sintaxis:

    cm synchronize | sync repspec git [url [--user=usr_name --pwd=pwd]] [--author]
            [[--txtsimilaritypercent | --binsimilaritypercent | --dirsimilaritypercent]=value]
            [--skipgitlfs] [--gitpushchunk=<value>]

    repspec             Especificación del repositorio.
                        Usa 'cm help objectspec' para obtener más información
                        sobre la especificación de repositorios.
    git                 (Default).

Opciones:

    url                 URL del repositorio remoto 
                        (http(s):// o git:// o una URL ssh).
    --user              Nombre de usuario para la URL especificada.
    --pwd               Contraseña para la URL especificada.
    --txtsimilaritypercent | --binsimilaritypercent | --dirsimilaritypercent
                        Opciones para detectar ítems movidos, del mismo modo que
                        lo hace la GUI de Unity VCS.
    --author            Usa los valores de nombre y fecha (timestamp) del autor
                        en git (git committer by default).
    --skipgitlfs        Ignora la configuración de Git LFS que haya en el
                        fichero '.gitattributes'. Se comporta como si no
                        tuviese soporte de Git LFS.
    --gitpushchunk      Procesa la operación de push (exportar cambios de
                        Unity VCS a Git) en bloques de un determinado número
                        de changesets. Esta opción solo es útil en
                        repositorios muy grandes para evitar problemas con
                        la red o con el tamaño del paquete a enviar a Git o
                        simplemente con el fin de depurar otros problemas.
                        Usa bloques de 1000 changesets si no se especifica
                        un valor concreto.                        

== CMD_HELP_SYNC ==
Notas:

    En caso de sincronizar con un servidor que no requiera credenciales, tras
    la primera sincronización no es necesario introducir el parámetro URL.
    Este comando no soporta repositorios locales.

    En el caso de usar el protocolo SSH, es necesario tener añadido al PATH
    el cliente por línea de comandos 'ssh' y que esté correctamente configurado
    para conectarse al servidor remoto (claves públicas/privadas).

Ejemplos:

      cm sync default@localhost:8087 git git://localhost/repository

== CMD_DESCRIPTION_TRIGGER ==
Use este comando para administrar triggers.

== CMD_USAGE_TRIGGER ==
Sintaxis:

    cm trigger | tr <command> [options]

Comandos:

    create | mk
    delete | rm
    edit
    list   | ls
    showtypes

    Para obtener más información sobre cada comando:
    cm trigger <command> --usage
    cm trigger <command> --help

== CMD_HELP_TRIGGER ==
Ejemplos:

    cm tr mk before-mklabel new "/path/to/script" --server=myserver:8084
    cm tr edit before-mklabel 7 --position=4 --server=myserver:8084
    cm tr ls before-mkbranch --server=myserver:8084
    cm tr rm after-setselector 4
    cm tr showtypes

== CMD_DESCRIPTION_UNDOCHECKOUT ==
Deshace la desprotección de un ítem.

== CMD_USAGE_UNDOCHECKOUT ==
Sintaxis:

    cm undocheckout | unco <item_path>+ [--all] [--symlink] [--silent]
         [--keepchanges | -k]

    item_path        Especificación de los ítems para deshacer la desprotección,
                     separados por comas.
                     Se pueden usar comillas (") para especificar rutas que
                     contengan espacios.
                     Use . para aplicar la operación al directorio actual.

Opciones:

    --all (-a)         Los ítems especificados que han sido cambiados, movidos
                       y borrados localmente también serán deshechos.

    --symlink          Deshace la protección del fichero de link y no al
                       elemento al que apunta.

    --silent           No muestra salida.

    --keepchanges (-k) Deshace la desprotección dejando los cambios locales.
                       Ejemplo: un checkout lo deja como changed con el 
                       contenido en disco que tenia antes de hacer el unco.
                       Esta opción no puede utilizarse con workspaces dinámicos.

== CMD_HELP_UNDOCHECKOUT ==
Notas:

    Si se desean descartar los cambios realizados en un ítem desprotegido
    previamente, se utiliza este comando.
    El ítem se cambia a la revisión justamente anterior.

    Requisitos para deshacer la desprotección:

        El ítem ha de estar controlado por Unity VCS
        El ítem ha de estar desprotegido


Ejemplos:

    cm undocheckout .
    (Desprotege el directorio actual)
    
    cm undocheckout fichero1.txt fichero2.txt
    
    cm unco c:\workspace\fichero.txt
    
    cm undocheckout -R c:\workspace\src
    (Deshace la desprotección de 'src' recursivamente)
    
    cm unco link --symlink
    (Efectúa la desprotección sobre el fichero de symlink y no del que apunta.)

    cm unco code\cgame\cg_main.c --all
    (Deshace la desprotección del fichero cambiado)

Deshacer 'changelist'. Ejemplo:

      cm status --short --changelist=pending_to_review | cm undocheckout -
      (El comando listará los paths en el changelist llamado 'pending_to_review'
      y dicha lista será redirigida a la entrada del comando 'uncheckout').

== CMD_DESCRIPTION_UNDOCHECKOUTUNCHANGED ==
Deshace la desprotección en elementos que no han cambiado.

== CMD_USAGE_UNDOCHECKOUTUNCHANGED ==
Sintaxis:

    cm uncounchanged [opciones]

Opciones:

    -R|-r|--recursive   Descender recursivamente dentro de directorios.

== CMD_HELP_UNDOCHECKOUTUNCHANGED ==
Notas:

    Este comando se aplica desde la raíz del workspace recursivamente.

Ejemplos:

    cm uncounchanged

== CMD_DESCRIPTION_UNDELETE ==
Recupera en el espacio de trabajo la revisión especificada en la ruta dada.

== CMD_USAGE_UNDELETE ==
Sintaxis:

      cm undelete rev_spec ruta

      rev_spec: Especificación de la revisión del ítem cuyo contenido se desea
      cargar en el espacio de trabajo.
      path: Ruta donde se va a restaurar.

== CMD_HELP_UNDELETE ==
Notas:

    El elemento a recuperar no debe de estar ya cargado en el workspace.

    La operación de recuperar no esta soportada para xlinks.

Ejemplos:

    cm undelete revid:756 C:\mywks\src\foo.c
    cm undelete itemid:68#cs:2 C:\mywks\dir\myfile.pdf
    cm undelete serverpath:/src#br:/main C:\mywks\Dir

== CMD_DESCRIPTION_UNDOCHANGE ==
Deshace los cambios hechos en un fichero.

== CMD_USAGE_UNDOCHANGE ==
Notas:

    Si se desean descartar los cambios en un ítem, independientemente de su
    estado (desprotegido o modificado sin hacer desprotección), este comando
    vuelve a la revisión anterior. Si se ordena realizar la operación sobre
    un directorio en vez de un fichero la acción se realiza recursivamente por
    defecto.

Ejemplos:

    cm unc . (Descarta los cambios de los ítems del directorio actual).
    cm unc fichero1.txt fichero2.txt
    cm unc c:\workspace\fichero1.txt

== CMD_HELP_UNDOCHANGE ==
Ejemplos:

    cm unc . (Descarta los cambios de los ítems del directorio actual).
    cm unc fichero1.txt fichero2.txt
    cm unc c:\workspace\fichero1.txt

== CMD_DESCRIPTION_UNDO ==
Deshace cambios en un workspace.

== CMD_USAGE_UNDO ==
Syntaxis:

    cm undo [<path> [...]] [--symlink] [-r | --recursive] [<filter> [...]]
            [--silent | --machinereadable [--startlineseparator=<sep>]
                                          [--endlineseparator=<sep>]
                                          [--fieldseparator=<sep>]]

    path        Rutas de los ficheros o directorios sobre los que deshacer los
                cambios. Use comillas dobles (" ") para especificar rutas que
                contengan espacios. Use un espacio en blanco para separar rutas
                de ítems.
                Si no se especifica ninguna ruta, la operación se ejecutará
                sobre los ficheros del directorio actual.
    filter      Aplica el filtro o filtros indicados a las rutas especificadas.
                Use un espacio en blanco para separar filtros. Consulte la
                sección Filtros para más información.

Opciones:

    --symlink               Deshace los cambios en el symlink pero no en el
                            destino (target).
    -r                      Deshace los cambios recursivamente.
    --silent                No muestra ninguna salida.
    --machinereadable       Muestra el resultado en un formato fácil de parsear.
    --startlineseparator    Usado con '--machinereadable', indica cómo deben
                            empezar las líneas del resultado.
    --endlineseparator      Usado con '--machinereadable', indica cómo deben
                            terminar las líneas del resultado.
    --fieldseparator        Usado con '--machinereadable', indica cómo deben
                            separarse los campos de cada línea resultante.

Filtros:

    Las rutas se pueden filtrar usando uno o varios de los filtros a continuación.
    Cada uno de estos filtros se refiere a un tipo de cambio:
    --checkedout    Selecciona ficheros y directorios desprotegidos (checked-out).
    --unchanged     Selecciona ficheros sin cambios.
    --changed       Selecciona ficheros y directorios cambiados o desprotegidos
                    (checked-out).
    --deleted       Selecciona ficheros y directorios borrados.
    --moved         Selecciona ficheros y directorios movidos.
    --added         Selecciona ficheros y directorios añadidos.

    Si la ruta contiene alguno de los tipos de cambios especificados en los
    filtros, entonces solo esos tipos de cambios se desharán en dicha ruta.
    Por ejemplo, si se especifica a la vez --checkedout y --moved, si un fichero
    está tanto movido como checkedout, ambos tipos de cambios se desharán.

    Si no se especifica ningun filtro, se deshacen todos los tipos de cambios.

== CMD_HELP_UNDO ==
Notas:

    - Si no se especifica ninguna ruta, se deshacen todos los cambios en el
      directorio actual pero no recursivamente.

    - Si se especifica una o más rutas, se deshacen todos los cambios en las
      rutas especificadas pero no recursivamente.

    - Para hacer que el undo sea recursivo hay que especificar la opción -r.
      Así para deshacer todos los cambios de un directorio incluyendo los cambios
      sobre el propio directorio habría que ejecutar lo siguiente:

          cm undo dirpath -r

      Si dirpath es la ruta de un workspace, entonces se deshacen todos los
      cambios dentro del workspace.

    - El comando undo es peligroso porque deshace los cambios irreversiblemente.
      Es decir, no hay manera posible de recuperar el estado anterior de los
      ficheros y directorios afectados.

    - En el siguiente escenario:

          /src
          |- file.txt
          |- code.cs
          \- /test
             |- test_a.py
             \- test_b.py

      Estos comandos son equivalentes (ejecutados desde el directorio /src):
          cm undo
          cm undo *
          cm undo file.txt code.cs /test

      Y estos también son equivalentes (también ejecutados desde /src):
          cm undo .
          cm undo /src file.txt code.cs

Ejemplos:

    cm undo .
    (Deshace todos los cambios en el directorio actual.
     No deshará los cambios contenidos en subdirectorios.)

    cm undo . -r
    (Deshace recursivamente todos los cambios en el directorio actual. Si
    se ejecuta desde la raíz del workspace, deshace todos los cambios en el
    workspace completo.)

    cm co file.txt
    cm undo file.txt
    (Deshace el checkout en file.txt.)

    cm undo c:\otroworkspace\file.txt
    (Deshace los cambios en 'file.txt' que se encuentra en un workspace distinto
    del que se está trabajando.)

    echo content >> file.txt
    cm undo file.txt
    (Deshace el cambio local en file.txt.)

    cm undo src
    (Deshace los cambios en el directorio src y en todos los ficheros
    controlados que contenga)

    cm undo src/*
    (Deshace los cambios en todos ficheros y directorios en src sin afectar
    al propio directorio.)

    cm undo *.cs
    (Deshace cambios en cada fichero y directorio que cumpla el patrón *.cs en
    el directorio actual.)

    cm undo *.cs -r
    (Deshace recursivamente cambios en cada fichero y directorio que cumpla el
    patrón *.cs en el directorio actual y en cada directorio por debajo.)

    cm co file1.txt file2.txt
    echo content >> file1.txt
    cm undo --unchanged
    (Deshace el checkout en file2.txt y no en file1.txt ya que file1.txt sí
    tiene cambios locales.)

    echo content >> file1.txt
    echo content >> file2.txt
    cm co file1.txt
    cm undo --checkedout
    (Deshace el cambio en el fichero desprotegido (checked-out) file1.txt e
    ignora file2.txt ya que no está en checked-out.)

    cm add file.txt
    cm undo file.txt
    (Deshace el añadido de file.txt dejándolo de nuevo como privado.)

    $ rm file1.txt
    $ echo content >> file2.txt
    $ cm add file3.txt
    $ cm undo --deleted --added *
    (Deshace el borrado de file1.txt y el añadido de file3.txt, ignorando
    el cambio en file2.txt.)

== CMD_DESCRIPTION_LOCK_UNLOCK ==
Deshace los bloqueos en los elementos de un servidor.

== CMD_USAGE_LOCK_UNLOCK ==
Sintaxis:

    cm lock unlock <itemspec>[ ...] [--remove]

    --remove            Elimina completamente el bloqueo para el elemento
                        específicado. Si esta opción no se espefica,
                        simplemente se libera el bloqueo actual.
    itemspec            Una o más especificaciones de ítem.(Use 'cm help objectspec'
                        para más información sobre especificaciones de ítem.)

== CMD_HELP_LOCK_UNLOCK ==
Notas:

    - Sólo el administrador del servidor puede ejecutar 'cm unlock --remove'
      para elminar bloqueos.
    - Sólo el administrador del servidor puede liberar bloqueos que pertenecen
      a otros usuarios. Cualquier usuario puede liberar su propios bloqueos.

Ejemplos:

    cm lock unlock itemid:56@myrep@DIGITALIS:8084
    (Libera el bloqueo del ítem id 56 en el repo 'myrep@DIGITALIS'.)

    cm lock unlock item:/workspace/foo.psd item:/workspace/bar.psd
    (Libera el bloqueo de los items especificados.)

    cm lock unlock itemid:56@myrep itemid:89@myrep --remove
    (Elimina el bloqueo de los items especificados.)
    
    cm lock unlock 91961b14-3dfe-4062-8c4c-f33a81d201f5
    (Deshace el bloqueo del ítem indicado.)

    cm lock unlock DIGITALIS:8084 2340b4fa-47aa-4d0e-bb00-0311af847865 \
      bcb98a61-2f62-4309-9a26-e21a2685e075
    (Deshace los bloqueos de los ítems indicados del servidor 'DIGITALIS'.)

    cm lock unlock tardis@cloud 4740c4fa-56af-3dfe-de10-8711fa248635 \
      71263c17-5eaf-5271-4d2c-a25f72e101d4
    (Deshace los bloqueos de los ítems indicados del servidor cloud 'tardis'.)

== CMD_DESCRIPTION_LOCK_CREATE ==
Crea bloqueos en los elementos de un servidor.

== CMD_USAGE_LOCK_CREATE ==
Sintaxis:

    cm lock create | mk <branchspec> <itemspec>[ ...]

    branchspec          La rama donde los bloqueos serán creados.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de rama.)
    itemspec            Una o más especificaciones de ítem.
                        (Use 'cm help objectspec' para más información sobre
                        especificaciones de ítem.)
                        
== CMD_HELP_LOCK_CREATE ==
Notas:

    - Sólo el administrador del servidor puede crear bloqueos.
    - Todos los bloqueos creados con este comando son inicializados en el
      estado Retained.
    - Si la revisión cargada en la rama especificada (por cada elemento
      especificado) coincide con la revision cargada en la rama de destino
      (/main por defecto), el lock no será creado.
    - El repositorio de la branchspec debe ser el mismo que el de todos las
      itemspecs especificadas.

Ejemplos:

    cm lock create /main/task@myrep itemid:56@myrep
    (Crea bloqueo para el ítem id 56 en la rama /main/task@myrep.)

    cm lock create br:/main/task item:/workspace/foo.psd item:/workspace/bar.psd
    (Crea bloqueos para los items especificados en la rama /main/task.)

== CMD_DESCRIPTION_UPDATE ==
Actualiza el espacio de trabajo.

== CMD_USAGE_UPDATE ==
Sintaxis:
    cm update [<ruta> | --last]
              [--changeset=csetspec] [--cloaked] [--dontmerge] [--forced]
              [--ignorechanges] [--override] [--recursewk] [--skipchangedcheck]
              [--silent] [--verbose] [--xml[=output_file]] [--encoding=name]
              [--machinereadable [--startlineseparator=<sep>]
                [--endlineseparator=<sep>] [--fieldseparator=<sep>]]
              [--forcedetailedprogress] [--noinput]

    ruta                    Ruta a actualizar.
                            Use . para aplicar la actualización al directorio actual.
                            Si no se especifica ninguna ruta, se actualiza todo
                            el workspace.
    --last                  Antes de la actualización, cambia el selector del
                            espacio de trabajo (workspace) desde una configuración
                            de changeset o de etiqueta a una configuración de
                            rama.
                            El selector se cambia a la rama a la que pertenece el
                            changeset o la etiqueta.

Opciones:
                        
    --changeset             Actualiza el espacio de trabajo a un changeset concreto.
                            (Use 'cm help objectspec' para obtener más información
                            sobre la especificación de changesets.)
    --cloaked               Incluye los ítems cloaked en la operación de update.
                            Si no se utiliza esta opción, se ignorarán durante
                            la actualización todos los ítems cloaked.
    --dontmerge             El update merge no se llevará cabo en el caso en que
                            se requiera esa operación.
    --forced                Fuerza la actualización de todos los ítems a aquella
                            revisión especificada en el selector.
    --ignorechanges         Si existen cambios pendientes durante la actualización
                            del espacio de trabajo, se ignorarán y se continuará
                            con la actualización.
    --noinput               Ignora las preguntas interactivas para continuar la
                            operación con cambios pendientes o haciendolos shelve.
                            Usar esta opción deshabilita la posibilidad de llevar
                            cambios pendientes.
    --override              Ignoran los ficheros cambiados fuera del control de
                            Unity VCS. Su contenido se sobrescribirá con el
                            contenido del servidor.
    --recursewk             Actualiza todos los espacios de trabajo de la ruta
                            actual. Esto es útil para actualizar todos los 
                            espacios de trabajo contenidos en la ruta indicada.
    --skipchangedcheck      Comprueba si hay cambios locales en el workspace
                            antes de actualizar. Esta opción es útil para
                            acelerar la actualización cuando siempre se
                            desprotegen (checkout) ficheros antes de
                            modificarlos.
    --silent                Solo se imprimen mensajes en caso de error.
                            Usar esta opción deshabilita la posibilidad de llevar
                            cambios pendientes.
    --verbose               Muestra información adicional.
    --xml                   Imprime el resultado en formato XML a la salida
                            estándar. También es posible especificar un fichero
                            de salida.
                            Usar esta opción deshabilita la posibilidad de llevar
                            cambios pendientes.
    --encoding              Usado con la opción --xml, especifica el encoding
                            que se usará en la salida XML, por ejemplo, utf-8.
                            Consulte la documentación de MSDN en
                            http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
                            para obtener la tabla de codificaciones soportadas y
                            su formato (al final de la página en la columna "Name").
    --machinereadable       Muestra el resultado en un formato fácil de parsear.
                            Usar esta opción deshabilita la posibilidad de llevar
                            cambios pendientes.
    --startlineseparator    Usado con '--machinereadable', indica cómo deben
                            empezar las líneas del resultado.
    --endlineseparator      Usado con '--machinereadable', indica cómo deben
                            terminar las líneas del resultado.
    --fieldseparator        Usado con '--machinereadable', indica cómo deben
                            separarse los campos de cada línea resultante.
    --forcedetailedprogress Fuerza mostrar el progreso detallado incluso cuando
                            se redirige la salida estándar.


== CMD_HELP_UPDATE ==
Notas:

    El comando 'update' solo descarga los ficheros necesarios.

    El comando asume recursividad.

    Cuando se especifica la opción --last, no es necesario especificar una ruta.
    En ese caso, el espacio de trabajo del directorio actual se actualizará.
    (Especificar la opción --last puede hacer que el selector del espacio de
    trabajo cambie a un configuración de rama si previamente apuntaba a un
    changeset o una etiqueta.)

Ejemplos:

    cm update
    (Actualiza el espacio de trabajo actual.)

    cm update .
    (Actualiza recursivamente el contenido del directorio actual.)

    cm update . --forced --verbose
    (Fuerza la actualización de todas las revisiones y muestra información
    adicional.)

    cm update --last

    cm update . --machinereadable --startlineseparator=">"
    (Actualiza recursivamente el directorio actual y muestra por pantalla el
    resultado en un formato simple y fácil de parsear, comenzando cada línea con
    el separador indicado.)

== CMD_DESCRIPTION_VERSION ==
Muestra el número de versión del cliente.

== CMD_USAGE_VERSION ==
Sintaxis:

    cm version

== CMD_HELP_VERSION ==

== CMD_DESCRIPTION_WHOAMI ==
Muestra el usuario actual de Unity VCS.

== CMD_USAGE_WHOAMI ==
Sintaxis:

    cm whoami

== CMD_HELP_WHOAMI ==

== CMD_USAGE_WKTREENODESTATUS ==
Uso:

    cm wktreenodestatus path1, path2, ...

== CMD_DESCRIPTION_WORKSPACE ==
Use este comando para administrar workspaces.

== CMD_USAGE_WORKSPACE ==
Sintaxis:

    cm workspace | wk comando [options]

Comandos:

    list   | ls
    create | mk
    delete | rm
    move   | mv
    rename

    Para obtener más información sobre cada comando:
    cm workspace comando --usage
    cm workspace comando --help

== CMD_HELP_WORKSPACE ==
Ejemplos:

    cm workspace create myWorkspace wk_path
    cm workspace list
    cm workspace delete myWorkspace

== CMD_DESCRIPTION_WORKSPACE_CREATE ==
Crea un nuevo workspace.

== CMD_USAGE_WORKSPACE_CREATE ==
Sintaxis:

    cm workspace | wk [create | mk] <rep_spec>
                      [create | mk] <nombre> <ruta> [<rep_spec>]
                      [create | mk] <nombre> <ruta> [--selector[=<fichero_selector>]
    (Crea un nuevo workspace.)

    cm workspace | wk [create | mk] <nombre> <ruta> --dynamic --tree=[<tree>]
    (Crea un workspace dinámico. Esta característica es experimental,
    y solamente se encuentra disponible en Windows.)

    nombre              Nombre del nuevo workspace.
    ruta                Ruta de acceso del workspace.
    rep_spec            Crea el nuevo workspace con el repositorio especificado.
                        Use 'cm help objectspec' para obtener más
                        información sobre especificaciones de repositorios.

Opciones:

    --selector          Edita el selector para el nuevo workspace.
                        Si se especifica selector-file, entonces el selector 
                        para el nuevo workspace se fiha desde el fichero
                        especificado.
    --dynamic           Crea un workspace dinámico. Esta funcionalidad es
                        todavía experimental, y solo está disponible para
                        Windows. Si se usa esta opción, es obligatorio usar el
                        parámetro --tree.
    --tree              Especifica el punto inicial al que apuntará el
                        workspace dinámico. Puede ser una especificación de
                        rama, changeset o etiqueta. El workspace continuará
                        usando el repositorio de la especificación. Use
                        'cm help objectspec' para obtener más información sobre
                        la especificación.

== CMD_HELP_WORKSPACE_CREATE ==
Notas:

    - Un workspace es una vista del repositorio mapeado en el sistema de
      ficheros local, en un directorio especificado. Utilice los comandos
      'cm showselector' y 'cm setselector' para ver y modificar el selector.
    - Si no se especifica rep_spec ni --selector, el workspace cargará
      automáticamente el contenido del primer repositorio (alfabéticamente) del
      servidor configurado en el fichero de configuración client.conf
    - Los workspaces dinámicos son una funcionalidad experimental (sólo Windows),
      y requiere que el programa plasticfs.exe se esté ejecutando en segundo
      plano.

Ejemplos:

    cm workspace create mycode
    cm wk mk mycode
    (Crea un workspace llamado 'mycode' apuntando al repositorio del mismo nombre.
    El directorio del workspace se creará bajo el directorio actual.)

    cm wk mk mycode@localhost:8084
    cm wk mk mycode@myorganization@cloud
    (Crea un workspace llamado 'mycode' igual que antes, pero es posible especificar
    un servidor de repositorios diferente.)

    cm workspace create myworkspace c:\workspace
    cm wk mk myworkspace /home/<USER>/wkspace
    (Crea el workspace 'myworkspace' en 'c:\workspace' y '/home/<USER>/wkspace'
    respectivamente.)

    cm wk mywktest c:\wks\wktest --selector=myselector.txt
    (Crea 'mywktest' utilizando como selector 'myselector.txt'.)

    cm wk mywkprj c:\wks\wkprj myrep@repserver:localhost:8084
    (Crea 'mywkprj' en 'c:\wks\wkprj' con el repositorio seleccionado.)

    cm wk mywkprj c:\dynwks\mywkprj --dynamic --tree=br:/main@myrep@localhost:8084
    (Crea el workspace dinámico 'mywkprj' en 'c:\dynwks\mywkprj' con el
     repositorio 'myrep@localhost:8084', apuntando a la rama '/main' la primera
     vez que se monte.)

== CMD_DESCRIPTION_WORKSPACE_DELETE ==
Borra un workspace.

== CMD_USAGE_WORKSPACE_DELETE ==
Sintaxis:

    cm workspace | wk delete | rm [ruta | wkspec] [--keepmetadata]

    ruta        Ruta del workspace.
    wkspec      Especificación del workspace a borrar.
                Use 'cm help objectspec' para obtener más información sobre la 
                especificación de espacios de trabajo.

Opciones:

    --keepmetadata:     No elimina los archivos de metadatos del directorio
                        .plastic.

== CMD_HELP_WORKSPACE_DELETE ==
Notas:

    Si no se especifican parámetros, se borrará el workspace actual.

Ejemplos:

    cm workspace delete 
    (Elimina el workspace actual)

    cm wk delete c:\workspace
    cm workspace rm /home/<USER>/wks
    cm wk rm wk:MiWorkspace
    cm wk rm wk:MiWorkspace@DIGITALIS

== CMD_DESCRIPTION_WORKSPACE_LIST ==
Muestra la lista de workspaces.

== CMD_USAGE_WORKSPACE_LIST ==
Sintaxis:

    cm workspace | wk [list | ls] [--format=str_format]

Opciones:

    --format            Devuelve el mensaje de respuesta con el formato
                        especificado. Consulte la ayuda para más información.

== CMD_HELP_WORKSPACE_LIST ==
Notas:

    Este comando acepta una cadena de texto formateada para mostrar la
    salida.

    Los parámetros de salida del comando son los siguientes:

        {0} | {wkname}        Nombre del workspace.
        {1} | {machine}       Nombre de la máquina.
        {2} | {path}          Ruta del workspace.
        {3} | {wkid}          Identificador único del workspace.
        {4} | {wkspec}        Especificación del workspace en el formato:
                              'wkname@machine'.
        {tab}                 Inserta un tabulador.
        {newline}             Inserta una nueva línea.

Ejemplos:

    cm wk
    (Lista todos los workspaces.)

    cm workspace list --format={0}#{3,40}
    cm workspace list --format={wkname}#{wkid,40}
    (Lista todos los workspaces mostrando el nombre del workspaces, el
    símbolo '#' y el identificador del workspace utilizando 40 espacios y
    alineado a la izquierda.)

    cm wk --format="Workspace {0} in path {2}"
    cm wk --format="Workspace {wkname} in path {path}"
    (Lista todos los workspaces, mostrando los resultados en cadenas
    formateadas.)

== CMD_DESCRIPTION_WORKSPACE_MOVE ==
Mueve un workspace.

== CMD_USAGE_WORKSPACE_MOVE ==
Sintaxis:

    cm workspace | wk move | mv [wkspec] ruta

Opciones:

    wkspec          Especificación del workspace a ser movido.
                    Use 'cm help objectspec' para obtener más información sobre la 
                    especificación de espacios de trabajo.
    ruta            La ruta destino a la que mover el workspace.

== CMD_HELP_WORKSPACE_MOVE ==
Notas:

    Este comando permite al usuario mover workspace a otro lugar en disco.

Ejemplos:

    cm workspace move myWorkspace \new\workspaceDirectory
    (Mueve el workspace a la ruta especificada.)

    cm wk mv C:\wkspaces\newpath
    (Mueve el workspace de trabajo a 'C:\wkspaces\newpath')

== CMD_DESCRIPTION_WORKSPACE_RENAME ==
Cambia el nombre de un workspace.

== CMD_USAGE_WORKSPACE_RENAME ==
Sintaxis:

    cm workspace | wk rename [nombre_antiguo] nombre_nuevo

    nombre_antiguo      Nombre antiguo del workspace.
    nombre_nuevo        Nombre nuevo del workspace.

== CMD_HELP_WORKSPACE_RENAME ==
Notas:

   Este comando renombra un workspace. Si no se especifica nombre_antiguo,
   el sistema tomará el nombre del workspace actual.

Ejemplos:

   cm workspace rename miworkspace wk2
   (El workspace 'miworkspace' se renombra a 'wk2'.)

   cm wk rename nuevoNombre
   (El workspace actual se renombra a 'nuevoNombre'.)

== CMD_DESCRIPTION_WORKSPACESTATUS ==
Muestra el changeset cargado en un workspace y si los hay los elementos cambios que contiene.

== CMD_USAGE_WORKSPACESTATUS ==
Sintaxis:

    cm status [workspacepath] [opciones] [tipos_de_búsqueda]

    workspacepath: El path de un workspace determinado o una ruta por debajo
        del mismo. La búsqueda de cambiados se realizará a partir del path
        especificado y de manera recursiva.

Opciones generales:

    --changelist=NAME: Muestra los cambios en el 'changelist' llamado "NAME".
    --changelists: Muestra los cambios agrupados en 'changelists'.
    --cutignored: Evita mostrar los contenidos de los directorios ignorados.
        Requiere el tipo de búsqueda --ignored.
    --header: Solo imprimirá la información del changeset cargado en el
        workspace, no realizará la búsqueda de elementos cambiados.
    --noheader: El comando no imprimirá el estado del workspace, sólo
        realizará e imprimirá la búsqueda de elementos cambiados.
    --nomergesinfo: La información de los merges de los cambios no se imprimirá.
    --head: El comando imprimirá el estado del último changeset de la rama.
    --short: Muestra únicamente rutas de elementos que tienen cambios.
    --symlink: Aplica la operación a los enlaces simbólicos en lugar de
        aplicarla a sus destinos.
    --dirwithchanges: Muestra directorios que contienen cambios (ítems
        añadidos, movidos o eliminados en su interior).
    --xml: Permite volcar la salida del comando en formato xml. Por defecto la
        salida sale en la pantalla, pero si se especifica un fichero
        (--xml=output.xml) la salida se vuelca en dicho fichero.
    --pretty: Imprime los cambios del workspace en formato de tabla.
    --encoding: si se utiliza con la opción --xml, permite especificar el
        encoding con el que se guardará la salida del comando. Consulte la
        documentación de la MSDN para obtener la tabla completa de
        codificaciones soportadas y su formato; al final de la página en la
        columna "Name":
        http://msdn.microsoft.com/en-us/library/system.text.encoding.aspx
    --wkrootrelativepaths, --wrp: Muestra rutas relativas a la raíz del
        workspace en vez de rutas relativas al directorio actual.
    --fullpaths, --fp: Fuerza al comando a imprimir rutas absolutas,
        sobreescribiendo cualquier otra configuración acerca de impresión
        de rutas.
    --iscochanged: Incluye información sobre si el contenido de un fichero en
        checkout ha sido o modificado o no. Esta opción sólo está disponible en
        combinación con las opciones "--compact", "--xml" y 
        "--machinereadable".
        Si no se especifica la opción "--iscochanged", las opciones 
        "--compact", "--xml" o "--machinereadable" sólamente mostrarán el 
        estado "CO" para un fichero en checkout cuyo contenido ha cambiado.
        Si se especifica la opción "--iscochanged", las opciones "--compact",
        "--xml" o "--machinereadable" mostrarán el estado "CO+CH" para un 
        fichero en checkout cuyo contenido ha cambiado.

Opciones del viejo formato:

    --cset: El comando imprimirá el estado del workspace en el viejo formato.
    --compact: El comando imprimirá el estado del workspace y la búsqueda de
        elementos cambiados en el viejo formato.
    --noheaders: Usado junto con --compact, la información de cada grupo de
        cambiós no se imprimirá. (No aplica al nuevo formato.)

Tipos de búsqueda:

    --added: El comando busca elementos añadidos.
    --checkout: El comando busca elementos en checkout.
    --changed: El comando busca elementos cambiados.
    --copied: El comando busca elementos copiados.
    --replaced: El comando busca elementos reemplazados.
    --deleted: El comando busca elementos borrados.
    --localdeleted: El comando busca elementos borrados localmente.
    --moved: El comando busca elementos movidos.
    --localmoved: El comando busca elementos movidos localmente.
    --percentofsimilarity (-p)=valor: Porcentaje de similitud entre dos
      ficheros para considerarlos el mismo en la búsqueda de movidos
      locales, su valor por defecto es un 20%.
    --txtsameext: Sólo aquellos ficheros de texto que tengan la misma
        extensión serán tenidos en cuenta para la búsqueda de movidos basada
        en la similitud del contenido. Por defecto todos los ficheros de texto
        son procesados.
    --binanyext: Cualquier fichero binario es tenido en cuenta para la
        búsqueda de movidos basada en la similitud del contenido. Por defecto
        sólo aquellos ficheros binarios que tengan la misma extensión
        son procesados.
    --private: El comando busca elementos no controlados.
    --ignored: El comando busca elementos ignorados.
    --hiddenchanged: El comando busca elementos cambiados marcados como
        ocultos (incluye '--changed').
    --cloaked: El comando busca elementos marcados como cloaked.
    --controlledchanged: Este modificador engloba los siguientes parámetros:
        '--added' '--checkout' '--copied' '--replaced' '--deleted' '--moved'.
    --all (-a): Este modificador engloba los siguientes parámetros:
        '--controlledchanged' '--changed' '--localdeleted' '--localmoved'
        '--private'.

== CMD_HELP_WORKSPACESTATUS ==
Notas:

    El comando status muestra el changeset cargado en un workspace
    y si los hay, los cambios que contiene.

    Se puede utilizar el comando para saber cuál es el changeset con el que se
    está trabajando en un momento dado, y poder utilizarlo, por ejemplo, para
    marcar configuraciones concretas mediante etiquetas de tipo changeset.

    También es útil para conocer los cambios pendientes que existen dentro
    del workspace, los tipos de cambios que deseamos obtener del workspace pueden
    ser modificados gracias a los parámetros que el comando admite. Por defecto
    se muestran todos los cambios, controlados y locales.

    El porcentaje de similitud entre dos ficheros usado por el algoritmo de
    cálculo de movidos locales se puede especicifar mediante la option '--percentofsimilarity'
    o '-p' y su rango va desde 0 hasta 100. Si este parámetro no es especidicado
    su valor por por defecto es del 20%.

    A través de éste comando, también es posible mostrar los cambios agrupados
    en 'changelists' de cliente. El changelist por defecto del sistema (llamado
    'default'), agrupa los cambios que no están contenidos en el resto de
    'changelists', por lo que los cambios que se muestran especificando éste
    'changelist', dependerán del tipo de búsquedas especificados en el comando.

    La opción de mostrar cambios agrupados en 'changelists' requiere mostrar cambios
    controlados (en estado 'added', 'checkout', 'copied', 'replaced', 'deleted' o
    'moved'), por lo que la opción '--controlledchanged' se activará automáticamente
    cuando se muestren 'changelists'.

    El formato por defecto para la salida en XML es UTF-8. Es posible cambiar
    dicho encoding usando la opción --encoding=otro_encoding.

    El comando 'status' imprime por defecto rutas relativas al directorio de trabajo,
    a menos que se especifiquen las opciones '--machinereadable' o '--short'
    (en cuyo caso, se imprimen rutas absolutas).

    Si se usa la opción '--xml', el comando 'status' imprimirá por defecto rutas
    relativas a la raíz del workspace, a menos que se especifique adicionalmente
    la opción '--fp' (en cuyo caso, se imprimen rutas absolutas).

Ejemplos:

    cm status
    (Ejecutándolo dentro de un workspace el comando imprime el changeset cargado
    y los elementos en checkout, añadidos, copiados, reemplazados, borrados, borrados locales,
    movidos, movidos locales, y privados. La busqueda de los elementos se realiza
    sobre el workspace de forma recursiva)

    cm status --controlledchanged
    (Ejecutándolo dentro de un workspace el comando imprime el changeset cargado
    y los elementos del workspace en checkout, añadidos, copiados, reemplazados,
    borrados, y movidos)

    cm status --added
    (Ejecutándolo dentro de workspace el comando imprime el changeset cargado
    y solamente los elementos añadidos. La búsqueda de los elementos se realiza
    sobre el workspace de forma recursiva)

    cm status c:\workspaceLocation\code\client --added
    (El comando imprime el changeset cargado y solamente los elementos añadidos.
    La búsqueda de los elementos se realiza sobre el path proporcionado de forma recursiva)

    cm status --changelists
    cm status --changelist
    (El comando muestra todos los cambios en el espacio de trabajo, agrupados
    por 'changelists').

    cm status --changelist=pending_to_review
    (El comando muestra los cambios que contiene el changelist 'pending_to_review')

    cm status --changelist=default --private
    (El comando muestra los cambios que contiene el changelist 'default',
    el changelist por defecto del sistema, mostrando elementos privados,
    además de elementos con cambios controlados si los hubiese).

    cm status --short --changelist=pending_to_review | cm checkin -
    (Se protegen los cambios en el changelist llamado 'pending_to_review').

    cm status c:\workspaceLocation\code\client --xml=output.xml
    (Guarda la salida del status en el fichero output.xml, en formato XML y
    usando UTF-8.)

    cm status --ignored
    (Muestra todos los elementos ignorados)
    Salida:
    IG src
    IG src/version.c
    IG out/app.exe

    cm status --ignored --cutignored
    (Muestra ficheros ignorados cuyo directorio padre no está ignorado y
    directorios ignorados pero no el árbol de sus contenidos)
    Salida:
    IG src
    IG out/app.exe

== CMD_DESCRIPTION_XLINK ==
Crea, edita o muestra un xlink

== CMD_USAGE_XLINK ==
Sintaxis:

    cm xlink [-w] [-rs] xlink_path / <csetspec | lbspec | brspec> [<expansion_rules>+]
    (Crea un xlink).

    cm xlink [-rs] xlink_path /relative_path <csetspec | lbspec | brspec> [<expansion_rules>+]
    (Crea un xlink parcial de solo lectura apuntando a /relative_path en lugar
    de apuntar a la raíz por defecto / ).

    cm xlink -e xlink_path <csetspec | lbspec | brspec>
    (Edita un xlink para cambiar la especificación destino).

    cm xlink -s|--show xlink_path
    (Muestra la información de un xlink incluyendo las reglas de expansión).

    cm xlink -ar|--addrules xlink_path <expansion_rules>+
    (Añade al xlink las reglas de expansión proporcionadas).

    cm xlink -dr|--deleterules xlink_path <expansion_rules>+
    (Borra del xlink las reglas de expansión especificadas).

    xlink_path          Ruta en el espacio de trabajo actual donde se cargan
                        los contenidos del repositorio que se va a enlazar
                        (cuando se crea un xlink) o que ya está enlazado
                        (cuando se edita un xlink).
    csetspec            Especificación del changeset que se va a enlazar.
                        Determina qué versión y rama se carga en el espacio de
                        trabajo para el repositorio enlazado.
                        Usa 'cm help objectspec' para obtener más información
                        sobre la especificación de changesets.
    lbspec              Especificación de la etiqueta que se va a enlazar.
                        Usa 'cm help objectspec' para obtener más información
                        sobre la especificación de etiquetas.
    brspec              Especificación de la rama que se va a enlazar.
                        Usa el changeset actual a donde la rama especificada
                        está apuntando.
                        Usa 'cm help objectspec' para obtener más información
                        sobre la especificación de ramas.
    -e                  Indica que el comando va a editar un xlink existente 
                        para cambiar la especificación del changeset enlazado.
    -s | --show         Muestra información sobre el xlink seleccionado.
    -ar | --addrules    Añade una o más reglas de expansión al xlink indicado.
    -dr | --deleterules Borra una o más reglas de expansión del xlink indicado.
    expansion_rules     Especifica una o más reglas de expansión. Cada regla de
                        expansión es un par (rama)-(rama a enlazar)
                        br:/main/fix-br:/main/develop/fix

Opciones:

  -w                    Crea un xlink de escritura. Esto significa que los 
                        contenidos bajo el xlink se podrán editar mediante el 
                        uso de autoexpansión de ramas.
  -rs                   Relative server. Permite crear un xlink a un repositorio
                        que es una réplica de otro que existe en un servidor 
                        central. De este modo se reconcilian automáticamente los
                        repositorios replicados con el original.

Ejemplos:

  cm xlink codigo\segundorepositorio / 1@segundo@localhost:8084
  cm xlink codigo\segundorepositorio / lb:LB001@segundo@localhost:8084
  cm xlink -s codigo\segundorepositorio
  cm xlink -ar codigo\segundorepositorio br:/main-br:/main/develop br:/main/fix-br:/main/develop/fix
  cm xlink -dr codigo\segundorepositorio br:/main/fix-br:/main/develop/fix

== CMD_HELP_XLINK ==
Notas:

    Este comando crea un xlink al changeset especificado. Por defecto se
    creará como un xlink de sólo lectura. Esto significa que los contenidos
    correspondientes a otro repositorio cargados por debajo del xlink no
    podrán ser modificados.

    Para la edición del changeset de un xlink se puede utilizar una sintaxis
    reducida en la que no hay que especificar el resto de parámetros del
    xlink. Solamente es necesario en nuevo changeset al que apuntará.

    Auto-expansión de ramas:

    Cuando se realiza un cambio en uno de los repositorios enlazados en modo
    de lectura y escritura (opción -e) es necesario crear una nueva rama en
    dicho repositorio basándose en la rama en la que se está trabajando en el
    repositorio principal. Para determinar el nombre de dicha rama, se aplican
    las siguientes reglas:
      1) Se comprueba si el nombre completo de la rama existe en el
    repositorio enlazado:
      - Si existe, se utiliza dicha rama.
      - Si no existe, Unity VCS construye el nombre de la rama de esta forma:
          - Nombre de la rama que contiene el changeset enlazado + nombre 
            de la rama del repositorio principal.
          - Si existe una rama con dicho nombre, se utiliza como rama de 
            trabajo.
          - Si no, se crea una rama nueva con el nombre así construido y 
            se establece como base de la rama el changeset enlazado.
      2) Después, se crea una nueva versión del xlink dentro de la nueva rama
         apuntando al nuevo changeset. 

    De este modo, la estructura completa del xlink se mantiene actualizada con 
    los últimos cambios en las versiones correctas.

Ejemplos:

    cm xlink code\firstrepo / 1@first@localhost:8084
    (Crea un xlink en la carpeta 'firstrepo' en el espacio de trabajo actual
     donde el changeset '1' en el repositorio 'first' se enlazará).

    cm xlink opengl\include /includes/opengl 1627@includes@localhost:8087
    (Crea un xlink parcial de solo lectura en la ruta 'opengl\include' en el
     espacio de trabajo actual donde la ruta '/includes/opengl' en el changeset 
     '1627' del repositorio 'includes' se enlazará como raíz. Esto significa
     que cualquier ítem dentro de '/includes/opengl' se enlazará en 
     'opengl\include' mientras que el resto del repositorio se ignorará).

    cm xlink -w -rs code\secondrepo / lb:LB001@second@localhost:8084
    (Crea un xlink de escritura y relativo en la ruta 'secondrepo' en el
     espacio de trabajo actual donde la etiqueta'LB001' en el repositorio
     'second' se enlazará).

    cm xlink code\thirdrepo / 3@third@localhost:8087 br:/main-br:/main/scm003
    (Crea un xlink en la ruta 'thirdrepo' dentro del espacio de trabajo actual
     donde el changeset '3' en el repositorio 'third' se enlazará).

    cm xlink -e code\secondrepo br:/main/task1234@second@localhost:8084
    (Edita el xlink 'code\secondrepo' para cambiar el repositorio destino 
     enlazando la rama 'main/task1234' en el repositorio 'second').

    cm xlink --show code\thirdrepo
    (Muestra información del xlink 'code\thirdrepo' incluyendo las reglas de 
     expansión si éstas existen).

    cm xlink -ar code\secondrepo br:/main-br:/main/develop br:/main/fix-br:/main/develop/fix
    (Añade dos reglas de expansión al xlink 'code\secondrepo').

    cm xlink -dr code\secondrepo br:/main/fix-br:/main/develop/fix
    (Borra la regla de expansión del xlink 'code\secondrepo').

    cm xlink code\secondrepo / 1@second@localhost:8084
    cm xlink code\secondrepo / lb:LB001@second@localhost:8084

== CMD_USAGE_AUTOCOMPLETE ==
Sintaxis:

    cm autocomplete install
     Instala el autocompletado de comandos para 'cm' en la shell.

    cm autocomplete uninstall
     Desinstala el autocompletado de comandos para 'cm' de la shell.

    cm autocomplete --line <shell_line> --position <cursor_position>
     Devuelve sugerencias de autocompletado para 'shell_line', que deben ser
     insertadas en 'cursor_position'. Este comando no está pensado para ser
     ejecutado por el usuario final, pero está documentado en caso de que se
     quiera extender el soporte de autocompletado a otras shells.

    shell_line      La linea que el usuario ha escrito en la shell cuando se
                    pidió el autocompletado.
                    En Bash, encuentra en la variable de entorno COMP_LINE.
                    En PowerShell, se encuentra en la variable $wordToComplete.
    cursor_position La posición del cursor cuando se pidió el autocompletado.
                    En Bash, se encuentra en la variable de entorno COMP_POINT.
                    En PowerShell, se encuentra en la variable $cursorPoint.

== CMD_DESCRIPTION_API ==
Arranca un servidor HTTP local que escucha peticiones de REST API.

== CMD_USAGE_API ==
Sintaxis:

    cm api [(-p|--port)=<portnumber>] [(-r|--remote)]

Opciones:

    -p | --port         Pide al servidor escuchar en el puerto <portnumber>
                        en lugar del 9090.

    -r | --remote       Permite conexiones entrantes remotas, es decir,
                        aquellas que provienen de otras máquinas en vez de
                        la local.

== CMD_HELP_API ==
Notas:

    El comando 'cm api' permite a los programadores realizar operaciones Unity VCS
    de cliente en sus máquinas.
    Lea la Guía de Unity VCS API para más información:
    https://www.plasticscm.com/documentation/restapi/plastic-scm-version-control-rest-api-guide

    Por defecto, la API escucha solamente conexiones locales en el puerto 9090.

    Para detener el servidor, presione la tecla Enter.

Ejemplos:

    cm api
    (Arranca la API en el puerto 9090; únicamente conexiones locales.)

    cm api -r
    (Arranca la API en el puerto 9090; permite cualquier conexión entrante.)

    cm api --port=15000 -r
    (Arranca la API en el puerto 15000; permite cualquier conexión entrante.)

== CMD_DESCRIPTION_CONFIGURECLIENT ==
Configura el cliente de Unity VCS y especificar un servidor por defecto para el usuario actual.

== CMD_USAGE_CONFIGURECLIENT ==
Sintaxis:
    cm configure [--language=<idioma> --workingmode=<modo> [AuthParameters] 
                 --server=<servidor> [--port=<puerto>]] [--clientconf=<ruta_clientconf>]

    --language          Idiomas disponibles:
                        en (English)
                        es (Spanish)

    --workingmode       Modos de seguridad/usuarios disponibles:
                        NameWorkingMode (Name)
                        NameIDWorkingMode (Name + ID)
                        LDAPWorkingMode (LDAP)
                        ADWorkingMode (Active Directory)
                        UPWorkingMode (User and password)
                        SSOWorkingMode (Single Sign On)
                        
    AuthParameters      Parámetros de autenticación (solo para modos LDAPWorkingMode y UPWorkingMode):
                        --user=<usuario>
                        --password=<contraseña>

                        Parámetros de Single Sign On (solo para modos SSOWorkingMode):
                        --user=<usuario>
                        --token=<token>
                        
    --server            Nombre/dirección IP del servidor de Unity VCS

    --port              Puerto del servidor de Unity VCS
                        (el puerto es opcional para servidores de Cloud)

    --clientconf        Ruta del fichero de configuración a crear (opcional)
                        Este argumento puede ser una ruta completa, un nombre de fichero o un directorio.
                        Si no se especifica este parámetro, el directorio por defecto para el fichero
                        de configuración 'client.conf' será:
                        * '%LocalAppData%\plastic4' en Windows
                        * '$HOME/.plastic4' en linux/macOS

                        Ejemplos:
                        
                        --clientconf=c:/path/to/myclient.conf
                        (La ruta especificada se utilizará para crear el fichero de configuración del cliente)

                        --clientconf=myclient.conf
                        (Se utilizará el fichero 'myclient.conf' dentro del directorio de configuración por defecto)

                        --clientconf=c:/exisitingDirectory
                        (Se utilizará el nombre de fichero por defecto, client.conf, en el directorio especificado)

== CMD_HELP_CONFIGURECLIENT ==
Notas:

    El comando 'cm configure' no podrá ser usado en las versiones Cloud Edition o DVCS Edition de Unity VCS.
    Use de forma alternativa la aplicación 'plastic --configure'.
    
Ejemplos:

    cm configure 
    (ejecuta la versión interactiva del comando de configuración del cliente de Unity VCS)

    cm configure --language=en --workingmode=LDAPWorkingMode --user=jack --password=01234 \
                  --server=plastic.mymachine.com --port=8084
    (configura el cliente de Unity VCS con los parámetros especificados, y creará el fichero
    de configuración 'client.conf' en el directorio por defecto).

    cm configure --language=en --workingmode=NameWorkingMode --server=plastic.mymachine.com \
                  --port=8084 --clientconf=clientconf_exp.conf
    (configura el cliente de Unity VCS con los parámetros especificados, y creará el fichero
    de configuración 'client.conf' en el directorio especificado).

== CMD_DESCRIPTION_PURGE ==
Este comando permite al usuario inspeccionar, registrar y ejecutar purgas sobre un repositorio. Las revisiones purgadas ya no son accesibles para el repositorio en cuestión, ayudando de esta manera a liberar espacio.

ADVERTENCIA: Las acciones de purga son irreversibles. Una vez son ejecutadas, ya NO podrá cargar las revisiones purgadas - ya sea cambiando su workspace o al mostrar las diferencias de una rama o changeset.

== CMD_USAGE_PURGE ==
Sintaxis:
    cm purge <comandos> [opciones]

Comandos:

    - register
    - execute
    - show
    - history
    - unregister

    Para obtener más información sobre cada comando:
    cm purge <command> --^usage
    cm purge <command> --^help

== CMD_HELP_PURGE ==
Examples:

    cm purge register ".mkv" "1955-Nov-05 6:00 AM" --repository=timemachine
    (registra una acción de purga para el repositorio 'timemachine')

    cm purge history
    (enumera el GUID y el statu de todas las acciones de purga registradas
    anteriormente en el servidor)

    cm purge show 545ec81b-23ea-462c-91f4-d7c62a6e8817 --verbose
    (muestra en detalle los metadatos de la acción de purga dado su ID,
    incluyendo los elementos y revisiones afectados por la purga)

    cm purge execute 545ec81b-23ea-462c-91f4-d7c62a6e8817
    (inicia una acción de purga previamente registrada)

    cm purge unregister 545ec81b-23ea-462c-91f4-d7c62a6e8817
    (las purgas que no hayan sido ejecutadas pueden eliminarse del registro)

== CMD_DESCRIPTION_PURGE_REGISTER ==
Calcula y registra una acción de purga. Se considerarán candidatas para la purga aquellas revisiones anteriores a la(s) fecha(s) seleccionadas durante el registro. Como regla, siempre preservamos revisiones etiquetadas así como al menos una revisión en todas las cabezas de rama. Puede utilizar el comando "cm purge show" para verificar las revisiones seleccionadas antes de ejecutar la purga mediante el comando "cm purge execute".

== CMD_USAGE_PURGE_REGISTER ==
Sintaxis:
    cm purge register (<extension> <before_date>)... [--repository=<rep_spec>]

Opciones:

    --repository          El repositorio donde se pretende aplicar la purga.
                          No es necesario bajo de la ruta de un espacio de trabajo salvo para
                          elegir uno diferente.
    extensión             Extensión del tipo de fichero a ser purgado. Note que las revisiones de
                          elementos renombrados se considerarán también para su eliminación.
    antes_de_fecha        Sólo aquellas revisiones anteriores a esta fecha se considerarán para
                          su eliminación.

== CMD_HELP_PURGE_REGISTER ==
Ejemplos:

    cm purge register ".mkv" "1955-Nov-05 6:00 AM"
    (registra una acción de purga sobre las revisiones de ficheros Matroska creadas antes de una fecha)

    cm purge register ".mkv" "6:00 AM"
    (puede especificar sólamente la fecha o la hora)

    cm purge register ".mkv" "6:00Z"
    (puede especificar una hora UTC en vez de una hora local)

    cm purge register ".mkv" "1955-Nov-05" --repository=timemachine
    (puede especificar un repositorio diferente para registrar la purga)

    cm purge register ".mkv" "1955-Nov-05" ".mp3" "1955-Nov-12"
    (puede proporcionar varias parejas de extensión y fecha a la vez)

== CMD_DESCRIPTION_PURGE_EXECUTE ==
Ejecuta una acción de purga registrada previamente.

ADVERTENCIA: Las acciones de purga son irreversibles. Una vez son ejecutadas, ya NO podrá cargar las revisiones purgadas - ya sea cambiando su workspace o al mostrar las diferencias de una rama o changeset. Use este comando bajo su propia responsabilidad.

Asegúrese de comprobar la historia del fichero para confirmar cuáles de las revisiones/changesets no son relevantes. No dude en utilizar el comando "cm purge show" antes de ejecutar la purga para verificar que ninguna revisión inesperada esté seleccionada para su eliminación.

== CMD_USAGE_PURGE_EXECUTE ==
Sintaxis:
    cm purge execute <purge_guid> [--server=<server>]

Opciones:

    --server              Permite especificar en que servidor se consultarán
                          las acciones de purga.
    purge_guid            El GUID devuelto por el comando "cm purge register".

== CMD_HELP_PURGE_EXECUTE ==
Ejemplos:

    cm purge execute be5b9145-1bd9-4c43-bd90-f2ff727bbf13
    (Ejecuta una purga dado su ID)

    cm purge execute be5b9145-1bd9-4c43-bd90-f2ff727bbf13 --server=myorg@cloud
    (Puede especificar un servidor concreto si así lo necesita)

== CMD_DESCRIPTION_PURGE_SHOW ==
Proporciona un informe del estado de la purga y sus contenidos.

== CMD_USAGE_PURGE_SHOW ==
Sintaxis:
    cm purge show <purge_guid> [--verbose | --server=<server>]

Opciones:

    --server              Permite especificar en que servidor se consultarán
                          las acciones de purga.
    --verbose             Expande el informe incluyendo los elementos y
                          revisiones involucrados en la purga.
    purge_guid            El GUID devuelto por el comando "cm purge register".

== CMD_HELP_PURGE_SHOW ==
Ejemplos:

    cm purge show be5b9145-1bd9-4c43-bd90-f2ff727bbf13
    (Proporciona un breve informe del estado de la purga)

    cm purge show be5b9145-1bd9-4c43-bd90-f2ff727bbf13 --verbose
    (Proporciona información adicional por extensión purgada,
    incluyendo elementos y revisiones)

    cm purge show be5b9145-1bd9-4c43-bd90-f2ff727bbf13 --server=myorg@cloud
    (Puede especificar un servidor diferente si así lo desea)

== CMD_DESCRIPTION_PURGE_HISTORY ==
Permite a los usuarios comprobar el estado de todas las purgas registradas en el servidor en algún momento.

También es útil para obtener algunos datos sobre ellas que se preservan para posibles consultas, tales como el autor, la fecha de ejecución o el tamaño del almacenamiento y los tipos afectados por la purga.

== CMD_USAGE_PURGE_HISTORY ==
Sintaxis:
    cm purge history [--verbose | --server=<server>]
                     [--sort=(desc|asc)]
                     [--skip=<skip> | --limit=<limit>]

Opciones:

    --server             Permite especificar en que servidor se consultarán
                         las acciones de purga.
    --verbose            Por omisión, sólo se muestra el ID y el estado para
                         cada purga. Esta opción también incluye información
                         más detallada.
    --sort               Especifica un orden para las purgas mostradas en el
                         resultado. Este puede ser descendente (desc) o
                         ascendente (asc).
    --skip               Especifica un número de entradas a omitir que no se
                         verán reflejadas en el resultado.
    --limit              Especifica el número máximo de entradas que se
                         mostrarán simultáneamente.

== CMD_HELP_PURGE_HISTORY ==
Ejemplos:

    cm purge history
    (muestra el ID y el estado para todas las purgas registradas en el servidor)

    cm purge history --server=stoltz@cloud
    (puede esplicitar un servidor para usar uno diferente si así lo necesita)

    cm purge history --verbose
    (incluye datos más detallados asociados a las purgas mostradas)

    cm purge history --sort=asc
    (muestra la historia empezando por las purgas más antiguas)

    cm purge history --skip=0  --limit=20
    cm purge history --skip=20 --limit=20
    cm purge history --skip=40 --limit=20
    (en lugar de mostrar todo a la vez, puede paginar los resultados)

== CMD_DESCRIPTION_PURGE_UNREGISTER ==
Las acciones de purga puede eliminarse del registro si decide no ejecutarlas.
Recuerde que no es posible eliminar aquellas purgas que ya se hayan iniciado.

== CMD_USAGE_PURGE_UNREGISTER ==
Sintaxis:
    cm purge unregister <purge_guid> [--server=<server>]

Opciones:

    --server              Permite especificar en que servidor se consultarán
                          las acciones de purga.
    purge_guid            El GUID devuelto por el comando "cm purge register".

== CMD_HELP_PURGE_UNREGISTER ==
Ejemplos:

    cm purge unregister 545ec81b-23ea-462c-91f4-d7c62a6e8817
    (las purgas que no hayan sido ejecutadas pueden eliminarse del registro)

    cm purge unregister 545ec81b-23ea-462c-91f4-d7c62a6e8817 --server=myorg@cloud
    (puede especificar un servidor diferente para eliminar la purga de su registro)
