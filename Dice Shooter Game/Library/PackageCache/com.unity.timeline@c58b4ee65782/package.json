{"name": "com.unity.timeline", "displayName": "Timeline", "version": "1.8.7", "unity": "2019.3", "keywords": ["unity", "animation", "editor", "timeline", "tools"], "description": "Use Unity Timeline to create cinematic content, game-play sequences, audio sequences, and complex particle effects.", "dependencies": {"com.unity.modules.director": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.particlesystem": "1.0.0"}, "relatedPackages": {"com.unity.timeline.tests": "1.8.7"}, "_upm": {"changelog": "### Added\n\n- Released ronl-workflow-custom-marker.md Added a new workflow to the Timeline Workflows documentation:\n- Released ronl-workflow-custom-marker.md The `Create a custom Notes marker` workflow demonstrates how to create a custom marker for adding notes to Timeline instances. This workflow also demonstrates how to change the default appearance of a custom marker with scripting and a Unity Style Sheet (USS).\n\n### Fixed\n\n- Fixed bug where using , and . (<>) to step frames in the Animation Window while the Timeline Window was linked would sometimes not work. [IN-56667](https://unity3d.atlassian.net/servicedesk/customer/portal/2/IN-56667)\n- When the Timeline and Animation windows are linked and the Timeline Window is active, moving the playhead in the Timeline Window will cause the animation window to repaint immediately."}, "upmCi": {"footprint": "48ee43890bac91d31a36af71d304183cb3070b5f"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.timeline@1.8/manual/index.html", "repository": {"url": "https://github.cds.internal.unity3d.com/unity/com.unity.timeline.git", "type": "git", "revision": "d6432ca638481c3d0e4c01f87f9cdbe3c4b1d529"}, "samples": [{"displayName": "Customization Samples", "description": "This sample demonstrates how to create custom timeline tracks, clips, markers and actions.", "path": "Samples~/Customization"}, {"displayName": "Gameplay Sequence Demo", "description": "This sample demonstrates how Timeline can be used to create a small in-game moment, using built-in Timeline tracks.", "path": "Samples~/GameplaySequenceDemo"}], "_fingerprint": "c58b4ee65782ad38338e29f7ee67787cb6998f04"}