#if BURST_INTERNAL || UNITY_BURST_EXPERIMENTAL_NEON_INTRINSICS
using System;
using System.Diagnostics;

namespace Unity.Burst.Intrinsics
{
    public unsafe static partial class Arm
    {
        public unsafe partial class Neon
        {
            /// <summary>
            /// Evaluates to true at compile time if Neon half precision floating point intrinsics are supported.
            /// </summary>
            public static bool IsNeonHalfFPSupported { get { return false; } }

            /// <summary>Floating-point Convert to lower precision Narrow (vector). This instruction reads each vector element in the SIMD&amp;FP source register, converts each result to half the precision of the source element, writes the final result to a vector, and writes the vector to the lower or upper half of the destination SIMD&amp;FP register. The destination vector elements are half as long as the source vector elements. The rounding mode is determined by the FPCR.The FCVTN instruction writes the vector to the lower half of the destination register and clears the upper half, while the FCVTN2 instruction writes the vector to the upper half of the destination register without affecting the other bits of the register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTN Vd.4H,Vn.4S</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcvt_f16_f32(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to lower precision Narrow (vector). This instruction reads each vector element in the SIMD&FP source register, converts each result to half the precision of the source element, writes the final result to a vector, and writes the vector to the lower or upper half of the destination SIMD&FP register. The destination vector elements are half as long as the source vector elements. The rounding mode is determined by the FPCR.
            /// <br/>Equivalent instruction: <c>FCVTN2 Vd.8H,Vn.4S</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcvt_high_f16_f32(v64 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to higher precision Long (vector). This instruction reads each element in a vector in the SIMD&amp;FP source register, converts each value to double the precision of the source element using the rounding mode that is determined by the FPCR, and writes each result to the equivalent element of the vector in the SIMD&amp;FP destination register.Where the operation lengthens a 64-bit vector to a 128-bit vector, the FCVTL2 variant operates on the elements in the top 64 bits of the source register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTL Vd.4S,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcvt_f32_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to higher precision Long (vector). This instruction reads each element in a vector in the SIMD&amp;FP source register, converts each value to double the precision of the source element using the rounding mode that is determined by the FPCR, and writes each result to the equivalent element of the vector in the SIMD&amp;FP destination register.Where the operation lengthens a 64-bit vector to a 128-bit vector, the FCVTL2 variant operates on the elements in the top 64 bits of the source register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTL2 Vd.4S,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcvt_high_f32_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Duplicate vector element to vector or scalar. This instruction duplicates the vector element at the specified element index in the source SIMD&amp;FP register into a scalar or each element in a vector, and writes the result to the destination SIMD&amp;FP register.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>DUP Hd,Vn.H[lane]</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">Lane index to a0. Must be an immediate in the range of [0..3]</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vget_lane_f16(v64 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Duplicate vector element to vector or scalar. This instruction duplicates the vector element at the specified element index in the source SIMD&amp;FP register into a scalar or each element in a vector, and writes the result to the destination SIMD&amp;FP register.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>DUP Hd,Vn.H[lane]</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">Lane index to a0. Must be an immediate in the range of [0..7]</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vgetq_lane_f16(v128 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Insert vector element from another vector element. This instruction copies the vector element of the source SIMD&amp;FP register to the specified vector element of the destination SIMD&amp;FP register.This instruction can insert data into individual elements within a SIMD&amp;FP register without clearing the remaining bits to zero.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>INS Vd.H[lane],Vn.H[0]</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <param name="a2">Lane index to a1. Must be an immediate in the range of [0..3]</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vset_lane_f16(f16 a0, v64 a1, Int32 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Insert vector element from another vector element. This instruction copies the vector element of the source SIMD&amp;FP register to the specified vector element of the destination SIMD&amp;FP register.This instruction can insert data into individual elements within a SIMD&amp;FP register without clearing the remaining bits to zero.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>INS Vd.H[lane],Vn.H[0]</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <param name="a2">Lane index to a1. Must be an immediate in the range of [0..7]</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vsetq_lane_f16(f16 a0, v128 a1, Int32 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Absolute value (vector). This instruction calculates the absolute value of each vector element in the source SIMD&amp;FP register, writes the result to a vector, and writes the vector to the destination SIMD&amp;FP register.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FABS Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vabsh_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Equal to zero (vector). This instruction compares each floating-point value from the first source SIMD&amp;FP registerand if the value is equal to zero sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.
            /// <br/>Equivalent instruction: <c>FCMEQ Hd,Hn,#0</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>UInt16</returns>
            [DebuggerStepThrough]
            public static UInt16 vceqzh_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Greater than or Equal to zero (vector). This instruction reads each floating-point value in the source SIMD&amp;FP register and if the value is greater than or equal to zero sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.
            /// <br/>Equivalent instruction: <c>FCMGE Hd,Hn,#0</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>UInt16</returns>
            [DebuggerStepThrough]
            public static UInt16 vcgezh_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Greater than zero (vector). This instruction reads each floating-point value in the source SIMD&amp;FP register and if the value is greater than zero sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.
            /// <br/>Equivalent instruction: <c>FCMGT Hd,Hn,#0</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>UInt16</returns>
            [DebuggerStepThrough]
            public static UInt16 vcgtzh_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Less than or Equal to zero (vector). This instruction reads each floating-point value in the source SIMD&amp;FP register and if the value is less than or equal to zero sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.
            /// <br/>Equivalent instruction: <c>FCMLE Hd,Hn,#0</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>UInt16</returns>
            [DebuggerStepThrough]
            public static UInt16 vclezh_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Less than zero (vector). This instruction reads each floating-point value in the source SIMD&amp;FP register and if the value is less than zero sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.
            /// <br/>Equivalent instruction: <c>FCMLT Hd,Hn,#0</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>UInt16</returns>
            [DebuggerStepThrough]
            public static UInt16 vcltzh_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Signed fixed-point Convert to Floating-point (vector). This instruction converts each element in a vector from fixed-point to floating-point using the rounding mode that is specified by the FPCR, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>SCVTF Hd,Hn</c></summary>
            /// <param name="a0">Int16 a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vcvth_f16_s16(Int16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Signed fixed-point Convert to Floating-point (vector). This instruction converts each element in a vector from fixed-point to floating-point using the rounding mode that is specified by the FPCR, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>SCVTF Hd,Hn</c></summary>
            /// <param name="a0">Int32 a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vcvth_f16_s32(Int32 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Signed fixed-point Convert to Floating-point (vector). This instruction converts each element in a vector from fixed-point to floating-point using the rounding mode that is specified by the FPCR, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>SCVTF Hd,Hn</c></summary>
            /// <param name="a0">Int64 a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vcvth_f16_s64(Int64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Unsigned fixed-point Convert to Floating-point (vector). This instruction converts each element in a vector from fixed-point to floating-point using the rounding mode that is specified by the FPCR, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>UCVTF Hd,Hn</c></summary>
            /// <param name="a0">UInt16 a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vcvth_f16_u16(UInt16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Unsigned fixed-point Convert to Floating-point (vector). This instruction converts each element in a vector from fixed-point to floating-point using the rounding mode that is specified by the FPCR, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>UCVTF Hd,Hn</c></summary>
            /// <param name="a0">UInt32 a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vcvth_f16_u32(UInt32 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Unsigned fixed-point Convert to Floating-point (vector). This instruction converts each element in a vector from fixed-point to floating-point using the rounding mode that is specified by the FPCR, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>UCVTF Hd,Hn</c></summary>
            /// <param name="a0">UInt64 a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vcvth_f16_u64(UInt64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed fixed-point, rounding toward Zero (vector). This instruction converts a scalar or each element in a vector from floating-point to fixed-point signed integer using the Round towards Zero rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTZS Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>Int16</returns>
            [DebuggerStepThrough]
            public static Int16 vcvth_s16_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed fixed-point, rounding toward Zero (vector). This instruction converts a scalar or each element in a vector from floating-point to fixed-point signed integer using the Round towards Zero rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTZS Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>Int32</returns>
            [DebuggerStepThrough]
            public static Int32 vcvth_s32_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed fixed-point, rounding toward Zero (vector). This instruction converts a scalar or each element in a vector from floating-point to fixed-point signed integer using the Round towards Zero rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTZS Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>Int64</returns>
            [DebuggerStepThrough]
            public static Int64 vcvth_s64_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned fixed-point, rounding toward Zero (vector). This instruction converts a scalar or each element in a vector from floating-point to fixed-point unsigned integer using the Round towards Zero rounding mode, and writes the result to the general-purpose destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTZU Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>UInt16</returns>
            [DebuggerStepThrough]
            public static UInt16 vcvth_u16_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned fixed-point, rounding toward Zero (vector). This instruction converts a scalar or each element in a vector from floating-point to fixed-point unsigned integer using the Round towards Zero rounding mode, and writes the result to the general-purpose destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTZU Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>UInt32</returns>
            [DebuggerStepThrough]
            public static UInt32 vcvth_u32_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned fixed-point, rounding toward Zero (vector). This instruction converts a scalar or each element in a vector from floating-point to fixed-point unsigned integer using the Round towards Zero rounding mode, and writes the result to the general-purpose destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTZU Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>UInt64</returns>
            [DebuggerStepThrough]
            public static UInt64 vcvth_u64_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed integer, rounding to nearest with ties to Away (vector). This instruction converts each element in a vector from a floating-point value to a signed integer value using the Round to Nearest with Ties to Away rounding mode and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTAS Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>Int16</returns>
            [DebuggerStepThrough]
            public static Int16 vcvtah_s16_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed integer, rounding to nearest with ties to Away (vector). This instruction converts each element in a vector from a floating-point value to a signed integer value using the Round to Nearest with Ties to Away rounding mode and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTAS Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>Int32</returns>
            [DebuggerStepThrough]
            public static Int32 vcvtah_s32_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed integer, rounding to nearest with ties to Away (vector). This instruction converts each element in a vector from a floating-point value to a signed integer value using the Round to Nearest with Ties to Away rounding mode and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTAS Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>Int64</returns>
            [DebuggerStepThrough]
            public static Int64 vcvtah_s64_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned integer, rounding to nearest with ties to Away (vector). This instruction converts each element in a vector from a floating-point value to an unsigned integer value using the Round to Nearest with Ties to Away rounding mode and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTAU Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>UInt16</returns>
            [DebuggerStepThrough]
            public static UInt16 vcvtah_u16_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned integer, rounding to nearest with ties to Away (vector). This instruction converts each element in a vector from a floating-point value to an unsigned integer value using the Round to Nearest with Ties to Away rounding mode and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTAU Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>UInt32</returns>
            [DebuggerStepThrough]
            public static UInt32 vcvtah_u32_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned integer, rounding to nearest with ties to Away (vector). This instruction converts each element in a vector from a floating-point value to an unsigned integer value using the Round to Nearest with Ties to Away rounding mode and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTAU Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>UInt64</returns>
            [DebuggerStepThrough]
            public static UInt64 vcvtah_u64_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed integer, rounding toward Minus infinity (vector). This instruction converts a scalar or each element in a vector from a floating-point value to a signed integer value using the Round towards Minus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTMS Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>Int16</returns>
            [DebuggerStepThrough]
            public static Int16 vcvtmh_s16_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed integer, rounding toward Minus infinity (vector). This instruction converts a scalar or each element in a vector from a floating-point value to a signed integer value using the Round towards Minus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTMS Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>Int32</returns>
            [DebuggerStepThrough]
            public static Int32 vcvtmh_s32_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed integer, rounding toward Minus infinity (vector). This instruction converts a scalar or each element in a vector from a floating-point value to a signed integer value using the Round towards Minus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTMS Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>Int64</returns>
            [DebuggerStepThrough]
            public static Int64 vcvtmh_s64_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned integer, rounding toward Minus infinity (vector). This instruction converts a scalar or each element in a vector from a floating-point value to an unsigned integer value using the Round towards Minus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTMU Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>UInt16</returns>
            [DebuggerStepThrough]
            public static UInt16 vcvtmh_u16_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned integer, rounding toward Minus infinity (vector). This instruction converts a scalar or each element in a vector from a floating-point value to an unsigned integer value using the Round towards Minus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTMU Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>UInt32</returns>
            [DebuggerStepThrough]
            public static UInt32 vcvtmh_u32_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned integer, rounding toward Minus infinity (vector). This instruction converts a scalar or each element in a vector from a floating-point value to an unsigned integer value using the Round towards Minus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTMU Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>UInt64</returns>
            [DebuggerStepThrough]
            public static UInt64 vcvtmh_u64_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed integer, rounding to nearest with ties to even (vector). This instruction converts a scalar or each element in a vector from a floating-point value to a signed integer value using the Round to Nearest rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTNS Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>Int16</returns>
            [DebuggerStepThrough]
            public static Int16 vcvtnh_s16_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed integer, rounding to nearest with ties to even (vector). This instruction converts a scalar or each element in a vector from a floating-point value to a signed integer value using the Round to Nearest rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTNS Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>Int32</returns>
            [DebuggerStepThrough]
            public static Int32 vcvtnh_s32_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed integer, rounding to nearest with ties to even (vector). This instruction converts a scalar or each element in a vector from a floating-point value to a signed integer value using the Round to Nearest rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTNS Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>Int64</returns>
            [DebuggerStepThrough]
            public static Int64 vcvtnh_s64_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned integer, rounding to nearest with ties to even (vector). This instruction converts a scalar or each element in a vector from a floating-point value to an unsigned integer value using the Round to Nearest rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTNU Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>UInt16</returns>
            [DebuggerStepThrough]
            public static UInt16 vcvtnh_u16_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned integer, rounding to nearest with ties to even (vector). This instruction converts a scalar or each element in a vector from a floating-point value to an unsigned integer value using the Round to Nearest rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTNU Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>UInt32</returns>
            [DebuggerStepThrough]
            public static UInt32 vcvtnh_u32_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned integer, rounding to nearest with ties to even (vector). This instruction converts a scalar or each element in a vector from a floating-point value to an unsigned integer value using the Round to Nearest rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTNU Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>UInt64</returns>
            [DebuggerStepThrough]
            public static UInt64 vcvtnh_u64_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed integer, rounding toward Plus infinity (vector). This instruction converts a scalar or each element in a vector from a floating-point value to a signed integer value using the Round towards Plus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTPS Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>Int16</returns>
            [DebuggerStepThrough]
            public static Int16 vcvtph_s16_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed integer, rounding toward Plus infinity (vector). This instruction converts a scalar or each element in a vector from a floating-point value to a signed integer value using the Round towards Plus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTPS Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>Int32</returns>
            [DebuggerStepThrough]
            public static Int32 vcvtph_s32_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed integer, rounding toward Plus infinity (vector). This instruction converts a scalar or each element in a vector from a floating-point value to a signed integer value using the Round towards Plus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTPS Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>Int64</returns>
            [DebuggerStepThrough]
            public static Int64 vcvtph_s64_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned integer, rounding toward Plus infinity (vector). This instruction converts a scalar or each element in a vector from a floating-point value to an unsigned integer value using the Round towards Plus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTPU Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>UInt16</returns>
            [DebuggerStepThrough]
            public static UInt16 vcvtph_u16_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned integer, rounding toward Plus infinity (vector). This instruction converts a scalar or each element in a vector from a floating-point value to an unsigned integer value using the Round towards Plus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTPU Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>UInt32</returns>
            [DebuggerStepThrough]
            public static UInt32 vcvtph_u32_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned integer, rounding toward Plus infinity (vector). This instruction converts a scalar or each element in a vector from a floating-point value to an unsigned integer value using the Round towards Plus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTPU Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>UInt64</returns>
            [DebuggerStepThrough]
            public static UInt64 vcvtph_u64_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Negate (vector). This instruction negates the value of each vector element in the source SIMD&amp;FP register, writes the result to a vector, and writes the vector to the destination SIMD&amp;FP register.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FNEG Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vnegh_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Reciprocal Estimate. This instruction finds an approximate reciprocal estimate for each vector element in the source SIMD&amp;FP register, places the result in a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRECPE Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vrecpeh_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Reciprocal exponent (scalar). This instruction finds an approximate reciprocal exponent for each vector element in the source SIMD&amp;FP register, places the result in a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRECPX Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vrecpxh_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral, toward Zero (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the Round towards Zero rounding mode, and writes the result to the SIMD&amp;FP destination register.A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTZ Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vrndh_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral, to nearest with ties to Away (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the Round to Nearest with Ties to Away rounding mode, and writes the result to the SIMD&amp;FP destination register.A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTA Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vrndah_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral, using current rounding mode (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the rounding mode that is determined by the FPCR, and writes the result to the SIMD&amp;FP destination register.A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTI Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vrndih_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral, toward Minus infinity (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the Round towards Minus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTM Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vrndmh_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral, to nearest with ties to even (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the Round to Nearest rounding mode, and writes the result to the SIMD&amp;FP destination register.A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTN Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vrndnh_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral, toward Plus infinity (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the Round towards Plus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTP Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vrndph_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral exact, using current rounding mode (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the rounding mode that is determined by the FPCR, and writes the result to the SIMD&amp;FP destination register.When a result value is not numerically equal to the corresponding input value, an Inexact exception is raised. A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTX Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vrndxh_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Reciprocal Square Root Estimate. This instruction calculates an approximate square root for each vector element in the source SIMD&amp;FP register, places the result in a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRSQRTE Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vrsqrteh_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Square Root (vector). This instruction calculates the square root for each vector element in the source SIMD&amp;FP register, places the result in a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FSQRT Hd,Hn</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vsqrth_f16(f16 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Add (vector). This instruction adds corresponding vector elements in the two source SIMD&amp;FP registers, writes the result into a vector, and writes the vector to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FADD Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vaddh_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Absolute Difference (vector). This instruction subtracts the floating-point values in the elements of the second source SIMD&amp;FP register, from the corresponding floating-point values in the elements of the first source SIMD&amp;FP register, places the absolute value of each result in a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FABD (scalar) Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vabdh_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Absolute Compare Greater than or Equal (vector). This instruction compares the absolute value of each floating-point value in the first source SIMD&amp;FP register with the absolute value of the corresponding floating-point value in the second source SIMD&amp;FP register and if the first value is greater than or equal to the second value sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FACGE Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>UInt16</returns>
            [DebuggerStepThrough]
            public static UInt16 vcageh_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Absolute Compare Greater than (vector). This instruction compares the absolute value of each vector element in the first source SIMD&amp;FP register with the absolute value of the corresponding vector element in the second source SIMD&amp;FP register and if the first value is greater than the second value sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FACGT Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>UInt16</returns>
            [DebuggerStepThrough]
            public static UInt16 vcagth_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Absolute Compare Greater than or Equal (vector). This instruction compares the absolute value of each floating-point value in the first source SIMD&amp;FP register with the absolute value of the corresponding floating-point value in the second source SIMD&amp;FP register and if the first value is greater than or equal to the second value sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FACGE Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>UInt16</returns>
            [DebuggerStepThrough]
            public static UInt16 vcaleh_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Absolute Compare Greater than (vector). This instruction compares the absolute value of each vector element in the first source SIMD&amp;FP register with the absolute value of the corresponding vector element in the second source SIMD&amp;FP register and if the first value is greater than the second value sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FACGT Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>UInt16</returns>
            [DebuggerStepThrough]
            public static UInt16 vcalth_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Equal (vector). This instruction compares each floating-point value from the first source SIMD&amp;FP register, with the corresponding floating-point value from the second source SIMD&amp;FP register, and if the comparison is equal sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMEQ Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>UInt16</returns>
            [DebuggerStepThrough]
            public static UInt16 vceqh_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Greater than or Equal (vector). This instruction reads each floating-point value in the first source SIMD&amp;FP register and if the value is greater than or equal to the corresponding floating-point value in the second source SIMD&amp;FP register sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMGE Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>UInt16</returns>
            [DebuggerStepThrough]
            public static UInt16 vcgeh_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Greater than (vector). This instruction reads each floating-point value in the first source SIMD&amp;FP register and if the value is greater than the corresponding floating-point value in the second source SIMD&amp;FP register sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMGT Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>UInt16</returns>
            [DebuggerStepThrough]
            public static UInt16 vcgth_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Greater than or Equal (vector). This instruction reads each floating-point value in the first source SIMD&amp;FP register and if the value is greater than or equal to the corresponding floating-point value in the second source SIMD&amp;FP register sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMGE Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>UInt16</returns>
            [DebuggerStepThrough]
            public static UInt16 vcleh_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Greater than (vector). This instruction reads each floating-point value in the first source SIMD&amp;FP register and if the value is greater than the corresponding floating-point value in the second source SIMD&amp;FP register sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMGT Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>UInt16</returns>
            [DebuggerStepThrough]
            public static UInt16 vclth_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Signed fixed-point Convert to Floating-point (vector). This instruction converts each element in a vector from fixed-point to floating-point using the rounding mode that is specified by the FPCR, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>SCVTF Hd,Hn,#n</c></summary>
            /// <param name="a0">Int16 a0</param>
            /// <param name="a1">Int32 a1</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vcvth_n_f16_s16(Int16 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Signed fixed-point Convert to Floating-point (vector). This instruction converts each element in a vector from fixed-point to floating-point using the rounding mode that is specified by the FPCR, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>SCVTF Hd,Hn,#n</c></summary>
            /// <param name="a0">Int32 a0</param>
            /// <param name="a1">Int32 a1</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vcvth_n_f16_s32(Int32 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Signed fixed-point Convert to Floating-point (vector). This instruction converts each element in a vector from fixed-point to floating-point using the rounding mode that is specified by the FPCR, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>SCVTF Hd,Hn,#n</c></summary>
            /// <param name="a0">Int64 a0</param>
            /// <param name="a1">Int32 a1</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vcvth_n_f16_s64(Int64 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Unsigned fixed-point Convert to Floating-point (vector). This instruction converts each element in a vector from fixed-point to floating-point using the rounding mode that is specified by the FPCR, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>UCVTF Hd,Hn,#n</c></summary>
            /// <param name="a0">UInt16 a0</param>
            /// <param name="a1">Int32 a1</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vcvth_n_f16_u16(UInt16 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Unsigned fixed-point Convert to Floating-point (vector). This instruction converts each element in a vector from fixed-point to floating-point using the rounding mode that is specified by the FPCR, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>UCVTF Hd,Hn,#n</c></summary>
            /// <param name="a0">UInt32 a0</param>
            /// <param name="a1">Int32 a1</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vcvth_n_f16_u32(UInt32 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Unsigned fixed-point Convert to Floating-point (vector). This instruction converts each element in a vector from fixed-point to floating-point using the rounding mode that is specified by the FPCR, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>UCVTF Hd,Hn,#n</c></summary>
            /// <param name="a0">UInt64 a0</param>
            /// <param name="a1">Int32 a1</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vcvth_n_f16_u64(UInt64 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed fixed-point, rounding toward Zero (vector). This instruction converts a scalar or each element in a vector from floating-point to fixed-point signed integer using the Round towards Zero rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTZS Hd,Hn,#n</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">Int32 a1</param>
            /// <returns>Int16</returns>
            [DebuggerStepThrough]
            public static Int16 vcvth_n_s16_f16(f16 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed fixed-point, rounding toward Zero (vector). This instruction converts a scalar or each element in a vector from floating-point to fixed-point signed integer using the Round towards Zero rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTZS Hd,Hn,#n</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">Int32 a1</param>
            /// <returns>Int32</returns>
            [DebuggerStepThrough]
            public static Int32 vcvth_n_s32_f16(f16 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed fixed-point, rounding toward Zero (vector). This instruction converts a scalar or each element in a vector from floating-point to fixed-point signed integer using the Round towards Zero rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTZS Hd,Hn,#n</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">Int32 a1</param>
            /// <returns>Int64</returns>
            [DebuggerStepThrough]
            public static Int64 vcvth_n_s64_f16(f16 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned fixed-point, rounding toward Zero (vector). This instruction converts a scalar or each element in a vector from floating-point to fixed-point unsigned integer using the Round towards Zero rounding mode, and writes the result to the general-purpose destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTZU Hd,Hn,#n</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">Int32 a1</param>
            /// <returns>UInt16</returns>
            [DebuggerStepThrough]
            public static UInt16 vcvth_n_u16_f16(f16 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned fixed-point, rounding toward Zero (vector). This instruction converts a scalar or each element in a vector from floating-point to fixed-point unsigned integer using the Round towards Zero rounding mode, and writes the result to the general-purpose destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTZU Hd,Hn,#n</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">Int32 a1</param>
            /// <returns>UInt32</returns>
            [DebuggerStepThrough]
            public static UInt32 vcvth_n_u32_f16(f16 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned fixed-point, rounding toward Zero (vector). This instruction converts a scalar or each element in a vector from floating-point to fixed-point unsigned integer using the Round towards Zero rounding mode, and writes the result to the general-purpose destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTZU Hd,Hn,#n</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">Int32 a1</param>
            /// <returns>UInt64</returns>
            [DebuggerStepThrough]
            public static UInt64 vcvth_n_u64_f16(f16 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Divide (vector). This instruction divides the floating-point values in the elements in the first source SIMD&amp;FP register, by the floating-point values in the corresponding elements in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FDIV Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vdivh_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Maximum (vector). This instruction compares corresponding vector elements in the two source SIMD&amp;FP registers, places the larger of each of the two floating-point values into a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMAX Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vmaxh_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Maximum Number (vector). This instruction compares corresponding vector elements in the two source SIMD&amp;FP registers, writes the larger of the two floating-point values into a vector, and writes the vector to the destination SIMD&amp;FP register.NaNs are handled according to the IEEE 754-2008 standard. If one vector element is numeric and the other is a quiet NaN, the result placed in the vector is the numerical value, otherwise the result is identical to FMAX (scalar).This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMAXNM Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vmaxnmh_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point minimum (vector). This instruction compares corresponding elements in the vectors in the two source SIMD&amp;FP registers, places the smaller of each of the two floating-point values into a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMIN Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vminh_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Minimum Number (vector). This instruction compares corresponding vector elements in the two source SIMD&amp;FP registers, writes the smaller of the two floating-point values into a vector, and writes the vector to the destination SIMD&amp;FP register.NaNs are handled according to the IEEE 754-2008 standard. If one vector element is numeric and the other is a quiet NaN, the result placed in the vector is the numerical value, otherwise the result is identical to FMIN (scalar).This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMINNM Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vminnmh_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMUL Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vmulh_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply extended (by element). This instruction multiplies the floating-point values in the vector elements in the first source SIMD&amp;FP register by the specified floating-point value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register.If one value is zero and the other value is infinite, the result is 2.0. In this case, the result is negative if only one of the values is negative, otherwise the result is positive.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMULX Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vmulxh_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Reciprocal Step. This instruction multiplies the corresponding floating-point values in the vectors of the two source SIMD&amp;FP registers, subtracts each of the products from 2.0, places the resulting floating-point values in a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRECPS Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vrecpsh_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Reciprocal Square Root Step. This instruction multiplies corresponding floating-point values in the vectors of the two source SIMD&amp;FP registers, subtracts each of the products from 3.0, divides these results by 2.0, places the results into a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRSQRTS Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vrsqrtsh_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Subtract (vector). This instruction subtracts the elements in the vector in the second source SIMD&amp;FP register, from the corresponding elements in the vector in the first source SIMD&amp;FP register, places each result into elements of a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FSUB Hd,Hn,Hm</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vsubh_f16(f16 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Add (scalar). This instruction multiplies the values of the first two SIMD&amp;FP source registers, adds the product to the value of the third SIMD&amp;FP source register, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMADD Hd,Hn,Hm,Ha</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <param name="a2">f16 a2</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vfmah_f16(f16 a0, f16 a1, f16 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Fused Multiply-Subtract (scalar). This instruction multiplies the values of the first two SIMD&amp;FP source registers, negates the product, adds that to the value of the third SIMD&amp;FP source register, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMSUB Hd,Hn,Hm,Ha</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <param name="a2">f16 a2</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vfmsh_f16(f16 a0, f16 a1, f16 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Absolute value (vector). This instruction calculates the absolute value of each vector element in the source SIMD&amp;FP register, writes the result to a vector, and writes the vector to the destination SIMD&amp;FP register.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FABS Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vabs_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Absolute value (vector). This instruction calculates the absolute value of each vector element in the source SIMD&amp;FP register, writes the result to a vector, and writes the vector to the destination SIMD&amp;FP register.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FABS Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vabsq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Equal (vector). This instruction compares each floating-point value from the first source SIMD&amp;FP register, with the corresponding floating-point value from the second source SIMD&amp;FP register, and if the comparison is equal sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMEQ Vd.4H,Vn.4H,#0</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vceqz_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Equal (vector). This instruction compares each floating-point value from the first source SIMD&amp;FP register, with the corresponding floating-point value from the second source SIMD&amp;FP register, and if the comparison is equal sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMEQ Vd.8H,Vn.8H,#0</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vceqzq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Greater than or Equal (vector). This instruction reads each floating-point value in the first source SIMD&amp;FP register and if the value is greater than or equal to the corresponding floating-point value in the second source SIMD&amp;FP register sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMGE Vd.4H,Vn.4H,#0</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcgez_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Greater than or Equal (vector). This instruction reads each floating-point value in the first source SIMD&amp;FP register and if the value is greater than or equal to the corresponding floating-point value in the second source SIMD&amp;FP register sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMGE Vd.8H,Vn.8H,#0</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcgezq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Greater than (vector). This instruction reads each floating-point value in the first source SIMD&amp;FP register and if the value is greater than the corresponding floating-point value in the second source SIMD&amp;FP register sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMGT Vd.4H,Vn.4H,#0</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcgtz_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Greater than (vector). This instruction reads each floating-point value in the first source SIMD&amp;FP register and if the value is greater than the corresponding floating-point value in the second source SIMD&amp;FP register sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMGT Vd.8H,Vn.8H,#0</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcgtzq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Less than or Equal to zero (vector). This instruction reads each floating-point value in the source SIMD&amp;FP register and if the value is less than or equal to zero sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMLE Vd.4H,Vn.4H,#0</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vclez_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Less than or Equal to zero (vector). This instruction reads each floating-point value in the source SIMD&amp;FP register and if the value is less than or equal to zero sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMLE Vd.8H,Vn.8H,#0</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vclezq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Less than zero (vector). This instruction reads each floating-point value in the source SIMD&amp;FP register and if the value is less than zero sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMLT Vd.4H,Vn.4H,#0</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcltz_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Less than zero (vector). This instruction reads each floating-point value in the source SIMD&amp;FP register and if the value is less than zero sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMLT Vd.8H,Vn.8H,#0</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcltzq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Signed fixed-point Convert to Floating-point (vector). This instruction converts each element in a vector from fixed-point to floating-point using the rounding mode that is specified by the FPCR, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>SCVTF Vd.4H,Vn.4H,#0</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcvt_f16_s16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Signed fixed-point Convert to Floating-point (vector). This instruction converts each element in a vector from fixed-point to floating-point using the rounding mode that is specified by the FPCR, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>SCVTF Vd.8H,Vn.8H,#0</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcvtq_f16_s16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Unsigned fixed-point Convert to Floating-point (vector). This instruction converts each element in a vector from fixed-point to floating-point using the rounding mode that is specified by the FPCR, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>UCVTF Vd.4H,Vn.4H,#0</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcvt_f16_u16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Unsigned fixed-point Convert to Floating-point (vector). This instruction converts each element in a vector from fixed-point to floating-point using the rounding mode that is specified by the FPCR, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>UCVTF Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcvtq_f16_u16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed fixed-point, rounding toward Zero (vector). This instruction converts a scalar or each element in a vector from floating-point to fixed-point signed integer using the Round towards Zero rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTZS Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcvt_s16_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed fixed-point, rounding toward Zero (vector). This instruction converts a scalar or each element in a vector from floating-point to fixed-point signed integer using the Round towards Zero rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTZS Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcvtq_s16_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned fixed-point, rounding toward Zero (vector). This instruction converts a scalar or each element in a vector from floating-point to fixed-point signed integer using the Round towards Zero rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTZU Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcvt_u16_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned fixed-point, rounding toward Zero (vector). This instruction converts a scalar or each element in a vector from floating-point to fixed-point signed integer using the Round towards Zero rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTZU Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcvtq_u16_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed integer, rounding to nearest with ties to Away (vector). This instruction converts each element in a vector from a floating-point value to a signed integer value using the Round to Nearest with Ties to Away rounding mode and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTAS Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcvta_s16_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed integer, rounding to nearest with ties to Away (vector). This instruction converts each element in a vector from a floating-point value to a signed integer value using the Round to Nearest with Ties to Away rounding mode and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTAS Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcvtaq_s16_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned integer, rounding to nearest with ties to Away (vector). This instruction converts each element in a vector from a floating-point value to an unsigned integer value using the Round to Nearest with Ties to Away rounding mode and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTAU Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcvta_u16_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned integer, rounding to nearest with ties to Away (vector). This instruction converts each element in a vector from a floating-point value to an unsigned integer value using the Round to Nearest with Ties to Away rounding mode and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTAU Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcvtaq_u16_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed integer, rounding toward Minus infinity (vector). This instruction converts a scalar or each element in a vector from a floating-point value to a signed integer value using the Round towards Minus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTMS Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcvtm_s16_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed integer, rounding toward Minus infinity (vector). This instruction converts a scalar or each element in a vector from a floating-point value to a signed integer value using the Round towards Minus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTMS Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcvtmq_s16_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned integer, rounding toward Minus infinity (vector). This instruction converts a scalar or each element in a vector from a floating-point value to an unsigned integer value using the Round towards Minus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTMU Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcvtm_u16_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned integer, rounding toward Minus infinity (vector). This instruction converts a scalar or each element in a vector from a floating-point value to an unsigned integer value using the Round towards Minus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTMU Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcvtmq_u16_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed integer, rounding to nearest with ties to even (vector). This instruction converts a scalar or each element in a vector from a floating-point value to a signed integer value using the Round to Nearest rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTNS Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcvtn_s16_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed integer, rounding to nearest with ties to even (vector). This instruction converts a scalar or each element in a vector from a floating-point value to a signed integer value using the Round to Nearest rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTNS Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcvtnq_s16_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned integer, rounding to nearest with ties to even (vector). This instruction converts a scalar or each element in a vector from a floating-point value to an unsigned integer value using the Round to Nearest rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTNU Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcvtn_u16_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned integer, rounding to nearest with ties to even (vector). This instruction converts a scalar or each element in a vector from a floating-point value to an unsigned integer value using the Round to Nearest rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTNU Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcvtnq_u16_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed integer, rounding toward Plus infinity (vector). This instruction converts a scalar or each element in a vector from a floating-point value to a signed integer value using the Round towards Plus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTPS Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcvtp_s16_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed integer, rounding toward Plus infinity (vector). This instruction converts a scalar or each element in a vector from a floating-point value to a signed integer value using the Round towards Plus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTPS Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcvtpq_s16_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned integer, rounding toward Plus infinity (vector). This instruction converts a scalar or each element in a vector from a floating-point value to an unsigned integer value using the Round towards Plus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTPU Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcvtp_u16_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned integer, rounding toward Plus infinity (vector). This instruction converts a scalar or each element in a vector from a floating-point value to an unsigned integer value using the Round towards Plus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTPU Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcvtpq_u16_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Negate (vector). This instruction negates the value of each vector element in the source SIMD&amp;FP register, writes the result to a vector, and writes the vector to the destination SIMD&amp;FP register.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FNEG Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vneg_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Negate (vector). This instruction negates the value of each vector element in the source SIMD&amp;FP register, writes the result to a vector, and writes the vector to the destination SIMD&amp;FP register.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FNEG Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vnegq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Reciprocal Estimate. This instruction finds an approximate reciprocal estimate for each vector element in the source SIMD&amp;FP register, places the result in a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRECPE Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vrecpe_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Reciprocal Estimate. This instruction finds an approximate reciprocal estimate for each vector element in the source SIMD&amp;FP register, places the result in a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRECPE Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vrecpeq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral, toward Zero (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the Round towards Zero rounding mode, and writes the result to the SIMD&amp;FP destination register.A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTZ Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vrnd_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral, toward Zero (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the Round towards Zero rounding mode, and writes the result to the SIMD&amp;FP destination register.A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTZ Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vrndq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral, to nearest with ties to Away (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the Round to Nearest with Ties to Away rounding mode, and writes the result to the SIMD&amp;FP destination register.A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTA Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vrnda_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral, to nearest with ties to Away (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the Round to Nearest with Ties to Away rounding mode, and writes the result to the SIMD&amp;FP destination register.A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTA Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vrndaq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral, using current rounding mode (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the rounding mode that is determined by the FPCR, and writes the result to the SIMD&amp;FP destination register.A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTI Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vrndi_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral, using current rounding mode (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the rounding mode that is determined by the FPCR, and writes the result to the SIMD&amp;FP destination register.A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTI Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vrndiq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral, toward Minus infinity (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the Round towards Minus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTM Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vrndm_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral, toward Minus infinity (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the Round towards Minus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTM Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vrndmq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral, to nearest with ties to even (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the Round to Nearest rounding mode, and writes the result to the SIMD&amp;FP destination register.A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTN Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vrndn_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral, to nearest with ties to even (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the Round to Nearest rounding mode, and writes the result to the SIMD&amp;FP destination register.A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTN Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vrndnq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral, toward Plus infinity (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the Round towards Plus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTP Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vrndp_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral, toward Plus infinity (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the Round towards Plus Infinity rounding mode, and writes the result to the SIMD&amp;FP destination register.A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTP Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vrndpq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral exact, using current rounding mode (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the rounding mode that is determined by the FPCR, and writes the result to the SIMD&amp;FP destination register.When a result value is not numerically equal to the corresponding input value, an Inexact exception is raised. A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTX Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vrndx_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Round to Integral exact, using current rounding mode (vector). This instruction rounds a vector of floating-point values in the SIMD&amp;FP source register to integral floating-point values of the same size using the rounding mode that is determined by the FPCR, and writes the result to the SIMD&amp;FP destination register.When a result value is not numerically equal to the corresponding input value, an Inexact exception is raised. A zero input gives a zero result with the same sign, an infinite input gives an infinite result with the same sign, and a NaN is propagated as for normal arithmetic.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRINTX Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vrndxq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Reciprocal Square Root Estimate. This instruction calculates an approximate square root for each vector element in the source SIMD&amp;FP register, places the result in a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRSQRTE Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vrsqrte_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Reciprocal Square Root Estimate. This instruction calculates an approximate square root for each vector element in the source SIMD&amp;FP register, places the result in a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRSQRTE Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vrsqrteq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Square Root (vector). This instruction calculates the square root for each vector element in the source SIMD&amp;FP register, places the result in a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FSQRT Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vsqrt_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Square Root (vector). This instruction calculates the square root for each vector element in the source SIMD&amp;FP register, places the result in a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FSQRT Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vsqrtq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Add (vector). This instruction adds corresponding vector elements in the two source SIMD&amp;FP registers, writes the result into a vector, and writes the vector to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FADD Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vadd_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Add (vector). This instruction adds corresponding vector elements in the two source SIMD&amp;FP registers, writes the result into a vector, and writes the vector to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FADD Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vaddq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Absolute Difference (vector). This instruction subtracts the floating-point values in the elements of the second source SIMD&amp;FP register, from the corresponding floating-point values in the elements of the first source SIMD&amp;FP register, places the absolute value of each result in a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FABD Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vabd_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Absolute Difference (vector). This instruction subtracts the floating-point values in the elements of the second source SIMD&amp;FP register, from the corresponding floating-point values in the elements of the first source SIMD&amp;FP register, places the absolute value of each result in a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FABD Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vabdq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Absolute Compare Greater than or Equal (vector). This instruction compares the absolute value of each floating-point value in the first source SIMD&amp;FP register with the absolute value of the corresponding floating-point value in the second source SIMD&amp;FP register and if the first value is greater than or equal to the second value sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FACGE Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcage_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Absolute Compare Greater than or Equal (vector). This instruction compares the absolute value of each floating-point value in the first source SIMD&amp;FP register with the absolute value of the corresponding floating-point value in the second source SIMD&amp;FP register and if the first value is greater than or equal to the second value sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FACGE Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcageq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Absolute Compare Greater than (vector). This instruction compares the absolute value of each vector element in the first source SIMD&amp;FP register with the absolute value of the corresponding vector element in the second source SIMD&amp;FP register and if the first value is greater than the second value sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FACGT Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcagt_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Absolute Compare Greater than (vector). This instruction compares the absolute value of each vector element in the first source SIMD&amp;FP register with the absolute value of the corresponding vector element in the second source SIMD&amp;FP register and if the first value is greater than the second value sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FACGT Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcagtq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Absolute Compare Greater than or Equal (vector). This instruction compares the absolute value of each floating-point value in the first source SIMD&amp;FP register with the absolute value of the corresponding floating-point value in the second source SIMD&amp;FP register and if the first value is greater than or equal to the second value sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FACGE Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcale_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Absolute Compare Greater than or Equal (vector). This instruction compares the absolute value of each floating-point value in the first source SIMD&amp;FP register with the absolute value of the corresponding floating-point value in the second source SIMD&amp;FP register and if the first value is greater than or equal to the second value sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FACGE Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcaleq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Absolute Compare Greater than (vector). This instruction compares the absolute value of each vector element in the first source SIMD&amp;FP register with the absolute value of the corresponding vector element in the second source SIMD&amp;FP register and if the first value is greater than the second value sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FACGT Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcalt_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Absolute Compare Greater than (vector). This instruction compares the absolute value of each vector element in the first source SIMD&amp;FP register with the absolute value of the corresponding vector element in the second source SIMD&amp;FP register and if the first value is greater than the second value sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FACGT Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcaltq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Equal (vector). This instruction compares each floating-point value from the first source SIMD&amp;FP register, with the corresponding floating-point value from the second source SIMD&amp;FP register, and if the comparison is equal sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMEQ Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vceq_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Equal (vector). This instruction compares each floating-point value from the first source SIMD&amp;FP register, with the corresponding floating-point value from the second source SIMD&amp;FP register, and if the comparison is equal sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMEQ Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vceqq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Greater than or Equal (vector). This instruction reads each floating-point value in the first source SIMD&amp;FP register and if the value is greater than or equal to the corresponding floating-point value in the second source SIMD&amp;FP register sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMGE Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcge_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Greater than or Equal (vector). This instruction reads each floating-point value in the first source SIMD&amp;FP register and if the value is greater than or equal to the corresponding floating-point value in the second source SIMD&amp;FP register sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMGE Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcgeq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Greater than (vector). This instruction reads each floating-point value in the first source SIMD&amp;FP register and if the value is greater than the corresponding floating-point value in the second source SIMD&amp;FP register sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMGT Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcgt_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Greater than (vector). This instruction reads each floating-point value in the first source SIMD&amp;FP register and if the value is greater than the corresponding floating-point value in the second source SIMD&amp;FP register sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMGT Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcgtq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Greater than or Equal (vector). This instruction reads each floating-point value in the first source SIMD&amp;FP register and if the value is greater than or equal to the corresponding floating-point value in the second source SIMD&amp;FP register sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMGE Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcle_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Greater than or Equal (vector). This instruction reads each floating-point value in the first source SIMD&amp;FP register and if the value is greater than or equal to the corresponding floating-point value in the second source SIMD&amp;FP register sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMGE Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcleq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Greater than (vector). This instruction reads each floating-point value in the first source SIMD&amp;FP register and if the value is greater than the corresponding floating-point value in the second source SIMD&amp;FP register sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMGT Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vclt_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Compare Greater than (vector). This instruction reads each floating-point value in the first source SIMD&amp;FP register and if the value is greater than the corresponding floating-point value in the second source SIMD&amp;FP register sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to one, otherwise sets every bit of the corresponding vector element in the destination SIMD&amp;FP register to zero.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCMGT Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcltq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Signed fixed-point Convert to Floating-point (vector). This instruction converts each element in a vector from fixed-point to floating-point using the rounding mode that is specified by the FPCR, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>SCVTF Vd.4H,Vn.4H,#n</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">Int32 a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcvt_n_f16_s16(v64 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Signed fixed-point Convert to Floating-point (vector). This instruction converts each element in a vector from fixed-point to floating-point using the rounding mode that is specified by the FPCR, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>SCVTF Vd.8H,Vn.8H,#n</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">Int32 a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcvtq_n_f16_s16(v128 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Unsigned fixed-point Convert to Floating-point (vector). This instruction converts each element in a vector from fixed-point to floating-point using the rounding mode that is specified by the FPCR, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>UCVTF Vd.4H,Vn.4H,#n</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">Int32 a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcvt_n_f16_u16(v64 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Unsigned fixed-point Convert to Floating-point (vector). This instruction converts each element in a vector from fixed-point to floating-point using the rounding mode that is specified by the FPCR, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>UCVTF Vd.8H,Vn.8H,#n</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">Int32 a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcvtq_n_f16_u16(v128 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed fixed-point, rounding toward Zero (vector). This instruction converts a scalar or each element in a vector from floating-point to fixed-point signed integer using the Round towards Zero rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTZS Vd.4H,Vn.4H,#n</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">Int32 a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcvt_n_s16_f16(v64 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Signed fixed-point, rounding toward Zero (vector). This instruction converts a scalar or each element in a vector from floating-point to fixed-point signed integer using the Round towards Zero rounding mode, and writes the result to the SIMD&amp;FP destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTZS Vd.8H,Vn.8H,#n</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">Int32 a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcvtq_n_s16_f16(v128 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned fixed-point, rounding toward Zero (vector). This instruction converts a scalar or each element in a vector from floating-point to fixed-point unsigned integer using the Round towards Zero rounding mode, and writes the result to the general-purpose destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTZU Vd.4H,Vn.4H,#n</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">Int32 a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vcvt_n_u16_f16(v64 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Convert to Unsigned fixed-point, rounding toward Zero (vector). This instruction converts a scalar or each element in a vector from floating-point to fixed-point unsigned integer using the Round towards Zero rounding mode, and writes the result to the general-purpose destination register.A floating-point exception can be generated by this instruction. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the Security state and Exception level in which the instruction is executed, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FCVTZU Vd.8H,Vn.8H,#n</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">Int32 a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vcvtq_n_u16_f16(v128 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Divide (vector). This instruction divides the floating-point values in the elements in the first source SIMD&amp;FP register, by the floating-point values in the corresponding elements in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FDIV Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vdiv_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Divide (vector). This instruction divides the floating-point values in the elements in the first source SIMD&amp;FP register, by the floating-point values in the corresponding elements in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FDIV Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vdivq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Maximum (vector). This instruction compares corresponding vector elements in the two source SIMD&amp;FP registers, places the larger of each of the two floating-point values into a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMAX Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vmax_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Maximum (vector). This instruction compares corresponding vector elements in the two source SIMD&amp;FP registers, places the larger of each of the two floating-point values into a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMAX Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vmaxq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Maximum Number (vector). This instruction compares corresponding vector elements in the two source SIMD&amp;FP registers, writes the larger of the two floating-point values into a vector, and writes the vector to the destination SIMD&amp;FP register.NaNs are handled according to the IEEE 754-2008 standard. If one vector element is numeric and the other is a quiet NaN, the result placed in the vector is the numerical value, otherwise the result is identical to FMAX (scalar).This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMAXNM Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vmaxnm_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Maximum Number (vector). This instruction compares corresponding vector elements in the two source SIMD&amp;FP registers, writes the larger of the two floating-point values into a vector, and writes the vector to the destination SIMD&amp;FP register.NaNs are handled according to the IEEE 754-2008 standard. If one vector element is numeric and the other is a quiet NaN, the result placed in the vector is the numerical value, otherwise the result is identical to FMAX (scalar).This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMAXNM Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vmaxnmq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point minimum (vector). This instruction compares corresponding elements in the vectors in the two source SIMD&amp;FP registers, places the smaller of each of the two floating-point values into a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMIN Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vmin_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point minimum (vector). This instruction compares corresponding elements in the vectors in the two source SIMD&amp;FP registers, places the smaller of each of the two floating-point values into a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMIN Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vminq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Minimum Number (vector). This instruction compares corresponding vector elements in the two source SIMD&amp;FP registers, writes the smaller of the two floating-point values into a vector, and writes the vector to the destination SIMD&amp;FP register.NaNs are handled according to the IEEE 754-2008 standard. If one vector element is numeric and the other is a quiet NaN, the result placed in the vector is the numerical value, otherwise the result is identical to FMIN (scalar).This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMINNM Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vminnm_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Minimum Number (vector). This instruction compares corresponding vector elements in the two source SIMD&amp;FP registers, writes the smaller of the two floating-point values into a vector, and writes the vector to the destination SIMD&amp;FP register.NaNs are handled according to the IEEE 754-2008 standard. If one vector element is numeric and the other is a quiet NaN, the result placed in the vector is the numerical value, otherwise the result is identical to FMIN (scalar).This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMINNM Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vminnmq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMUL Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vmul_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMUL Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vmulq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply extended (by element). This instruction multiplies the floating-point values in the vector elements in the first source SIMD&amp;FP register by the specified floating-point value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register.If one value is zero and the other value is infinite, the result is 2.0. In this case, the result is negative if only one of the values is negative, otherwise the result is positive.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMULX Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vmulx_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply extended (by element). This instruction multiplies the floating-point values in the vector elements in the first source SIMD&amp;FP register by the specified floating-point value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register.If one value is zero and the other value is infinite, the result is 2.0. In this case, the result is negative if only one of the values is negative, otherwise the result is positive.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMULX Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vmulxq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Add Pair of elements (scalar). This instruction adds two floating-point vector elements in the source SIMD&amp;FP register and writes the scalar result into the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FADDP Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vpadd_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Add Pair of elements (scalar). This instruction adds two floating-point vector elements in the source SIMD&amp;FP register and writes the scalar result into the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FADDP Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vpaddq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Maximum of Pair of elements (scalar). This instruction compares two vector elements in the source SIMD&amp;FP register and writes the largest of the floating-point values as a scalar to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMAXP Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vpmax_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Maximum of Pair of elements (scalar). This instruction compares two vector elements in the source SIMD&amp;FP register and writes the largest of the floating-point values as a scalar to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMAXP Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vpmaxq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Maximum Number of Pair of elements (scalar). This instruction compares two vector elements in the source SIMD&amp;FP register and writes the largest of the floating-point values as a scalar to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMAXNMP Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vpmaxnm_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Maximum Number of Pair of elements (scalar). This instruction compares two vector elements in the source SIMD&amp;FP register and writes the largest of the floating-point values as a scalar to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMAXNMP Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vpmaxnmq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Minimum of Pair of elements (scalar). This instruction compares two vector elements in the source SIMD&amp;FP register and writes the smallest of the floating-point values as a scalar to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMINP Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vpmin_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Minimum of Pair of elements (scalar). This instruction compares two vector elements in the source SIMD&amp;FP register and writes the smallest of the floating-point values as a scalar to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMINP Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vpminq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Minimum Number of Pair of elements (scalar). This instruction compares two vector elements in the source SIMD&amp;FP register and writes the smallest of the floating-point values as a scalar to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMINNMP Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vpminnm_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Minimum Number of Pair of elements (scalar). This instruction compares two vector elements in the source SIMD&amp;FP register and writes the smallest of the floating-point values as a scalar to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMINNMP Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vpminnmq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Reciprocal Step. This instruction multiplies the corresponding floating-point values in the vectors of the two source SIMD&amp;FP registers, subtracts each of the products from 2.0, places the resulting floating-point values in a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRECPS Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vrecps_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Reciprocal Step. This instruction multiplies the corresponding floating-point values in the vectors of the two source SIMD&amp;FP registers, subtracts each of the products from 2.0, places the resulting floating-point values in a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRECPS Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vrecpsq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Reciprocal Square Root Step. This instruction multiplies corresponding floating-point values in the vectors of the two source SIMD&amp;FP registers, subtracts each of the products from 3.0, divides these results by 2.0, places the results into a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRSQRTS Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vrsqrts_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Reciprocal Square Root Step. This instruction multiplies corresponding floating-point values in the vectors of the two source SIMD&amp;FP registers, subtracts each of the products from 3.0, divides these results by 2.0, places the results into a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FRSQRTS Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vrsqrtsq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Subtract (vector). This instruction subtracts the elements in the vector in the second source SIMD&amp;FP register, from the corresponding elements in the vector in the first source SIMD&amp;FP register, places each result into elements of a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FSUB Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vsub_f16(v64 a0, v64 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Subtract (vector). This instruction subtracts the elements in the vector in the second source SIMD&amp;FP register, from the corresponding elements in the vector in the first source SIMD&amp;FP register, places each result into elements of a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR, or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FSUB Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vsubq_f16(v128 a0, v128 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Add to accumulator (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, and accumulates the results in the vector elements of the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMLA Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <param name="a2">64-bit vector a2</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vfma_f16(v64 a0, v64 a1, v64 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Add to accumulator (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, and accumulates the results in the vector elements of the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMLA Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <param name="a2">128-bit vector a2</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vfmaq_f16(v128 a0, v128 a1, v128 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Subtract from accumulator (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, and subtracts the results from the vector elements of the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMLS Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <param name="a2">64-bit vector a2</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vfms_f16(v64 a0, v64 a1, v64 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Subtract from accumulator (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, and subtracts the results from the vector elements of the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMLS Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <param name="a2">128-bit vector a2</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vfmsq_f16(v128 a0, v128 a1, v128 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Add to accumulator (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, and accumulates the results in the vector elements of the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMLA Vd.4H,Vn.4H,Vm.H[lane]</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <param name="a2">64-bit vector a2</param>
            /// <param name="a3">Lane index to a2. Must be an immediate in the range of [0..3]</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vfma_lane_f16(v64 a0, v64 a1, v64 a2, Int32 a3)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Add to accumulator (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, and accumulates the results in the vector elements of the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMLA Vd.8H,Vn.8H,Vm.H[lane]</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <param name="a2">64-bit vector a2</param>
            /// <param name="a3">Lane index to a2. Must be an immediate in the range of [0..3]</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vfmaq_lane_f16(v128 a0, v128 a1, v64 a2, Int32 a3)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Add to accumulator (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, and accumulates the results in the vector elements of the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMLA Vd.4H,Vn.4H,Vm.H[lane]</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <param name="a2">128-bit vector a2</param>
            /// <param name="a3">Lane index to a2. Must be an immediate in the range of [0..7]</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vfma_laneq_f16(v64 a0, v64 a1, v128 a2, Int32 a3)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Add to accumulator (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, and accumulates the results in the vector elements of the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMLA Vd.8H,Vn.8H,Vm.H[lane]</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <param name="a2">128-bit vector a2</param>
            /// <param name="a3">Lane index to a2. Must be an immediate in the range of [0..7]</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vfmaq_laneq_f16(v128 a0, v128 a1, v128 a2, Int32 a3)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Add to accumulator (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, and accumulates the results in the vector elements of the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMLA Vd.4H,Vn.4H,Vm.H[0]</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <param name="a2">f16 a2</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vfma_n_f16(v64 a0, v64 a1, f16 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Add to accumulator (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, and accumulates the results in the vector elements of the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMLA Vd.8H,Vn.8H,Vm.H[0]</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <param name="a2">f16 a2</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vfmaq_n_f16(v128 a0, v128 a1, f16 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Add to accumulator (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, and accumulates the results in the vector elements of the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMLA Hd,Hn,Vm.H[lane]</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <param name="a2">64-bit vector a2</param>
            /// <param name="a3">Lane index to a2. Must be an immediate in the range of [0..3]</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vfmah_lane_f16(f16 a0, f16 a1, v64 a2, Int32 a3)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Add to accumulator (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, and accumulates the results in the vector elements of the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMLA Hd,Hn,Vm.H[lane]</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <param name="a2">128-bit vector a2</param>
            /// <param name="a3">Lane index to a2. Must be an immediate in the range of [0..7]</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vfmah_laneq_f16(f16 a0, f16 a1, v128 a2, Int32 a3)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Subtract from accumulator (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, and subtracts the results from the vector elements of the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMLS Vd.4H,Vn.4H,Vm.H[lane]</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <param name="a2">64-bit vector a2</param>
            /// <param name="a3">Lane index to a2. Must be an immediate in the range of [0..3]</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vfms_lane_f16(v64 a0, v64 a1, v64 a2, Int32 a3)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Subtract from accumulator (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, and subtracts the results from the vector elements of the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMLS Vd.8H,Vn.8H,Vm.H[lane]</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <param name="a2">64-bit vector a2</param>
            /// <param name="a3">Lane index to a2. Must be an immediate in the range of [0..3]</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vfmsq_lane_f16(v128 a0, v128 a1, v64 a2, Int32 a3)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Subtract from accumulator (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, and subtracts the results from the vector elements of the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMLS Vd.4H,Vn.4H,Vm.H[lane]</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <param name="a2">128-bit vector a2</param>
            /// <param name="a3">Lane index to a2. Must be an immediate in the range of [0..7]</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vfms_laneq_f16(v64 a0, v64 a1, v128 a2, Int32 a3)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Subtract from accumulator (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, and subtracts the results from the vector elements of the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMLS Vd.8H,Vn.8H,Vm.H[lane]</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <param name="a2">128-bit vector a2</param>
            /// <param name="a3">Lane index to a2. Must be an immediate in the range of [0..7]</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vfmsq_laneq_f16(v128 a0, v128 a1, v128 a2, Int32 a3)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Subtract from accumulator (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, and subtracts the results from the vector elements of the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMLS Vd.4H,Vn.4H,Vm.H[0]</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <param name="a2">f16 a2</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vfms_n_f16(v64 a0, v64 a1, f16 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Subtract from accumulator (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, and subtracts the results from the vector elements of the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMLS Vd.8H,Vn.8H,Vm.H[0]</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <param name="a2">f16 a2</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vfmsq_n_f16(v128 a0, v128 a1, f16 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Subtract from accumulator (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, and subtracts the results from the vector elements of the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMLS Hd,Hn,Vm.H[lane]</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <param name="a2">64-bit vector a2</param>
            /// <param name="a3">Lane index to a2. Must be an immediate in the range of [0..3]</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vfmsh_lane_f16(f16 a0, f16 a1, v64 a2, Int32 a3)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point fused Multiply-Subtract from accumulator (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, and subtracts the results from the vector elements of the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMLS Hd,Hn,Vm.H[lane]</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">f16 a1</param>
            /// <param name="a2">128-bit vector a2</param>
            /// <param name="a3">Lane index to a2. Must be an immediate in the range of [0..7]</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vfmsh_laneq_f16(f16 a0, f16 a1, v128 a2, Int32 a3)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMUL Vd.4H,Vn.4H,Vm.H[lane]</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <param name="a2">Lane index to a1. Must be an immediate in the range of [0..3]</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vmul_lane_f16(v64 a0, v64 a1, Int32 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMUL Vd.8H,Vn.8H,Vm.H[lane]</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <param name="a2">Lane index to a1. Must be an immediate in the range of [0..3]</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vmulq_lane_f16(v128 a0, v64 a1, Int32 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMUL Vd.4H,Vn.4H,Vm.H[lane]</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <param name="a2">Lane index to a1. Must be an immediate in the range of [0..7]</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vmul_laneq_f16(v64 a0, v128 a1, Int32 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMUL Vd.8H,Vn.8H,Vm.H[lane]</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <param name="a2">Lane index to a1. Must be an immediate in the range of [0..7]</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vmulq_laneq_f16(v128 a0, v128 a1, Int32 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMUL Vd.4H,Vn.4H,Vm.H[0]</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vmul_n_f16(v64 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMUL Vd.8H,Vn.8H,Vm.H[0]</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vmulq_n_f16(v128 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMUL Hd,Hn,Vm.H[lane]</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <param name="a2">Lane index to a1. Must be an immediate in the range of [0..3]</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vmulh_lane_f16(f16 a0, v64 a1, Int32 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply (by element). This instruction multiplies the vector elements in the first source SIMD&amp;FP register by the specified value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMUL Hd,Hn,Vm.H[lane]</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <param name="a2">Lane index to a1. Must be an immediate in the range of [0..7]</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vmulh_laneq_f16(f16 a0, v128 a1, Int32 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply extended (by element). This instruction multiplies the floating-point values in the vector elements in the first source SIMD&amp;FP register by the specified floating-point value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register.If one value is zero and the other value is infinite, the result is 2.0. In this case, the result is negative if only one of the values is negative, otherwise the result is positive.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMULX Vd.4H,Vn.4H,Vm.H[lane]</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <param name="a2">Lane index to a1. Must be an immediate in the range of [0..3]</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vmulx_lane_f16(v64 a0, v64 a1, Int32 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply extended (by element). This instruction multiplies the floating-point values in the vector elements in the first source SIMD&amp;FP register by the specified floating-point value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register.If one value is zero and the other value is infinite, the result is 2.0. In this case, the result is negative if only one of the values is negative, otherwise the result is positive.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMULX Vd.8H,Vn.8H,Vm.H[lane]</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <param name="a2">Lane index to a1. Must be an immediate in the range of [0..3]</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vmulxq_lane_f16(v128 a0, v64 a1, Int32 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply extended (by element). This instruction multiplies the floating-point values in the vector elements in the first source SIMD&amp;FP register by the specified floating-point value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register.If one value is zero and the other value is infinite, the result is 2.0. In this case, the result is negative if only one of the values is negative, otherwise the result is positive.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMULX Vd.4H,Vn.4H,Vm.H[lane]</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <param name="a2">Lane index to a1. Must be an immediate in the range of [0..7]</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vmulx_laneq_f16(v64 a0, v128 a1, Int32 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply extended (by element). This instruction multiplies the floating-point values in the vector elements in the first source SIMD&amp;FP register by the specified floating-point value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register.If one value is zero and the other value is infinite, the result is 2.0. In this case, the result is negative if only one of the values is negative, otherwise the result is positive.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMULX Vd.8H,Vn.8H,Vm.H[lane]</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <param name="a2">Lane index to a1. Must be an immediate in the range of [0..7]</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vmulxq_laneq_f16(v128 a0, v128 a1, Int32 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply extended (by element). This instruction multiplies the floating-point values in the vector elements in the first source SIMD&amp;FP register by the specified floating-point value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register.If one value is zero and the other value is infinite, the result is 2.0. In this case, the result is negative if only one of the values is negative, otherwise the result is positive.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMULX Vd.4H,Vn.4H,Vm.H[0]</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vmulx_n_f16(v64 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply extended (by element). This instruction multiplies the floating-point values in the vector elements in the first source SIMD&amp;FP register by the specified floating-point value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register.If one value is zero and the other value is infinite, the result is 2.0. In this case, the result is negative if only one of the values is negative, otherwise the result is positive.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMULX Vd.8H,Vn.8H,Vm.H[0]</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">f16 a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vmulxq_n_f16(v128 a0, f16 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply extended (by element). This instruction multiplies the floating-point values in the vector elements in the first source SIMD&amp;FP register by the specified floating-point value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register.If one value is zero and the other value is infinite, the result is 2.0. In this case, the result is negative if only one of the values is negative, otherwise the result is positive.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMULX Hd,Hn,Vm.H[lane]</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <param name="a2">Lane index to a1. Must be an immediate in the range of [0..3]</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vmulxh_lane_f16(f16 a0, v64 a1, Int32 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Multiply extended (by element). This instruction multiplies the floating-point values in the vector elements in the first source SIMD&amp;FP register by the specified floating-point value in the second source SIMD&amp;FP register, places the results in a vector, and writes the vector to the destination SIMD&amp;FP register.If one value is zero and the other value is infinite, the result is 2.0. In this case, the result is negative if only one of the values is negative, otherwise the result is positive.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMULX Hd,Hn,Vm.H[lane]</c></summary>
            /// <param name="a0">f16 a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <param name="a2">Lane index to a1. Must be an immediate in the range of [0..7]</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vmulxh_laneq_f16(f16 a0, v128 a1, Int32 a2)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Maximum across Vector. This instruction compares all the vector elements in the source SIMD&amp;FP register, and writes the largest of the values as a scalar to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMAXV Hd,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vmaxv_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Maximum across Vector. This instruction compares all the vector elements in the source SIMD&amp;FP register, and writes the largest of the values as a scalar to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMAXV Hd,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vmaxvq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Minimum across Vector. This instruction compares all the vector elements in the source SIMD&amp;FP register, and writes the smallest of the values as a scalar to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMINV Hd,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vminv_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Minimum across Vector. This instruction compares all the vector elements in the source SIMD&amp;FP register, and writes the smallest of the values as a scalar to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMINV Hd,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vminvq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Maximum Number across Vector. This instruction compares all the vector elements in the source SIMD&amp;FP register, and writes the largest of the values as a scalar to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.NaNs are handled according to the IEEE 754-2008 standard. If one vector element is numeric and the other is a quiet NaN, the result of the comparison is the numerical value, otherwise the result is identical to FMAX (scalar).This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMAXNMV Hd,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vmaxnmv_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Maximum Number across Vector. This instruction compares all the vector elements in the source SIMD&amp;FP register, and writes the largest of the values as a scalar to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.NaNs are handled according to the IEEE 754-2008 standard. If one vector element is numeric and the other is a quiet NaN, the result of the comparison is the numerical value, otherwise the result is identical to FMAX (scalar).This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMAXNMV Hd,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vmaxnmvq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Minimum Number across Vector. This instruction compares all the vector elements in the source SIMD&amp;FP register, and writes the smallest of the values as a scalar to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.NaNs are handled according to the IEEE 754-2008 standard. If one vector element is numeric and the other is a quiet NaN, the result of the comparison is the numerical value, otherwise the result is identical to FMIN (scalar).This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMINNMV Hd,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vminnmv_f16(v64 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Floating-point Minimum Number across Vector. This instruction compares all the vector elements in the source SIMD&amp;FP register, and writes the smallest of the values as a scalar to the destination SIMD&amp;FP register. All the values in this instruction are floating-point values.NaNs are handled according to the IEEE 754-2008 standard. If one vector element is numeric and the other is a quiet NaN, the result of the comparison is the numerical value, otherwise the result is identical to FMIN (scalar).This instruction can generate a floating-point exception. Depending on the settings in FPCR, the exception results in either a flag being set in FPSR or a synchronous exception being generated. For more information, see Floating-point exception traps.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>FMINNMV Hd,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vminnmvq_f16(v128 a0)
            {
                throw new NotImplementedException();
            }

            /// <summary>Bitwise Select. This instruction sets each bit in the destination SIMD&amp;FP register to the corresponding bit from the first source SIMD&amp;FP register when the original destination bit was 1, otherwise from the second source SIMD&amp;FP register.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>BSL Vd.8B,Vn.8B,Vm.8B</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <param name="a2">64-bit vector a2</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vbsl_f16(v64 a0, v64 a1, v64 a2)
            {
                return vbsl_s8(a0, a1, a2);
            }

            /// <summary>Bitwise Select. This instruction sets each bit in the destination SIMD&amp;FP register to the corresponding bit from the first source SIMD&amp;FP register when the original destination bit was 1, otherwise from the second source SIMD&amp;FP register.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>BSL Vd.16B,Vn.16B,Vm.16B</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <param name="a2">128-bit vector a2</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vbslq_f16(v128 a0, v128 a1, v128 a2)
            {
                return vbslq_s8(a0, a1, a2);
            }

            /// <summary>Duplicate vector element to vector or scalar. This instruction duplicates the vector element at the specified element index in the source SIMD&amp;FP register into a scalar or each element in a vector, and writes the result to the destination SIMD&amp;FP register.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>DUP Vd.4H,Vn.H[lane]</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">Lane index to a0. Must be an immediate in the range of [0..3]</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vdup_lane_f16(v64 a0, Int32 a1)
            {
                return vdup_lane_s16(a0, a1);
            }

            /// <summary>Duplicate vector element to vector or scalar. This instruction duplicates the vector element at the specified element index in the source SIMD&amp;FP register into a scalar or each element in a vector, and writes the result to the destination SIMD&amp;FP register.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>DUP Vd.8H,Vn.H[lane]</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">Lane index to a0. Must be an immediate in the range of [0..3]</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vdupq_lane_f16(v64 a0, Int32 a1)
            {
                return vdupq_lane_s16(a0, a1);
            }

            /// <summary>Extract vector from pair of vectors. This instruction extracts the lowest vector elements from the second source SIMD&amp;FP register and the highest vector elements from the first source SIMD&amp;FP register, concatenates the results into a vector, and writes the vector to the destination SIMD&amp;FP register vector. The index value specifies the lowest vector element to extract from the first source register, and consecutive elements are extracted from the first, then second, source registers until the destination vector is filled.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>EXT Vd.8B,Vn.8B,Vm.8B,#(n&lt;&lt;1)</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <param name="a2">Int32 a2</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vext_f16(v64 a0, v64 a1, Int32 a2)
            {
                return vext_s16(a0, a1, a2);
            }

            /// <summary>Extract vector from pair of vectors. This instruction extracts the lowest vector elements from the second source SIMD&amp;FP register and the highest vector elements from the first source SIMD&amp;FP register, concatenates the results into a vector, and writes the vector to the destination SIMD&amp;FP register vector. The index value specifies the lowest vector element to extract from the first source register, and consecutive elements are extracted from the first, then second, source registers until the destination vector is filled.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>EXT Vd.16B,Vn.16B,Vm.16B,#(n&lt;&lt;1)</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <param name="a2">Int32 a2</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vextq_f16(v128 a0, v128 a1, Int32 a2)
            {
                return vextq_s16(a0, a1, a2);
            }

            /// <summary>Reverse elements in 64-bit doublewords (vector). This instruction reverses the order of 8-bit, 16-bit, or 32-bit elements in each doubleword of the vector in the source SIMD&amp;FP register, places the results into a vector, and writes the vector to the destination SIMD&amp;FP register.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>REV64 Vd.4H,Vn.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vrev64_f16(v64 a0)
            {
                return vrev64_s16(a0);
            }

            /// <summary>Reverse elements in 64-bit doublewords (vector). This instruction reverses the order of 8-bit, 16-bit, or 32-bit elements in each doubleword of the vector in the source SIMD&amp;FP register, places the results into a vector, and writes the vector to the destination SIMD&amp;FP register.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>REV64 Vd.8H,Vn.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vrev64q_f16(v128 a0)
            {
                return vrev64q_s16(a0);
            }

            /// <summary>Zip vectors (primary). This instruction reads adjacent vector elements from the lower half of two source SIMD&amp;FP registers as pairs, interleaves the pairs and places them into a vector, and writes the vector to the destination SIMD&amp;FP register. The first pair from the first source register is placed into the two lowest vector elements, with subsequent pairs taken alternately from each source register.This instruction can be used with ZIP2 to interleave two vectors.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>ZIP1 Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vzip1_f16(v64 a0, v64 a1)
            {
                return vzip1_s16(a0, a1);
            }

            /// <summary>Zip vectors (primary). This instruction reads adjacent vector elements from the lower half of two source SIMD&amp;FP registers as pairs, interleaves the pairs and places them into a vector, and writes the vector to the destination SIMD&amp;FP register. The first pair from the first source register is placed into the two lowest vector elements, with subsequent pairs taken alternately from each source register.This instruction can be used with ZIP2 to interleave two vectors.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>ZIP1 Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vzip1q_f16(v128 a0, v128 a1)
            {
                return vzip1q_s16(a0, a1);
            }

            /// <summary>Zip vectors (secondary). This instruction reads adjacent vector elements from the upper half of two source SIMD&amp;FP registers as pairs, interleaves the pairs and places them into a vector, and writes the vector to the destination SIMD&amp;FP register. The first pair from the first source register is placed into the two lowest vector elements, with subsequent pairs taken alternately from each source register.This instruction can be used with ZIP1 to interleave two vectors.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>ZIP2 Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vzip2_f16(v64 a0, v64 a1)
            {
                return vzip2_s16(a0, a1);
            }

            /// <summary>Zip vectors (secondary). This instruction reads adjacent vector elements from the upper half of two source SIMD&amp;FP registers as pairs, interleaves the pairs and places them into a vector, and writes the vector to the destination SIMD&amp;FP register. The first pair from the first source register is placed into the two lowest vector elements, with subsequent pairs taken alternately from each source register.This instruction can be used with ZIP1 to interleave two vectors.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>ZIP2 Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vzip2q_f16(v128 a0, v128 a1)
            {
                return vzip2q_s16(a0, a1);
            }

            /// <summary>Unzip vectors (primary). This instruction reads corresponding even-numbered vector elements from the two source SIMD&amp;FP registers, starting at zero, places the result from the first source register into consecutive elements in the lower half of a vector, and the result from the second source register into consecutive elements in the upper half of a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can be used with UZP2 to de-interleave two vectors.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>UZP1 Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vuzp1_f16(v64 a0, v64 a1)
            {
                return vuzp1_s16(a0, a1);
            }

            /// <summary>Unzip vectors (primary). This instruction reads corresponding even-numbered vector elements from the two source SIMD&amp;FP registers, starting at zero, places the result from the first source register into consecutive elements in the lower half of a vector, and the result from the second source register into consecutive elements in the upper half of a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can be used with UZP2 to de-interleave two vectors.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>UZP1 Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vuzp1q_f16(v128 a0, v128 a1)
            {
                return vuzp1q_s16(a0, a1);
            }

            /// <summary>Unzip vectors (secondary). This instruction reads corresponding odd-numbered vector elements from the two source SIMD&amp;FP registers, places the result from the first source register into consecutive elements in the lower half of a vector, and the result from the second source register into consecutive elements in the upper half of a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can be used with UZP1 to de-interleave two vectors.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>UZP2 Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vuzp2_f16(v64 a0, v64 a1)
            {
                return vuzp2_s16(a0, a1);
            }

            /// <summary>Unzip vectors (secondary). This instruction reads corresponding odd-numbered vector elements from the two source SIMD&amp;FP registers, places the result from the first source register into consecutive elements in the lower half of a vector, and the result from the second source register into consecutive elements in the upper half of a vector, and writes the vector to the destination SIMD&amp;FP register.This instruction can be used with UZP1 to de-interleave two vectors.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>UZP2 Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vuzp2q_f16(v128 a0, v128 a1)
            {
                return vuzp2q_s16(a0, a1);
            }

            /// <summary>Transpose vectors (primary). This instruction reads corresponding even-numbered vector elements from the two source SIMD&amp;FP registers, starting at zero, places each result into consecutive elements of a vector, and writes the vector to the destination SIMD&amp;FP register. Vector elements from the first source register are placed into even-numbered elements of the destination vector, starting at zero, while vector elements from the second source register are placed into odd-numbered elements of the destination vector.By using this instruction with TRN2, a 2 x 2 matrix can be transposed.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>TRN1 Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vtrn1_f16(v64 a0, v64 a1)
            {
                return vtrn1_s16(a0, a1);
            }

            /// <summary>Transpose vectors (primary). This instruction reads corresponding even-numbered vector elements from the two source SIMD&amp;FP registers, starting at zero, places each result into consecutive elements of a vector, and writes the vector to the destination SIMD&amp;FP register. Vector elements from the first source register are placed into even-numbered elements of the destination vector, starting at zero, while vector elements from the second source register are placed into odd-numbered elements of the destination vector.By using this instruction with TRN2, a 2 x 2 matrix can be transposed.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>TRN1 Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vtrn1q_f16(v128 a0, v128 a1)
            {
                return vtrn1q_s16(a0, a1);
            }

            /// <summary>Transpose vectors (secondary). This instruction reads corresponding odd-numbered vector elements from the two source SIMD&amp;FP registers, places each result into consecutive elements of a vector, and writes the vector to the destination SIMD&amp;FP register. Vector elements from the first source register are placed into even-numbered elements of the destination vector, starting at zero, while vector elements from the second source register are placed into odd-numbered elements of the destination vector.By using this instruction with TRN1, a 2 x 2 matrix can be transposed.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>TRN2 Vd.4H,Vn.4H,Vm.4H</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">64-bit vector a1</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vtrn2_f16(v64 a0, v64 a1)
            {
                return vtrn2_s16(a0, a1);
            }

            /// <summary>Transpose vectors (secondary). This instruction reads corresponding odd-numbered vector elements from the two source SIMD&amp;FP registers, places each result into consecutive elements of a vector, and writes the vector to the destination SIMD&amp;FP register. Vector elements from the first source register are placed into even-numbered elements of the destination vector, starting at zero, while vector elements from the second source register are placed into odd-numbered elements of the destination vector.By using this instruction with TRN1, a 2 x 2 matrix can be transposed.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>TRN2 Vd.8H,Vn.8H,Vm.8H</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">128-bit vector a1</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vtrn2q_f16(v128 a0, v128 a1)
            {
                return vtrn2q_s16(a0, a1);
            }

            /// <summary>Duplicate vector element to vector or scalar. This instruction duplicates the vector element at the specified element index in the source SIMD&amp;FP register into a scalar or each element in a vector, and writes the result to the destination SIMD&amp;FP register.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>DUP Vd.4H,Vn.H[lane]</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">Lane index to a0. Must be an immediate in the range of [0..7]</param>
            /// <returns>64-bit vector</returns>
            [DebuggerStepThrough]
            public static v64 vdup_laneq_f16(v128 a0, Int32 a1)
            {
                return vdup_laneq_s16(a0, a1);
            }

            /// <summary>Duplicate vector element to vector or scalar. This instruction duplicates the vector element at the specified element index in the source SIMD&amp;FP register into a scalar or each element in a vector, and writes the result to the destination SIMD&amp;FP register.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>DUP Vd.8H,Vn.H[lane]</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">Lane index to a0. Must be an immediate in the range of [0..7]</param>
            /// <returns>128-bit vector</returns>
            [DebuggerStepThrough]
            public static v128 vdupq_laneq_f16(v128 a0, Int32 a1)
            {
                return vdupq_laneq_s16(a0, a1);
            }

            /// <summary>Duplicate vector element to vector or scalar. This instruction duplicates the vector element at the specified element index in the source SIMD&amp;FP register into a scalar or each element in a vector, and writes the result to the destination SIMD&amp;FP register.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>DUP Hd,Vn.H[lane]</c></summary>
            /// <param name="a0">64-bit vector a0</param>
            /// <param name="a1">Lane index to a0. Must be an immediate in the range of [0..3]</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vduph_lane_f16(v64 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }

            /// <summary>Duplicate vector element to vector or scalar. This instruction duplicates the vector element at the specified element index in the source SIMD&amp;FP register into a scalar or each element in a vector, and writes the result to the destination SIMD&amp;FP register.Depending on the settings in the CPACR_EL1, CPTR_EL2, and CPTR_EL3 registers, and the current Security state and Exception level, an attempt to execute the instruction might be trapped.
            /// <br/>Equivalent instruction: <c>DUP Hd,Vn.H[lane]</c></summary>
            /// <param name="a0">128-bit vector a0</param>
            /// <param name="a1">Lane index to a0. Must be an immediate in the range of [0..7]</param>
            /// <returns>f16</returns>
            [DebuggerStepThrough]
            public static f16 vduph_laneq_f16(v128 a0, Int32 a1)
            {
                throw new NotImplementedException();
            }
        }
    }
}
#endif // BURST_INTERNAL || UNITY_BURST_EXPERIMENTAL_NEON_INTRINSICS
