{"name": "com.unity.burst", "displayName": "<PERSON><PERSON><PERSON>", "version": "1.8.18", "unity": "2020.3", "description": "Burst is a compiler that translates from IL/.NET bytecode to highly optimized native code using LLVM.", "dependencies": {"com.unity.mathematics": "1.2.1", "com.unity.modules.jsonserialize": "1.0.0"}, "relatedPackages": {"com.unity.burst.tests": "1.8.18"}, "_upm": {"changelog": "### Added\n- Added the `UNITY_BURST_DISABLE_COMPILATION` environment variable as an alternative to the `--burst-disable-compilation` command-line argument\n\n### Removed\n\n### Changed\n\n### Fixed\n- Static fields used in static constructors were sometimes incorrectly set to read only, despite being written.\n- Fixed a case of the editor getting stuck loading during a domain reload if <PERSON><PERSON><PERSON> was set to synchronous compilation\n- Fixed hashing bug that could occur when method signatures differed only by generic parameter count\n- Branches within if (cpufeaturesupported) blocks could cause the transform pass to miss identify which blocks are supporting which features, leading to errors at compile time about intrinsics not being in matching blocks.\n- Fixed 'cannot open input file ucrt.lib' error when building for Universal Windows Platform and targeting SDK 10.0.26100.0\n\n### Known Issues"}, "upmCi": {"footprint": "3f0690746572fe5258ec50be90f8fbb1ce31a2ff"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.burst@1.8/manual/index.html", "repository": {"url": "https://github.cds.internal.unity3d.com/unity/burst.git", "type": "git", "revision": "594a8d8794083be96fa510b2deea0954f971ecc9"}, "_fingerprint": "616862665d8c8ffe643d505aebb5de2ee73b0ab0"}