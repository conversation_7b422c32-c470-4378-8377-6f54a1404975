using UnityEngine;

public class Cursor : Mono<PERSON>eh<PERSON>our
{
    [Header("Cursor Settings")]
    public float rotationSpeed = 90f; // Degrees per second
    public Camera playerCamera;
    
    private void Start()
    {
        // Hide the default cursor
        UnityEngine.Cursor.visible = false;
        
        // If no camera is assigned, try to find the main camera
        if (playerCamera == null)
        {
            playerCamera = Camera.main;
        }
    }

    private void Update()
    {
        FollowMouse();
        RotateConstantly();
    }
    
    private void FollowMouse()
    {
        // Get mouse position in world coordinates
        Vector3 mouseWorldPos = GetMouseWorldPosition();
        
        // Update cursor position to follow mouse
        transform.position = mouseWorldPos;
    }
    
    private void RotateConstantly()
    {
        // Rotate at a constant speed around the Z-axis
        transform.Rotate(0, 0, rotationSpeed * Time.deltaTime);
    }
    
    private Vector3 GetMouseWorldPosition()
    {
        // Get mouse position in screen coordinates
        Vector3 mouseScreenPos = Input.mousePosition;
        
        // Convert to world coordinates
        Vector3 mouseWorldPos = playerCamera.ScreenToWorldPoint(mouseScreenPos);
        mouseWorldPos.z = 0f; // Keep on the same Z plane as the cursor
        
        return mouseWorldPos;
    }
}
