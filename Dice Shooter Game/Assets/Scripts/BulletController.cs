using UnityEngine;

public class BulletController : <PERSON>o<PERSON><PERSON><PERSON><PERSON>
{
    [Head<PERSON>("Bullet Settings")]
    [SerializeField] private Transform player;
    [SerializeField] private Transform cursor;
    [SerializeField] private float rotationRadius = 2f;
    
    [Header("Movement Settings")]
    [SerializeField] private float positionSpeed = 5f;
    [SerializeField] private float rotationSpeed = 5f;
    
    private Vector3 centerPosition;
    private Vector3 targetPosition;
    private Quaternion targetRotation;
    
    private void Awake()
    {
        // Find player if not assigned
        if (player == null)
        {
            player = FindObjectOfType<Player>()?.transform;
        }
        
        // Find cursor if not assigned
        if (cursor == null)
        {
            cursor = FindObjectOfType<Cursor>()?.transform;
        }
    }
    
    private void Update()
    {
        UpdateBulletPosition();
        UpdateBulletRotation();
    }
    
    private void UpdateBulletPosition()
    {
        if (player != null && cursor != null)
        {
            centerPosition = player.position;
            
            // Calculate direction from player to cursor
            Vector3 direction = (cursor.position - centerPosition).normalized;
            
            // Calculate target position at the intersection of the circle and the line
            targetPosition = centerPosition + direction * rotationRadius;
            
            // Smoothly move towards target position
            transform.position = Vector3.Lerp(transform.position, targetPosition, positionSpeed * Time.deltaTime);
        }
    }
    
    private void UpdateBulletRotation()
    {
        if (cursor != null)
        {
            // Calculate direction from bullet to cursor
            Vector3 direction = (cursor.position - transform.position).normalized;
            
            // Calculate target rotation to make bullet's up vector point towards cursor
            targetRotation = Quaternion.LookRotation(Vector3.forward, direction);
            
            // Smoothly rotate towards target rotation
            transform.rotation = Quaternion.Lerp(transform.rotation, targetRotation, rotationSpeed * Time.deltaTime);
        }
    }
    
    // Public methods to control bullet
    public void SetRadius(float radius)
    {
        rotationRadius = radius;
    }
    
    public void SetPositionSpeed(float speed)
    {
        positionSpeed = speed;
    }
    
    public void SetRotationSpeed(float speed)
    {
        rotationSpeed = speed;
    }
}
