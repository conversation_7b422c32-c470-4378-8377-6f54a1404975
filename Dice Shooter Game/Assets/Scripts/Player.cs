using UnityEngine;

public class Player : MonoBehaviour
{
    [Head<PERSON>("Player Settings")]
    [SerializeField] private GameObject cursor;
    [SerializeField] private Map map;
    [SerializeField] private Vector2Int startGridPos = new Vector2Int(5, 5);
    
    [Header("Movement Settings")]
    [SerializeField] private float moveSpeed = 5f;
    [SerializeField] private Action action;
    
    [Header("Shooting Settings")]
    [SerializeField] private GameObject bulletPrefab;
    [SerializeField] private BulletController bulletController;
    
    private Vector2Int currentGridPos;
    private bool isMoving = false;
    private Vector3 targetPosition;
    
    private void Awake() 
    {
        // Find map if not assigned
        if (map == null)
        {
            map = FindObjectOfType<Map>();
        }
        
    }
    
    void Start()
    {
        // Initialize at grid position
        currentGridPos = startGridPos;
        if (map != null)
        {
            transform.position = map.GetWorldPosition(currentGridPos.x, currentGridPos.y);
        }
    }

    void Update()
    {
        HandleMovement();
        HandleShooting();
        MoveToTarget();
    }
    
    private void HandleMovement()
    {
        // Check for left mouse button press for movement
        if (Input.GetMouseButtonDown(0) && !isMoving)
        {
            if (cursor != null && action != null)
            {
                Vector3 direction = CalculateDirectionToCursor();
                Vector2Int moveDirection = GetMoveDirection(direction);
                
                // Create action data and execute action
                ActionData actionData = new ActionData(moveDirection);
                action.SetActionData(actionData);
                action.Execute();
            }
        }
    }
    
    private void HandleShooting()
    {
        // Check for right mouse button press for shooting
        if (Input.GetMouseButtonDown(1))
        {
            Debug.Log("Right mouse button pressed");
            
            if (bulletPrefab == null)
            {
                Debug.LogError("Bullet Prefab is not assigned!");
                return;
            }
            
            if (bulletController == null)
            {
                Debug.LogError("Bullet Controller is not assigned!");
                return;
            }
            
            // Instantiate bullet at bullet controller position and rotation
            GameObject bullet = Instantiate(bulletPrefab, bulletController.transform.position, bulletController.transform.rotation);
            Debug.Log($"Bullet instantiated at {bulletController.transform.position}");
            
            // Get bullet component and shoot
            Bullet bulletScript = bullet.GetComponent<Bullet>();
            if (bulletScript != null)
            {
                bulletScript.Shoot(bulletController.transform.up);
                Debug.Log("Bullet shot!");
            }
            else
            {
                Debug.LogError("Bullet script not found on bullet prefab!");
            }
        }
    }
    
    private Vector3 CalculateDirectionToCursor()
    {
        // Calculate direction from player to cursor
        Vector3 playerPos = transform.position;
        Vector3 cursorPos = cursor.transform.position;
        
        Vector3 direction = (cursorPos - playerPos).normalized;
        return direction;
    }
    
    private Vector2Int GetMoveDirection(Vector3 direction)
    {
        // Calculate angle in degrees (using Y-axis for 2D movement)
        float angle = Mathf.Atan2(direction.y, direction.x) * Mathf.Rad2Deg;
        
        // Normalize angle to 0-360 range
        if (angle < 0) angle += 360f;
        
        // Debug log to see the angle
        Debug.Log($"Direction: {direction}, Angle: {angle}°");
        
        // Determine movement direction based on angle
        // In Unity 2D: Right=0°, Up=90°, Left=180°, Down=270°
        // Up: 45-135 degrees
        // Right: 315-45 degrees (wrapping around 0)
        // Down: 225-315 degrees  
        // Left: 135-225 degrees
        
        if (angle >= 45f && angle < 135f)
        {
            return Vector2Int.up; // Up (positive Y)
        }
        else if (angle >= 315f || angle < 45f)
        {
            return Vector2Int.right; // Right (positive X)
        }
        else if (angle >= 225f && angle < 315f)
        {
            return Vector2Int.down; // Down (negative Y)
        }
        else // angle >= 135f && angle < 225f
        {
            return Vector2Int.left; // Left (negative X)
        }
    }
    
    private bool IsValidGridPosition(Vector2Int gridPos)
    {
        if (map == null) return false;
        
        return gridPos.x >= 0 && gridPos.x < map.Width && 
               gridPos.y >= 0 && gridPos.y < map.Length;
    }
    
    private void MoveToGridPosition(Vector2Int newGridPos)
    {
        currentGridPos = newGridPos;
        targetPosition = map.GetWorldPosition(newGridPos.x, newGridPos.y);
        isMoving = true;
    }
    
    private void MoveToTarget()
    {
        if (isMoving)
        {
            // Move towards target position
            transform.position = Vector3.MoveTowards(transform.position, targetPosition, moveSpeed * Time.deltaTime);
            
            // Check if we've reached the target
            if (Vector3.Distance(transform.position, targetPosition) < 0.1f)
            {
                transform.position = targetPosition;
                isMoving = false;
            }
        }
    }
    
    // Public getter for current grid position
    public Vector2Int CurrentGridPosition => currentGridPos;
    
    // Public method for movement actions
    public void MoveInDirection(Vector2Int direction)
    {
        if (!isMoving)
        {
            Vector2Int newGridPos = currentGridPos + direction;
            if (IsValidGridPosition(newGridPos))
            {
                MoveToGridPosition(newGridPos);
            }
        }
    }
}
