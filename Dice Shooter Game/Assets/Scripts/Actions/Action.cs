using UnityEngine;

[System.Serializable]
public struct ActionData
{
    public Vector2Int direction;
    
    public ActionData(Vector2Int dir)
    {
        direction = dir;
    }
}

public abstract class Action : MonoBehaviour
{
    [HideInInspector] protected ActionData actionData;
    
    public abstract void Execute();
    
    public virtual void SetActionData(ActionData data)
    {
        actionData = data;
    }
}
