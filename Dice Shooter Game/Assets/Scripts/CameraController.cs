using UnityEngine;
using Unity.Cinemachine;

public class CameraController : MonoBeh<PERSON><PERSON>
{
    [Head<PERSON>("Camera Tracking Settings")]
    [SerializeField] private Transform player;
    [SerializeField] private Transform cursor;
    [SerializeField] private Transform cameraTrackingTarget;
    [SerializeField] private float followSpeed = 3f;
    
    [Header("Cinemachine Settings")]
    [SerializeField] private CinemachineCamera virtualCamera;
    
    private void Awake()
    {
        // Find virtual camera if not assigned
        if (virtualCamera == null)
        {
            virtualCamera = GetComponent<CinemachineCamera>();
        }
        
        // Find player if not assigned
        if (player == null)
        {
            player = FindObjectOfType<Player>()?.transform;
        }
        
        // Find cursor if not assigned
        if (cursor == null)
        {
            cursor = FindObjectOfType<Cursor>()?.transform;
        }
    }
    
    private void Start()
    {
        // Set up Cinemachine to follow the tracking target
        if (virtualCamera != null && cameraTrackingTarget != null)
        {
            virtualCamera.Follow = cameraTrackingTarget;
        }
    }
    
    private void Update()
    {
        UpdateCameraTracking();
    }
    
    private void UpdateCameraTracking()
    {
        if (player != null && cursor != null && cameraTrackingTarget != null)
        {
            // Calculate center point between player and cursor
            Vector3 playerPos = player.position;
            Vector3 cursorPos = cursor.position;
            Vector3 centerPoint = (playerPos + cursorPos) * 0.5f;
            
            // Smoothly move the tracking target to the center point
            cameraTrackingTarget.position = Vector3.Lerp(
                cameraTrackingTarget.position, 
                centerPoint, 
                followSpeed * Time.deltaTime
            );
        }
    }
    
    // Public methods for external control
    public void SetPlayer(Transform playerTransform)
    {
        player = playerTransform;
    }
    
    public void SetCursor(Transform cursorTransform)
    {
        cursor = cursorTransform;
    }
    
    public void SetFollowSpeed(float speed)
    {
        followSpeed = speed;
    }
}
