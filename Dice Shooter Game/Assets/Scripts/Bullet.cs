using UnityEngine;

public class Bullet : MonoBehaviour
{
    [<PERSON><PERSON>("Bullet Settings")]
    [SerializeField] private float lifetime = 5f;
    [SerializeField] private float speed = 10f;
    [SerializeField] private LayerMask dummiesLayer = 1 << 8; // Default to layer 8
    
    private float currentLifetime;
    private Vector2 direction;
    private bool isShot = false;
    
    void Update()
    {
        // Only move if bullet has been shot
        if (isShot)
        {
            // Move bullet forward in world space
            transform.position += (Vector3)direction * speed * Time.deltaTime;

            // Decrease lifetime
            currentLifetime -= Time.deltaTime;
            Debug.Log("currentLifetime: " + currentLifetime);

            // Destroy bullet when lifetime expires
            if (currentLifetime <= 0f)
            {
                DestroyBullet();
            }
        }
    }
    
    private void OnTriggerEnter2D(Collider2D other)
    {
        // Check if the collider is on the dummies layer
        if (IsInLayerMask(other.gameObject.layer, dummiesLayer))
        {
            // Handle collision with dummy
            HandleDummyCollision(other);
        }
    }
    
    private bool IsInLayerMask(int layer, LayerMask layerMask)
    {
        return (layerMask.value & (1 << layer)) != 0;
    }
    
    private void HandleDummyCollision(Collider2D dummy)
    {
        DestroyBullet();
    }
    
    private void DestroyBullet()
    {
        Destroy(gameObject);
    }
    
    public void Shoot(Vector2 shootDirection)
    {
        // Activate the bullet and start moving
        gameObject.SetActive(true);
        currentLifetime = lifetime;
        direction = shootDirection.normalized; // Ensure direction is normalized
        isShot = true;
    }
}
