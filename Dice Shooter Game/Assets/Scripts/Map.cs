using UnityEngine;

public class Map : MonoBehaviour
{
    [Header("Map Configuration")]
    [SerializeField] private GameObject mapNodePrefab;
    [SerializeField] private int width = 10;
    [SerializeField] private int length = 10;
    [SerializeField] private float nodeSpacing = 1f;
    
    [Header("Grid Settings")]
    [SerializeField] private Transform gridParent;
    
    private GameObject[,] gridNodes;
    
    void Start()
    {
        GenerateGrid();
    }
    
    void Update()
    {
        
    }
    
    /// <summary>
    /// Generates a grid of map nodes centered at position (0,0)
    /// </summary>
    public void GenerateGrid()
    {
        if (mapNodePrefab == null)
        {
            Debug.LogError("Map Node Prefab is not assigned!");
            return;
        }
        
        // Clear existing grid if it exists
        ClearGrid();
        
        // Create grid array
        gridNodes = new GameObject[width, length];
        
        // Calculate offset to center the grid at (0,0)
        float offsetX = -(width - 1) * nodeSpacing * 0.5f;
        float offsetY = -(length - 1) * nodeSpacing * 0.5f;
        
        // Create grid parent if not assigned
        if (gridParent == null)
        {
            GameObject gridParentObj = new GameObject("Grid");
            gridParent = gridParentObj.transform;
            gridParent.SetParent(transform);
        }
        
        // Generate grid nodes
        for (int x = 0; x < width; x++)
        {
            for (int y = 0; y < length; y++)
            {
                Vector3 position = new Vector3(
                    offsetX + x * nodeSpacing,
                    offsetY + y * nodeSpacing,
                    0f
                );
                
                GameObject node = Instantiate(mapNodePrefab, position, Quaternion.identity, gridParent);
                node.name = $"MapNode_{x}_{y}";
                gridNodes[x, y] = node;
            }
        }
        
        Debug.Log($"Generated grid with {width}x{length} nodes, centered at (0,0)");
    }
    
    /// <summary>
    /// Clears the existing grid
    /// </summary>
    public void ClearGrid()
    {
        if (gridNodes != null)
        {
            for (int x = 0; x < width; x++)
            {
                for (int z = 0; z < length; z++)
                {
                    if (gridNodes[x, z] != null)
                    {
                        DestroyImmediate(gridNodes[x, z]);
                    }
                }
            }
        }
        
        // Clear grid parent children
        if (gridParent != null)
        {
            for (int i = gridParent.childCount - 1; i >= 0; i--)
            {
                DestroyImmediate(gridParent.GetChild(i).gameObject);
            }
        }
    }
    
    /// <summary>
    /// Gets the map node at the specified grid coordinates
    /// </summary>
    /// <param name="x">X coordinate in grid</param>
    /// <param name="y">Y coordinate in grid</param>
    /// <returns>GameObject at the specified position, or null if out of bounds</returns>
    public GameObject GetNodeAt(int x, int y)
    {
        if (x >= 0 && x < width && y >= 0 && y < length)
        {
            return gridNodes[x, y];
        }
        return null;
    }
    
    /// <summary>
    /// Gets the world position for a given grid coordinate
    /// </summary>
    /// <param name="x">X coordinate in grid</param>
    /// <param name="z">Z coordinate in grid</param>
    /// <returns>World position, or Vector3.zero if out of bounds</returns>
    public Vector3 GetWorldPosition(int x, int y)
    {
        if (x >= 0 && x < width && y >= 0 && y < length)
        {
            float offsetX = -(width - 1) * nodeSpacing * 0.5f;
            float offsetY = -(length - 1) * nodeSpacing * 0.5f;
            
            return new Vector3(
                offsetX + x * nodeSpacing,
                offsetY + y * nodeSpacing,
                0f
            );
        }
        return Vector3.zero;
    }
    
    /// <summary>
    /// Converts world position to grid coordinates
    /// </summary>
    /// <param name="worldPos">World position</param>
    /// <returns>Grid coordinates as Vector2Int, or (-1,-1) if out of bounds</returns>
    public Vector2Int WorldToGrid(Vector3 worldPos)
    {
        float offsetX = -(width - 1) * nodeSpacing * 0.5f;
        float offsetY = -(length - 1) * nodeSpacing * 0.5f;
        
        int x = Mathf.RoundToInt((worldPos.x - offsetX) / nodeSpacing);
        int y = Mathf.RoundToInt((worldPos.y - offsetY) / nodeSpacing);
        
        if (x >= 0 && x < width && y >= 0 && y < length)
        {
            return new Vector2Int(x, y);
        }
        
        return new Vector2Int(-1, -1);
    }
    
    // Properties for accessing grid dimensions
    public int Width => width;
    public int Length => length;
    public float NodeSpacing => nodeSpacing;
}
